{% extends "base.html" %}

{% block title %}كل الاجتماعات - نظام إدارة المواعيد{% endblock %}

{% block content %}
<style>
.clickable-card {
    transition: all 0.3s ease;
    border: none !important;
}

.clickable-card:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.clickable-card.active {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

.meeting-item {
    transition: all 0.3s ease;
}

.meeting-item[style*="display: none"] {
    opacity: 0;
    transform: translateY(-10px);
}
</style>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="page-title">
                    <i class="fas fa-calendar-alt me-3"></i>
                    كل الاجتماعات ({{ meetings|length }})
                </h2>
                <div>
                    <a href="{{ url_for('add_meeting') }}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>إضافة اجتماع جديد
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card bg-primary text-white clickable-card" onclick="filterMeetings('all')" style="cursor: pointer;">
                <div class="card-body text-center">
                    <h6>إجمالي الاجتماعات</h6>
                    <h3>{{ total_meetings }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-success text-white clickable-card" onclick="filterMeetings('active')" style="cursor: pointer;">
                <div class="card-body text-center">
                    <h6>الاجتماعات النشطة</h6>
                    <h3>{{ active_meetings }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-info text-white clickable-card" onclick="filterMeetings('finished')" style="cursor: pointer;">
                <div class="card-body text-center">
                    <h6>الاجتماعات المنتهية</h6>
                    <h3>{{ finished_meetings }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-warning text-white clickable-card" onclick="filterMeetings('postponed')" style="cursor: pointer;">
                <div class="card-body text-center">
                    <h6>الاجتماعات المؤجلة</h6>
                    <h3>{{ postponed_meetings }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-danger text-white clickable-card" onclick="filterMeetings('cancelled')" style="cursor: pointer;">
                <div class="card-body text-center">
                    <h6>الاجتماعات الملغية</h6>
                    <h3>{{ cancelled_meetings }}</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة الاجتماعات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">قائمة الاجتماعات</h5>
                </div>
                <div class="card-body">
                    {% if meetings %}
                        {% for meeting in meetings %}
                        <!-- استخدام الحالة المحسوبة من الخادم -->
                        {% set meeting_status = meeting.display_status %}

                        <!-- DEBUG: {{ meeting.subject }} | server_status={{ meeting.display_status }} -->

                        <!-- تشخيص: طباعة معلومات الاجتماع -->
                        <!-- {{ meeting.subject }}: status={{ meeting.status }}, is_cancelled={{ meeting.is_cancelled }}, is_postponed={{ meeting.is_postponed }}, final_status={{ meeting_status }} -->

                        <div class="meeting-item card mb-3"
                             data-status="{{ meeting_status }}"
                             style="display: block !important;">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h5 class="card-title mb-2">
                                            <i class="fas fa-calendar-check me-2 text-primary"></i>
                                            {{ meeting.subject }}
                                        </h5>
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <p class="mb-1">
                                                    <i class="fas fa-calendar text-success me-1"></i>
                                                    <strong>التاريخ:</strong> {{ meeting.meeting_date.strftime('%d-%m-%Y') }}
                                                </p>
                                                <p class="mb-1">
                                                    <i class="fas fa-clock text-warning me-1"></i>
                                                    <strong>الوقت:</strong> {{ meeting.meeting_time | twelve_hour_time }}
                                                </p>
                                            </div>
                                            <div class="col-sm-6">
                                                <p class="mb-1">
                                                    <i class="fas fa-map-marker-alt me-1"></i>
                                                    <strong>مكان الاجتماع:</strong> {{ meeting.location }}
                                                </p>
                                                <p class="mb-1">
                                                    <i class="fas fa-building text-info me-1"></i>
                                                    <strong>جهة الدعوة:</strong> {{ meeting.inviting_party }}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <div class="mb-2">
                                            {% if meeting.is_cancelled %}
                                                <span class="badge bg-danger fs-6 px-3 py-2">
                                                    <i class="fas fa-times-circle me-1"></i>ملغي
                                                </span>
                                            {% elif meeting.is_postponed %}
                                                <span class="badge bg-warning text-dark fs-6 px-3 py-2">
                                                    <i class="fas fa-clock me-1"></i>مؤجل
                                                </span>
                                            {% else %}
                                                <span class="badge bg-success fs-6 px-3 py-2">
                                                    <i class="fas fa-check-circle me-1"></i>نشط
                                                </span>
                                            {% endif %}
                                        </div>
                                        <div class="btn-group-vertical d-grid gap-2">
                                            <button class="btn btn-outline-danger btn-sm" 
                                                    onclick="showMeetingDetails({{ meeting.id }})">
                                                <i class="fas fa-eye me-1" style="color: red;"></i>التفاصيل
                                            </button>
                                            <a href="{{ url_for('edit_meeting', id=meeting.id) }}" class="btn btn-outline-warning btn-sm">
                                                <i class="fas fa-edit me-1"></i>تعديل
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle me-2"></i>
                            لا توجد اجتماعات مسجلة حالياً
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
console.log('🚀 تم تحميل صفحة الاجتماعات البسيطة');
console.log('📊 عدد الاجتماعات:', {{ meetings|length }});

function showMeetingDetails(meetingId) {
    console.log('👁️ عرض تفاصيل الاجتماع:', meetingId);
    alert('عرض تفاصيل الاجتماع رقم: ' + meetingId);
    // يمكن إضافة المزيد من التفاصيل هنا
}

// التأكد من ظهور جميع الاجتماعات
document.addEventListener('DOMContentLoaded', function() {
    const meetings = document.querySelectorAll('.meeting-item');
    console.log('✅ عدد الاجتماعات في DOM:', meetings.length);
    
    meetings.forEach((meeting, index) => {
        meeting.style.display = 'block';
        meeting.style.visibility = 'visible';
        meeting.style.opacity = '1';
        console.log(`✅ اجتماع ${index + 1} مرئي`);
    });
    
    if (meetings.length > 0) {
        console.log('🎉 تم عرض جميع الاجتماعات بنجاح!');

        // تشخيص: طباعة جميع الاجتماعات وحالاتها
        console.log('📋 قائمة جميع الاجتماعات وحالاتها:');
        meetings.forEach((meeting, index) => {
            const status = meeting.getAttribute('data-status');
            const title = meeting.querySelector('.card-title').textContent.trim();
            console.log(`${index + 1}. "${title}" - الحالة: "${status}"`);
        });

        // إحصاء الحالات
        const statusCount = {};
        meetings.forEach(meeting => {
            const status = meeting.getAttribute('data-status');
            statusCount[status] = (statusCount[status] || 0) + 1;
        });
        console.log('📊 إحصاء الحالات في JavaScript:', statusCount);

        // تنبيه مؤقت للتشخيص
        let debugInfo = 'إحصاء الحالات:\n';
        for (let status in statusCount) {
            debugInfo += `${status}: ${statusCount[status]}\n`;
        }
        console.log('🔍 إحصاء الحالات في JavaScript:', debugInfo);

    } else {
        console.log('⚠️ لا توجد اجتماعات للعرض');
    }
});

// دالة فلترة الاجتماعات
function filterMeetings(status) {
    console.log('🔍 فلترة الاجتماعات حسب:', status);

    const meetings = document.querySelectorAll('.meeting-item');
    let visibleCount = 0;

    // إزالة التحديد من جميع البطاقات
    document.querySelectorAll('.clickable-card').forEach(card => {
        card.style.transform = 'scale(1)';
        card.style.boxShadow = '';
    });

    // تحديد البطاقة المنقورة
    event.target.closest('.clickable-card').style.transform = 'scale(1.05)';
    event.target.closest('.clickable-card').style.boxShadow = '0 8px 25px rgba(0,0,0,0.3)';

    console.log(`📋 فحص ${meetings.length} اجتماع للفلترة...`);

    meetings.forEach((meeting, index) => {
        const meetingStatus = meeting.getAttribute('data-status');
        const meetingTitle = meeting.querySelector('.card-title').textContent.trim();

        console.log(`📝 اجتماع ${index + 1}: "${meetingTitle}" - الحالة: "${meetingStatus}"`);

        if (status === 'all' || meetingStatus === status) {
            meeting.style.display = 'block';
            visibleCount++;
            console.log(`✅ عرض اجتماع ${index + 1}`);
        } else {
            meeting.style.display = 'none';
            console.log(`❌ إخفاء اجتماع ${index + 1}`);
        }
    });

    // تحديث عنوان القائمة
    const listTitle = document.querySelector('.card-header h5');
    const statusNames = {
        'all': 'جميع الاجتماعات',
        'active': 'الاجتماعات النشطة',
        'finished': 'الاجتماعات المنتهية',
        'postponed': 'الاجتماعات المؤجلة',
        'cancelled': 'الاجتماعات الملغية'
    };

    listTitle.textContent = `${statusNames[status]} (${visibleCount})`;

    console.log(`✅ تم عرض ${visibleCount} اجتماع من فئة ${statusNames[status]}`);

    // تنبيه مؤقت للتشخيص
    if (status === 'active') {
        alert(`تم فلترة الاجتماعات النشطة: ${visibleCount} اجتماع`);
    }
}

// إضافة تأثيرات بصرية للبطاقات
document.querySelectorAll('.clickable-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        if (this.style.transform !== 'scale(1.05)') {
            this.style.transform = 'scale(1.02)';
            this.style.transition = 'all 0.3s ease';
        }
    });

    card.addEventListener('mouseleave', function() {
        if (this.style.transform !== 'scale(1.05)') {
            this.style.transform = 'scale(1)';
        }
    });
});

// فحص تلقائي لحالة الاجتماعات كل 30 ثانية
let lastUpdateTime = Date.now();

function checkMeetingStatus() {
    const now = new Date();
    const currentTime = now.getHours().toString().padStart(2, '0') + ':' +
                       now.getMinutes().toString().padStart(2, '0');

    console.log(`🔍 فحص تلقائي في ${currentTime}...`);

    // فحص الاجتماعات في الصفحة
    const meetings = document.querySelectorAll('.meeting-item');
    let needsUpdate = false;

    meetings.forEach(meeting => {
        const status = meeting.getAttribute('data-status');
        // إذا كان هناك اجتماع نشط، تحقق من انتهاء وقته
        if (status === 'active') {
            // يمكن إضافة منطق أكثر تعقيداً هنا
            needsUpdate = true;
        }
    });

    // إعادة تحميل فقط إذا مضى وقت كافي منذ آخر تحديث
    if (needsUpdate && (Date.now() - lastUpdateTime) > 30000) {
        console.log('🔄 إعادة تحميل لتحديث الحالات...');
        lastUpdateTime = Date.now();
        location.reload();
    }
}

// فحص كل 30 ثانية
setInterval(checkMeetingStatus, 30000);

console.log('⏰ تم تفعيل الفحص التلقائي كل 30 ثانية');
</script>
{% endblock %}
