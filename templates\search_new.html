{% extends "base.html" %}

{% block title %}البحث في الاجتماعات{% endblock %}

{% block extra_css %}
<style>
    .search-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }
    
    .search-header {
        text-align: center;
        margin-bottom: 3rem;
        padding: 2rem;
        background: linear-gradient(135deg, #1B4332 0%, #2D5A3D 100%);
        color: white;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    }
    
    .search-form {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: #1B4332;
        margin-bottom: 0.5rem;
        display: block;
    }
    
    .form-control {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        border-color: #1B4332;
        box-shadow: 0 0 0 3px rgba(27, 67, 50, 0.1);
        outline: none;
    }

    /* 🔽 أنماط القائمة المنسدلة لجهة الدعوة في البحث */
    #search_value_dropdown {
        position: absolute !important;
        top: 100% !important;
        left: 0 !important;
        right: 0 !important;
        z-index: 1050 !important;
        background: white !important;
        border: 1px solid #dee2e6 !important;
        border-radius: 12px !important;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
        margin-top: 4px !important;
        max-height: 200px !important;
        overflow-y: auto !important;
    }

    #search_value_dropdown .dropdown-item {
        padding: 12px 16px !important;
        border: none !important;
        background: transparent !important;
        color: #333 !important;
        font-size: 1rem !important;
        transition: all 0.2s ease !important;
        cursor: pointer !important;
        border-bottom: 1px solid rgba(0,0,0,0.05) !important;
    }

    #search_value_dropdown .dropdown-item:last-child {
        border-bottom: none !important;
    }

    #search_value_dropdown .dropdown-item:hover {
        background: linear-gradient(135deg, #1B4332 0%, #2D5A3D 100%) !important;
        color: white !important;
        transform: translateX(4px) !important;
    }

    #search_value_dropdown .dropdown-item.text-muted {
        color: #6c757d !important;
        font-style: italic !important;
        cursor: default !important;
    }

    #search_value_dropdown .dropdown-item.text-muted:hover {
        background: transparent !important;
        color: #6c757d !important;
        transform: none !important;
    }

    /* تحسين شريط التمرير للقائمة المنسدلة */
    #search_value_dropdown::-webkit-scrollbar {
        width: 6px !important;
    }

    #search_value_dropdown::-webkit-scrollbar-track {
        background: #f1f1f1 !important;
        border-radius: 6px !important;
    }

    #search_value_dropdown::-webkit-scrollbar-thumb {
        background: #1B4332 !important;
        border-radius: 6px !important;
    }

    #search_value_dropdown::-webkit-scrollbar-thumb:hover {
        background: #2D5A3D !important;
    }
    
    .btn-search {
        background: linear-gradient(135deg, #1B4332 0%, #2D5A3D 100%);
        color: white;
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-search:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(27, 67, 50, 0.3);
        color: white;
    }
    
    .results-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    .results-header {
        background: #f8f9fa;
        padding: 1.5rem 2rem;
        border-bottom: 2px solid #e9ecef;
        font-weight: 600;
        color: #1B4332;
    }
    
    .result-item {
        padding: 2rem;
        border-bottom: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }
    
    .result-item:hover {
        background-color: #f8f9fa;
    }
    
    .result-item:last-child {
        border-bottom: none;
    }
    
    .result-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #1B4332;
        margin-bottom: 1rem;
    }
    
    .result-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }
    
    .detail-item {
        display: flex;
        align-items: center;
        color: #495057;
    }
    
    .detail-item i {
        color: #1B4332;
        margin-left: 0.5rem;
        width: 20px;
    }
    
    .no-results {
        text-align: center;
        padding: 4rem 2rem;
        color: #6c757d;
    }
    
    .no-results i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    .badge {
        padding: 0.4rem 0.8rem;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .badge-success {
        background-color: #28a745;
        color: white;
    }
    
    .badge-danger {
        background-color: #dc3545;
        color: white;
    }
    
    .badge-primary {
        background-color: #1B4332;
        color: white;
    }

    /* أزرار البحث السريع العصرية */
    .quick-search-section {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 40px rgba(0,0,0,0.08);
        border: 1px solid rgba(27, 67, 50, 0.1);
        position: relative;
        overflow: hidden;
    }

    .quick-search-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #1B4332, #2D5A3D, #40916C, #52B788);
        border-radius: 20px 20px 0 0;
    }

    .section-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .section-header h3 {
        color: #1B4332;
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        letter-spacing: 0.5px;
    }

    .section-header p {
        color: #6c757d;
        font-size: 1rem;
        margin: 0;
        opacity: 0.8;
    }

    /* الأزرار الرئيسية للبحث */
    .main-search-buttons {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
        margin-top: 2.5rem;
        padding: 0 1rem;
        max-width: 1200px;
        margin-left: auto;
        margin-right: auto;
    }

    .main-search-card {
        background: white;
        border: 3px solid #e9ecef;
        border-radius: 20px;
        padding: 2rem;
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        gap: 1.5rem;
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 30px rgba(0,0,0,0.08);
        min-height: 120px;
    }

    .main-search-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
        transition: left 0.8s ease;
    }

    .main-search-card:hover::before {
        left: 100%;
    }

    .main-search-card:hover {
        border-color: #1B4332;
        transform: translateY(-12px) scale(1.03);
        box-shadow: 0 25px 80px rgba(27, 67, 50, 0.2);
    }

    .main-search-card:active {
        transform: translateY(-8px) scale(1.01);
        transition: all 0.1s ease;
    }

    .search-card-icon {
        width: 80px;
        height: 80px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        flex-shrink: 0;
        transition: all 0.4s ease;
        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        position: relative;
        z-index: 2;
    }

    .date-icon {
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        box-shadow: 0 8px 25px rgba(255, 215, 0, 0.5);
    }

    .completed-icon {
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        box-shadow: 0 8px 25px rgba(255, 215, 0, 0.5);
    }

    .department-icon {
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        box-shadow: 0 8px 25px rgba(255, 215, 0, 0.5);
    }

    .main-search-card:hover .search-card-icon {
        transform: rotate(8deg) scale(1.15);
        box-shadow: 0 12px 35px rgba(0,0,0,0.3);
    }

    .search-card-content {
        flex: 1;
        text-align: right;
        position: relative;
        z-index: 2;
    }

    .search-card-content h3 {
        color: #1B4332;
        font-size: 1.3rem;
        font-weight: 700;
        margin-bottom: 0.75rem;
        transition: all 0.3s ease;
        letter-spacing: 0.5px;
    }

    .search-card-content p {
        color: #6c757d;
        font-size: 1rem;
        margin: 0;
        opacity: 0.85;
        transition: all 0.3s ease;
        line-height: 1.5;
    }

    .main-search-card:hover .search-card-content h3 {
        color: #2D5A3D;
        transform: translateX(-8px);
    }

    .main-search-card:hover .search-card-content p {
        opacity: 1;
        transform: translateX(-8px);
        color: #495057;
    }

    .search-card-arrow {
        color: #1B4332;
        font-size: 1.5rem;
        transition: all 0.3s ease;
        opacity: 0.7;
        position: relative;
        z-index: 2;
    }

    .main-search-card:hover .search-card-arrow {
        opacity: 1;
        transform: translateX(-12px);
        color: #2D5A3D;
    }

    /* تأثيرات إضافية */
    .main-search-card.processing {
        pointer-events: none;
        opacity: 0.8;
    }

    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    @keyframes buttonPulse {
        0%, 100% {
            box-shadow: 0 8px 30px rgba(0,0,0,0.08);
        }
        50% {
            box-shadow: 0 12px 40px rgba(27, 67, 50, 0.15);
        }
    }

    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
        }
    }

    .main-search-card:focus {
        outline: none;
        animation: buttonPulse 2s infinite;
    }

    /* تحسينات للأجهزة المحمولة */
    @media (max-width: 768px) {
        .quick-search-section {
            padding: 1.5rem;
            border-radius: 16px;
        }

        .main-search-buttons {
            grid-template-columns: 1fr;
            gap: 1.5rem;
            padding: 0 0.5rem;
        }

        .main-search-card {
            padding: 1.5rem;
            border-radius: 16px;
            min-height: 100px;
        }

        .search-card-icon {
            width: 65px;
            height: 65px;
            font-size: 1.7rem;
            border-radius: 16px;
        }

        .search-card-content h3 {
            font-size: 1.2rem;
        }

        .search-card-content p {
            font-size: 0.9rem;
        }

        .section-header h3 {
            font-size: 1.3rem;
        }

        .section-header p {
            font-size: 0.9rem;
        }
    }

    /* تحسينات للشاشات المتوسطة والكبيرة */
    @media (min-width: 769px) {
        .main-search-buttons {
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
        }
    }

    /* تحسينات للشاشات الكبيرة جداً */
    @media (min-width: 1200px) {
        .main-search-buttons {
            grid-template-columns: repeat(2, 1fr);
            gap: 2.5rem;
            max-width: 1000px;
        }
    }

    @media (max-width: 576px) {
        .quick-search-section {
            padding: 1rem;
            border-radius: 12px;
        }

        .main-search-card {
            padding: 1.25rem;
            gap: 1rem;
            border-radius: 12px;
            min-height: 85px;
        }

        .search-card-icon {
            width: 55px;
            height: 55px;
            font-size: 1.5rem;
            border-radius: 12px;
        }

        .search-card-content h3 {
            font-size: 1.1rem;
        }

        .search-card-content p {
            font-size: 0.85rem;
        }

        .search-card-arrow {
            font-size: 1.3rem;
        }

        .section-header {
            margin-bottom: 1.5rem;
        }

        .section-header h3 {
            font-size: 1.2rem;
        }

        .section-header p {
            font-size: 0.85rem;
        }
    }

    /* أزرار التصدير والطباعة العصرية */
    .export-actions-bar {
        background: linear-gradient(135deg, #1B4332 0%, #2D5A3D 100%);
        border-radius: 16px;
        padding: 1.5rem 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(27, 67, 50, 0.2);
        position: relative;
        overflow: hidden;
    }

    .export-actions-bar::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    .export-actions-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        z-index: 2;
    }

    .export-info {
        color: white;
        font-size: 1.1rem;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .export-buttons {
        display: flex;
        gap: 1rem;
    }

    .modern-export-btn {
        background: white;
        border: none;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 600;
        font-size: 0.9rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        position: relative;
        overflow: hidden;
    }

    .modern-export-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        transition: left 0.3s ease;
        z-index: 1;
    }

    .modern-export-btn i,
    .modern-export-btn span {
        position: relative;
        z-index: 2;
    }

    .print-btn {
        color: #495057;
    }

    .print-btn::before {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    }

    .print-btn:hover {
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
    }

    .print-btn:hover::before {
        left: 0;
    }

    .pdf-btn {
        color: #dc3545;
    }

    .pdf-btn::before {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    }

    .pdf-btn:hover {
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
    }

    .pdf-btn:hover::before {
        left: 0;
    }

    .excel-btn {
        color: #28a745;
    }

    .excel-btn::before {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }

    .excel-btn:hover {
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
    }

    .excel-btn:hover::before {
        left: 0;
    }

    /* تم حذف CSS الخاص بالأزرار الفردية حسب الطلب */

    /* تم حذف CSS الخاص بأزرار الإجراءات الفردية حسب الطلب */

    /* تحسينات للأجهزة المحمولة - أزرار التصدير */
    @media (max-width: 768px) {
        .export-actions-content {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }

        .export-buttons {
            justify-content: center;
            flex-wrap: wrap;
            gap: 0.75rem;
        }

        .modern-export-btn {
            padding: 0.625rem 1.25rem;
            font-size: 0.85rem;
        }

        /* تم حذف CSS الخاص بالأزرار الفردية من media query */

        .export-actions-bar {
            padding: 1.25rem 1.5rem;
            border-radius: 12px;
        }
    }

    @media (max-width: 576px) {
        .export-buttons {
            flex-direction: column;
            width: 100%;
        }

        .modern-export-btn {
            width: 100%;
            justify-content: center;
            padding: 0.75rem 1rem;
        }

        /* تم حذف CSS الخاص بالأزرار الفردية من media query الثانية */

        .export-actions-bar {
            padding: 1rem;
        }

        .export-info {
            font-size: 1rem;
        }
    }







    /* حاويات التاريخ المحسنة - حل نهائي */
    .fixed-date-container {
        background: white;
        border: 3px solid #e9ecef;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        text-align: center;
    }

    .fixed-date-container:hover {
        border-color: #1B4332;
        box-shadow: 0 8px 30px rgba(27, 67, 50, 0.15);
        transform: translateY(-3px);
    }

    .date-header-fixed {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
        border-bottom: 2px solid #f8f9fa;
    }

    .date-icon-fixed {
        font-size: 2rem;
        margin-left: 0.75rem;
    }

    .date-text-fixed {
        font-size: 1.2rem;
        font-weight: 700;
        color: #1B4332;
        font-family: 'Arial', 'Helvetica', sans-serif;
        letter-spacing: 2px;
        text-transform: uppercase;
    }

    .fixed-date-input {
        width: 100%;
        padding: 1rem;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        font-size: 1.1rem;
        font-weight: 600;
        color: #1B4332;
        background: #f8f9fa;
        outline: none;
        transition: all 0.3s ease;
        direction: ltr !important;
        text-align: center !important;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    }

    /* تحسين عرض التاريخ بالتنسيق dd/mm/yyyy */
    .fixed-date-input::-webkit-datetime-edit {
        direction: ltr !important;
        text-align: center !important;
        font-weight: 600;
    }

    .fixed-date-input::-webkit-datetime-edit-day-field {
        color: #dc3545;
        font-weight: bold;
        font-size: 1.1em;
    }

    .fixed-date-input::-webkit-datetime-edit-month-field {
        color: #28a745;
        font-weight: bold;
        font-size: 1.1em;
    }

    /* إظهار النص المخصص عندما يكون الحقل فارغاً */
    .fixed-date-input:invalid {
        color: #6c757d;
        position: relative;
    }

    .fixed-date-input:invalid::-webkit-datetime-edit {
        color: transparent;
    }

    .fixed-date-input::before {
        content: attr(placeholder);
        color: #6c757d;
        font-size: 1rem;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .fixed-date-input:invalid::before {
        opacity: 1;
    }

    /* تصميم منتقي التاريخ المخصص */
    .custom-date-picker {
        position: relative;
        width: 100%;
        height: 50px;
        display: block;
        margin: 0;
    }

    .custom-date-input {
        width: 100%;
        height: 50px;
        border: none;
        border-radius: 12px;
        padding: 0 60px 0 15px;
        font-size: 1rem;
        color: transparent;
        background: transparent;
        transition: all 0.3s ease;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 2;
        cursor: pointer;
    }

    .custom-date-input::-webkit-calendar-picker-indicator {
        opacity: 0;
        cursor: pointer;
    }

    /* ضمان استخدام التقويم الميلادي */
    .custom-date-input {
        -webkit-locale: "ar-EG";
        locale: "ar-EG";
    }

    .custom-date-input::-webkit-datetime-edit {
        direction: ltr;
        text-align: center;
    }

    .custom-date-input:focus {
        border-color: #1B4332;
        box-shadow: 0 0 0 0.2rem rgba(27, 67, 50, 0.15);
        outline: none;
    }

    .date-picker-overlay {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 50px;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 0 15px;
        background: white;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        z-index: 1;
    }

    .date-picker-overlay:hover {
        border-color: #2D5A3D;
        box-shadow: 0 2px 8px rgba(27, 67, 50, 0.1);
    }

    .date-picker-text {
        color: #1B4332;
        font-size: 1.1rem;
        font-weight: 600;
        flex: 1;
        text-align: center;
        direction: rtl;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        padding: 0 1rem;
    }

    .date-picker-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #1B4332 0%, #2D5A3D 100%);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        box-shadow: 0 2px 8px rgba(27, 67, 50, 0.2);
        transition: all 0.3s ease;
    }

    .date-picker-overlay:hover .date-picker-icon {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(27, 67, 50, 0.3);
    }

    /* تحسين النص عند اختيار تاريخ */
    .custom-date-input:valid + .date-picker-overlay .date-picker-text {
        color: #1B4332;
        font-weight: 700;
        font-size: 1.2rem;
    }

    /* إصلاح مشكلة التداخل */
    #dateSearchGroup .row {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -0.75rem;
        align-items: flex-start;
    }

    #dateSearchGroup .col-md-6 {
        padding: 0 0.75rem;
        flex: 0 0 50%;
        max-width: 50%;
        box-sizing: border-box;
    }

    #dateSearchGroup .form-group {
        margin-bottom: 1.5rem;
        position: relative;
        width: 100%;
        clear: both;
    }

    #dateSearchGroup .form-label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: #1B4332;
    }

    /* تحسين للأجهزة المحمولة */
    @media (max-width: 768px) {
        .row.g-4 > .col-md-6 {
            flex: 0 0 100%;
            max-width: 100%;
            margin-bottom: 1rem;
        }

        .custom-date-picker {
            margin-bottom: 1rem;
        }

        .date-picker-overlay {
            height: 45px;
        }

        .custom-date-input {
            height: 45px;
        }

        .date-picker-icon {
            width: 35px;
            height: 35px;
            font-size: 1rem;
        }
    }

    .fixed-date-input::-webkit-datetime-edit-year-field {
        color: #1B4332;
        font-weight: bold;
        font-size: 1.1em;
    }

    .fixed-date-input:focus {
        border-color: #1B4332;
        background: white;
        box-shadow: 0 0 0 4px rgba(27, 67, 50, 0.1);
        transform: scale(1.02);
    }

    .fixed-date-input::-webkit-calendar-picker-indicator {
        cursor: pointer;
        opacity: 0.7;
        filter: invert(0.5);
    }

    .fixed-date-input::-webkit-calendar-picker-indicator:hover {
        opacity: 1;
        filter: invert(0.2);
    }

    /* إصلاح عرض التاريخ */
    .fixed-date-input::-webkit-datetime-edit {
        direction: ltr !important;
        text-align: center !important;
    }

    .fixed-date-input::-webkit-datetime-edit-fields-wrapper {
        direction: ltr !important;
    }

    .fixed-date-input::-webkit-datetime-edit-year-field,
    .fixed-date-input::-webkit-datetime-edit-month-field,
    .fixed-date-input::-webkit-datetime-edit-day-field {
        direction: ltr !important;
        text-align: center !important;
    }

    .modern-date-input-container {
        position: relative;
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }

    .modern-date-input-container:hover {
        border-color: #1B4332;
        box-shadow: 0 4px 15px rgba(27, 67, 50, 0.1);
        transform: translateY(-1px);
    }

    .modern-date-input-container:focus-within {
        border-color: #1B4332;
        box-shadow: 0 0 0 4px rgba(27, 67, 50, 0.1);
    }

    .modern-date-input {
        width: 100%;
        height: 56px;
        padding: 1rem 4rem 1rem 1rem;
        border: none;
        background: transparent;
        font-size: 1rem;
        font-weight: 500;
        color: #333;
        direction: ltr;
        text-align: left;
        outline: none;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .modern-date-input::-webkit-calendar-picker-indicator {
        opacity: 0;
        position: absolute;
        right: 0;
        width: 4rem;
        height: 100%;
        cursor: pointer;
        z-index: 10;
    }

    .date-input-overlay {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 3px 12px rgba(40, 167, 69, 0.3);
        z-index: 15;
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .date-input-overlay:hover {
        transform: translateY(-50%) scale(1.15);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
        background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
    }

    .date-input-overlay:active {
        transform: translateY(-50%) scale(1.05);
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.5);
    }

    .modern-date-input:focus + .date-input-overlay {
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        transform: translateY(-50%) scale(1.1);
        animation: pulse-calendar 1s infinite;
    }

    .modern-date-input:valid {
        background: linear-gradient(135deg, rgba(255, 215, 0, 0.05) 0%, rgba(255, 215, 0, 0.02) 100%);
        border-left: 4px solid #FFD700;
    }

    .modern-date-input:valid + .date-input-overlay {
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        animation: success-pulse 2s ease-in-out;
    }

    @keyframes pulse-calendar {
        0%, 100% { box-shadow: 0 3px 12px rgba(255, 215, 0, 0.4); }
        50% { box-shadow: 0 6px 20px rgba(255, 215, 0, 0.7); }
    }

    @keyframes success-pulse {
        0% { transform: translateY(-50%) scale(1); }
        50% { transform: translateY(-50%) scale(1.2); }
        100% { transform: translateY(-50%) scale(1); }
    }





        .btn-icon {
            width: 45px;
            height: 45px;
            font-size: 1.2rem;
            border-radius: 10px;
        }

        .btn-title {
            font-size: 0.95rem;
        }

        .btn-subtitle {
            font-size: 0.75rem;
        }

        .modern-date-input-container {
            border-radius: 10px;
        }

        .modern-date-input {
            height: 52px;
            padding: 0.875rem 3.5rem 0.875rem 0.875rem;
            font-size: 0.95rem;
        }

        .date-input-overlay {
            width: 36px;
            height: 36px;
            font-size: 1rem;
            border-radius: 8px;
        }

        .fixed-date-container {
            padding: 1.25rem;
        }

        .date-icon-fixed {
            font-size: 1.8rem;
            margin-left: 0.5rem;
        }

        .date-text-fixed {
            font-size: 1.1rem;
        }

        .fixed-date-input {
            font-size: 1rem;
            padding: 0.875rem;
        }
    }

    @media (max-width: 576px) {


        .btn-icon {
            width: 40px;
            height: 40px;
            font-size: 1.1rem;
            border-radius: 8px;
            margin-bottom: 0.5rem;
        }

        .btn-text {
            text-align: center;
        }

        .btn-title {
            font-size: 0.9rem;
        }

        .btn-subtitle {
            font-size: 0.7rem;
        }

        .modern-date-input {
            height: 48px;
            padding: 0.75rem 3rem 0.75rem 0.75rem;
            font-size: 0.9rem;
        }

        .date-input-overlay {
            width: 32px;
            height: 32px;
            font-size: 0.9rem;
            border-radius: 6px;
        }

        .fixed-date-container {
            padding: 1rem;
        }

        .date-header-fixed {
            flex-direction: column;
            margin-bottom: 0.75rem;
        }

        .date-icon-fixed {
            font-size: 1.5rem;
            margin-left: 0;
            margin-bottom: 0.5rem;
        }

        .date-text-fixed {
            font-size: 1rem;
        }

        .fixed-date-input {
            font-size: 0.9rem;
            padding: 0.75rem;
        }
    }

    /* تصميم حقول التاريخ */
    .date-input-container {
        position: relative;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 0.75rem;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        margin-bottom: 0.5rem;
    }

    .date-input-container:hover {
        border-color: #1B4332;
        box-shadow: 0 4px 15px rgba(27, 67, 50, 0.1);
        transform: translateY(-1px);
    }

    .date-input-container:focus-within {
        border-color: #1B4332;
        box-shadow: 0 0 0 3px rgba(27, 67, 50, 0.1);
    }

    .professional-date-input {
        border: none !important;
        background: transparent !important;
        padding: 0.5rem 2.5rem 0.5rem 0.5rem !important;
        font-size: 1rem !important;
        font-weight: 500 !important;
        direction: ltr !important;
        text-align: left !important;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        font-variant-numeric: lining-nums !important;
        color: #333 !important;
        cursor: pointer !important;
        width: 100% !important;
        box-shadow: none !important;
        outline: none !important;
    }

    .professional-date-input:focus {
        box-shadow: none !important;
        outline: none !important;
        background: rgba(27, 67, 50, 0.02) !important;
    }

    .date-picker-overlay {
        position: absolute;
        right: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        color: #1B4332;
        font-size: 1.1rem;
        pointer-events: none;
        z-index: 5;
        transition: all 0.3s ease;
    }

    .date-input-container:hover .date-picker-overlay {
        color: #2D5A3D;
        transform: translateY(-50%) scale(1.1);
    }

    .date-input-container:focus-within .date-picker-overlay {
        color: #1B4332;
        transform: translateY(-50%) scale(1.2);
        text-shadow: 0 0 8px rgba(27, 67, 50, 0.3);
    }

    /* إخفاء أيقونة التقويم الافتراضية */
    .professional-date-input::-webkit-calendar-picker-indicator {
        opacity: 0 !important;
        position: absolute !important;
        right: 0 !important;
        width: 2.5rem !important;
        height: 100% !important;
        cursor: pointer !important;
        z-index: 10 !important;
    }

    /* تحسين عرض النص في حقول التاريخ */
    .professional-date-input::-webkit-datetime-edit {
        color: #333 !important;
        opacity: 1 !important;
        visibility: visible !important;
        padding: 2px 0 !important;
        margin: 0 !important;
        line-height: 1.4 !important;
        vertical-align: middle !important;
    }

    .professional-date-input::-webkit-datetime-edit-text,
    .professional-date-input::-webkit-datetime-edit-month-field,
    .professional-date-input::-webkit-datetime-edit-day-field,
    .professional-date-input::-webkit-datetime-edit-year-field {
        color: #333 !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        padding: 1px !important;
        background: transparent !important;
        border: none !important;
        outline: none !important;
    }

    /* تأثيرات خاصة عند اختيار التاريخ */
    .professional-date-input:valid {
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.05) 0%, rgba(40, 167, 69, 0.02) 100%) !important;
    }

    .date-input-container.selected {
        border-color: #28a745 !important;
        box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1) !important;
    }

    .date-input-container.selected .date-picker-overlay {
        color: #28a745 !important;
    }

    /* أزرار التواريخ السريعة */
    .quick-date-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-bottom: 1rem;
        padding: 1rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 10px;
        border: 1px solid #dee2e6;
    }

    .quick-date-buttons .btn {
        border-radius: 8px;
        font-size: 0.85rem;
        padding: 0.4rem 0.8rem;
        font-weight: 500;
        transition: all 0.3s ease;
        border: 2px solid #1B4332;
        color: #1B4332;
        background: white;
    }

    .quick-date-buttons .btn:hover {
        background: #1B4332;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(27, 67, 50, 0.3);
    }

    .quick-date-buttons .btn:active {
        transform: translateY(0);
        box-shadow: 0 2px 6px rgba(27, 67, 50, 0.2);
    }

    @media (max-width: 768px) {
        .quick-date-buttons {
            justify-content: center;
        }

        .quick-date-buttons .btn {
            flex: 1;
            min-width: 120px;
        }
    }

    /* نافذة الحوار المخصصة */
    .custom-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        backdrop-filter: blur(5px);
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .custom-modal-overlay.show {
        opacity: 1;
        visibility: visible;
    }

    .custom-modal {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        max-width: 500px;
        width: 90%;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        transform: scale(0.8) translateY(50px);
        transition: all 0.3s ease;
        direction: rtl;
        text-align: right;
    }

    .custom-modal-overlay.show .custom-modal {
        transform: scale(1) translateY(0);
    }

    .custom-modal-header {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f0f0f0;
    }

    .custom-modal-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #fd7e14, #ffc107);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
        color: white;
        font-size: 1.5rem;
    }

    .custom-modal-title {
        font-size: 1.3rem;
        font-weight: bold;
        color: #1B4332;
        margin: 0;
    }

    .custom-modal-body {
        margin-bottom: 2rem;
    }

    .custom-modal-input {
        width: 100%;
        padding: 1rem;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        font-size: 1rem;
        transition: all 0.3s ease;
        direction: rtl;
        text-align: right;
    }

    .custom-modal-input:focus {
        outline: none;
        border-color: #fd7e14;
        box-shadow: 0 0 0 3px rgba(253, 126, 20, 0.1);
    }

    .custom-modal-buttons {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
    }

    .custom-modal-btn {
        padding: 0.8rem 2rem;
        border: none;
        border-radius: 10px;
        font-size: 1rem;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        min-width: 100px;
    }

    .custom-modal-btn-primary {
        background: linear-gradient(135deg, #1B4332, #2D5A3D);
        color: white;
    }

    .custom-modal-btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(27, 67, 50, 0.3);
    }

    .custom-modal-btn-secondary {
        background: #f8f9fa;
        color: #6c757d;
        border: 2px solid #e9ecef;
    }

    .custom-modal-btn-secondary:hover {
        background: #e9ecef;
        transform: translateY(-2px);
    }
</style>
{% endblock %}

{% block content %}
<div class="search-container">
    <!-- Header -->
    <div class="search-header">
        <h1><i class="fas fa-search me-3"></i>البحث في الاجتماعات</h1>
        <p class="mb-0">ابحث عن الاجتماعات باستخدام معايير مختلفة</p>
    </div>
    
    <!-- Quick Search Buttons -->
    <div class="quick-search-section">
        <div class="section-header">
            <h3><i class="fas fa-bolt me-2"></i>البحث السريع</h3>
            <p>اختر نوع البحث المطلوب بنقرة واحدة</p>
        </div>

        <div class="main-search-buttons">
            <!-- البحث بالتاريخ -->
            <div class="main-search-card" onclick="showDateSearch()" tabindex="0" role="button" aria-label="البحث بالتاريخ">
                <div class="search-card-icon date-icon">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <div class="search-card-content">
                    <h3>البحث بالتاريخ</h3>
                    <p>ابحث في فترة زمنية محددة</p>
                </div>
                <div class="search-card-arrow">
                    <i class="fas fa-chevron-left"></i>
                </div>
            </div>

            <!-- البحث حسب رقم الكتاب -->
            <div class="main-search-card" onclick="showBookNumberSearch()" tabindex="0" role="button" aria-label="البحث حسب رقم الكتاب">
                <div class="search-card-icon" style="background: #17a2b8;">
                    <i class="fas fa-book"></i>
                </div>
                <div class="search-card-content">
                    <h3>البحث حسب رقم الكتاب</h3>
                    <p>البحث باستخدام رقم الكتاب</p>
                </div>
                <div class="search-card-arrow">
                    <i class="fas fa-chevron-left"></i>
                </div>
            </div>

            <!-- البحث حسب الموضوع -->
            <div class="main-search-card" onclick="showSubjectSearch()" tabindex="0" role="button" aria-label="البحث حسب الموضوع">
                <div class="search-card-icon" style="background: #6f42c1;">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="search-card-content">
                    <h3>البحث حسب الموضوع</h3>
                    <p>البحث في موضوع الاجتماع</p>
                </div>
                <div class="search-card-arrow">
                    <i class="fas fa-chevron-left"></i>
                </div>
            </div>

            <!-- البحث حسب جهة الدعوة -->
            <div class="main-search-card" onclick="showInvitingPartySearch()" tabindex="0" role="button" aria-label="البحث حسب جهة الدعوة">
                <div class="search-card-icon" style="background: #fd7e14;">
                    <i class="fas fa-building"></i>
                </div>
                <div class="search-card-content">
                    <h3>البحث حسب جهة الدعوة</h3>
                    <p>البحث باستخدام الجهة الداعية</p>
                </div>
                <div class="search-card-arrow">
                    <i class="fas fa-chevron-left"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Form -->
    <div class="search-form" id="mainSearchForm" style="display: none;">
        <form method="POST" action="/search_new" id="searchForm">
            <!-- حقل مخفي لنوع البحث -->
            <input type="hidden" name="search_type" id="searchType" value="subject">

            <div class="row">
                <div class="col-md-8">
                    <!-- حقل البحث النصي -->
                    <div class="form-group" id="textSearchGroup">
                        <label class="form-label">قيمة البحث</label>
                        <div class="position-relative">
                            <input type="text" name="search_value" class="form-control" id="searchValueInput"
                                   placeholder="أدخل النص للبحث..."
                                   value="{{ search_value or '' }}" autocomplete="off">
                            <div id="search_value_dropdown" class="dropdown-menu w-100" style="display: none; max-height: 200px; overflow-y: auto;">
                                <!-- سيتم ملء القائمة بواسطة JavaScript -->
                            </div>
                            <div class="spinner-border spinner-border-sm position-absolute" id="search_value_loading"
                                 style="display: none; top: 50%; right: 10px; transform: translateY(-50%); width: 1rem; height: 1rem;" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    </div>

                    <!-- حقول البحث بالتاريخ -->
                    <div class="form-group" id="dateSearchGroup" style="display: none;">
                        <label class="form-label">فترة البحث</label>



                        <div class="row g-4">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label" style="display: none;">من تاريخ</label>
                                    <div class="custom-date-picker">
                                        <input type="date" name="start_date" class="custom-date-input"
                                               value="{{ start_date or '' }}" id="startDateInput"
                                               lang="ar-EG" data-calendar="gregorian">
                                        <div class="date-picker-overlay">
                                            <span class="date-picker-text">من تاريخ</span>
                                            <div class="date-picker-icon">
                                                <i class="fas fa-calendar-alt"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label" style="display: none;">إلى تاريخ</label>
                                    <div class="custom-date-picker">
                                        <input type="date" name="end_date" class="custom-date-input"
                                               value="{{ end_date or '' }}" id="endDateInput"
                                               lang="ar-EG" data-calendar="gregorian">
                                        <div class="date-picker-overlay">
                                            <span class="date-picker-text">إلى تاريخ</span>
                                            <div class="date-picker-icon">
                                                <i class="fas fa-calendar-alt"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-search w-100">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <label class="form-label">&nbsp;</label>
                        <button type="button" class="btn btn-outline-secondary w-100" onclick="showQuickButtons()">
                            <i class="fas fa-arrow-right me-2"></i>عودة
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    
    <!-- Results -->
    {% if search_performed %}
    <div class="results-container">
        <div class="results-header">
            <i class="fas fa-list me-2"></i>
            نتائج البحث: {{ results|length }} نتيجة
            {% if search_type and search_value %}
                <small class="text-muted">(البحث في {{ search_type }}: "{{ search_value }}")</small>
            {% endif %}
        </div>
        
        {% if results %}
            <!-- زر تصدير PDF فقط -->
            <div class="export-actions-bar">
                <div class="export-actions-content">
                    <div class="export-info">
                        <i class="fas fa-file-export me-2"></i>
                        <span>تصدير {{ results|length }} نتيجة</span>
                    </div>
                    <div class="export-buttons">
                        <button onclick="printAllResults()"
                           class="modern-export-btn print-btn"
                           style="background: #6c757d; border: none; display: inline-flex; align-items: center; gap: 0.5rem; color: white; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 14px; margin-left: 10px;">
                            <i class="fas fa-print"></i>
                            <span>طباعة الكل</span>
                        </button>
                        <button onclick="exportToPDF()"
                           class="modern-export-btn pdf-btn"
                           style="background: #dc3545; border: none; display: inline-flex; align-items: center; gap: 0.5rem; color: white; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: bold; font-size: 14px;">
                            <i class="fas fa-file-pdf"></i>
                            <span>تصدير PDF</span>
                        </button>
                    </div>
                </div>
            </div>

            {% for meeting in results %}
            <div class="result-item" onclick="window.location.href='{{ url_for('meeting_details', meeting_id=meeting.id) }}'" style="cursor: pointer;">
                <div class="result-title">
                    <i class="fas fa-calendar-check me-2"></i>
                    {{ meeting.subject or 'بدون موضوع' }}
                    <span class="float-end">
                        {% if meeting.is_cancelled %}
                            <span class="badge badge-danger">ملغي</span>
                        {% else %}
                            <span class="badge badge-success">نشط</span>
                        {% endif %}
                        <span class="badge badge-primary">{{ meeting.meeting_type or 'غير محدد' }}</span>
                    </span>
                </div>
                
                <div class="result-details">
                    <div class="detail-item">
                        <i class="fas fa-calendar"></i>
                        <strong>التاريخ:</strong> {{ meeting.meeting_date.strftime('%Y/%m/%d') }}
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-clock"></i>
                        <strong>الوقت:</strong> {{ meeting.meeting_time | twelve_hour_time }}
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <strong>الموقع (GPS):</strong> {{ meeting.location or 'غير محدد' }}
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-building"></i>
                        <strong>جهة الدعوة:</strong> {{ meeting.inviting_party or 'غير محدد' }}
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-book"></i>
                        <strong>رقم الكتاب:</strong> {{ meeting.book_number or 'غير محدد' }}
                    </div>
                    <div class="detail-item">
                        <i class="fas fa-user-tie"></i>
                        <strong>اللباس:</strong> {{ meeting.dress_code or 'غير محدد' }}
                    </div>
                </div>
                
                {% if meeting.notes %}
                <div class="alert alert-info mt-2">
                    <i class="fas fa-sticky-note me-2"></i>
                    <strong>ملاحظات:</strong> {{ meeting.notes }}
                </div>
                {% endif %}

                <!-- أزرار الإجراءات -->
                <div class="text-center mt-3">
                    <a href="{{ url_for('meeting_details', meeting_id=meeting.id) }}"
                       class="btn btn-outline-primary btn-sm me-2">
                        <i class="fas fa-info-circle me-1"></i>
                        عرض التفاصيل الكاملة
                    </a>
                    <button onclick="printSingleMeeting(this); event.stopPropagation();"
                            class="btn btn-outline-info btn-sm">
                        <i class="fas fa-print me-1"></i>
                        طباعة
                    </button>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="no-results">
                <i class="fas fa-search"></i>
                <h4>لا توجد نتائج</h4>
                <p>لم يتم العثور على اجتماعات تطابق معايير البحث المحددة</p>
                <a href="/search_new" class="btn btn-search">
                    <i class="fas fa-redo me-2"></i>بحث جديد
                </a>
            </div>
        {% endif %}
    </div>
    {% endif %}
</div>

<!-- نافذة الحوار المخصصة -->
<div id="customModal" class="custom-modal-overlay">
    <div class="custom-modal">
        <div class="custom-modal-header">
            <div class="custom-modal-icon">
                <i class="fas fa-building"></i>
            </div>
            <h3 class="custom-modal-title">إدخال جهة الدعوة</h3>
        </div>
        <div class="custom-modal-body">
            <p style="color: #6c757d; margin-bottom: 1rem;">يرجى إدخال اسم جهة الدعوة:</p>
            <input type="text" id="customModalInput" class="custom-modal-input" placeholder="مثال: وزارة التعليم">
        </div>
        <div class="custom-modal-buttons">
            <button type="button" class="custom-modal-btn custom-modal-btn-secondary" onclick="closeCustomModal()">
                <i class="fas fa-times me-2"></i>إلغاء
            </button>
            <button type="button" class="custom-modal-btn custom-modal-btn-primary" onclick="confirmCustomModal()">
                <i class="fas fa-check me-2"></i>تأكيد
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('تحميل صفحة البحث الجديدة...');

    // إعداد تبديل نوع البحث
    setupSearchTypeToggle();

    // تحسين النماذج
    setupFormSubmission();

    // تحسين حقول التاريخ الجديدة
    enhanceDateInputs();

    // تأثيرات النتائج
    animateResults();

    // تحديد نوع البحث الحالي عند التحميل
    toggleSearchFields();

    // تحسين أزرار التاريخ السريعة
    enhanceQuickButtons();
});

// تحسين أزرار التاريخ السريعة


// وظائف الطباعة والتصدير
function printAllResults() {
    console.log('🖨️ بدء طباعة جميع النتائج باستخدام القالب الموحد');

    // التحقق من وجود النتائج
    const resultItems = document.querySelectorAll('.result-item');
    if (resultItems.length === 0) {
        showErrorMessage('لا توجد نتائج للطباعة. قم بإجراء بحث أولاً.');
        return;
    }

    // إظهار رسالة تحميل
    showLoadingMessage('جاري تحضير الطباعة...');

    // استخراج بيانات جميع النتائج
    const allMeetings = [];
    resultItems.forEach((item, index) => {
        const meetingData = extractMeetingDataFromSearchResult(item);
        if (meetingData.subject) {
            allMeetings.push({
                ...meetingData,
                index: index + 1
            });
        }
    });

    if (allMeetings.length === 0) {
        hideLoadingMessage();
        showErrorMessage('لم يتم العثور على بيانات صالحة للطباعة');
        return;
    }

    // إنشاء محتوى موحد لجميع النتائج
    const content = [];
    allMeetings.forEach(meeting => {
        content.push(
            { icon: 'fas fa-hashtag', label: `الاجتماع رقم`, value: meeting.index },
            { icon: 'fas fa-file-alt', label: 'الموضوع', value: meeting.subject },
            { icon: 'fas fa-tag', label: 'نوع الفعالية', value: meeting.meeting_type },
            { icon: 'fas fa-calendar', label: 'التاريخ', value: meeting.meeting_date },
            { icon: 'fas fa-clock', label: 'الوقت', value: meeting.meeting_time },
            { icon: 'fas fa-map-marker-alt', label: 'الموقع (GPS)', value: meeting.location },
            { icon: 'fas fa-building', label: 'جهة الدعوة', value: meeting.inviting_party }
        );

        if (meeting.notes) {
            content.push({ icon: 'fas fa-sticky-note', label: 'ملاحظات', value: meeting.notes });
        }

        // فاصل بين الاجتماعات
        if (meeting.index < allMeetings.length) {
            content.push({ icon: 'fas fa-minus', label: '────────────', value: '────────────' });
        }
    });

    // طباعة جميع الاجتماعات في جدول واحد
    if (typeof createUnifiedPrintTemplate === 'function') {
        printAllMeetingsInTable(allMeetings);
        hideLoadingMessage();
    } else {
        // الطريقة القديمة كاحتياطي
        printAllResultsOldWay(resultItems);
    }
}

// طباعة جميع الاجتماعات في جدول واحد
function printAllMeetingsInTable(meetings) {
    console.log('🖨️ طباعة جميع الاجتماعات في جدول واحد:', meetings.length);

    if (!meetings || meetings.length === 0) {
        console.error('❌ لا توجد اجتماعات للطباعة');
        alert('لا توجد اجتماعات للطباعة');
        return;
    }

    // إنشاء محتوى الجدول
    const tableContent = createMeetingsTableHTML(meetings);

    // إنشاء البيانات للقالب الموحد
    const data = {
        title: 'تقرير نتائج البحث',
        subtitle: `${meetings.length} اجتماع - نظام إدارة المواعيد`,
        content: [
            {
                icon: 'fas fa-table',
                label: 'جدول الاجتماعات',
                value: tableContent
            }
        ],
        showBorder: true,
        customStyles: `
            .detail-value table {
                width: 100%;
                border-collapse: collapse;
                margin: 10px 0;
                font-size: 11px;
                direction: rtl;
            }

            .detail-value table th {
                background: #1B4332;
                color: white;
                font-weight: bold;
                padding: 8px 6px;
                text-align: center;
                border: 1px solid #1B4332;
                font-size: 10px;
            }

            .detail-value table td {
                padding: 6px 4px;
                border: 1px solid #ddd;
                text-align: right;
                background: white;
                font-size: 10px;
                line-height: 1.3;
            }

            .detail-value table tr:nth-child(even) td {
                background: #f8f9fa;
            }

            .detail-value table tr:hover td {
                background: #e3f2fd;
            }

            .meeting-row-number {
                background: #1B4332 !important;
                color: white !important;
                text-align: center !important;
                font-weight: bold !important;
            }

            .detail-row {
                border: none !important;
                background: transparent !important;
                padding: 0 !important;
                margin: 0 !important;
            }

            .detail-icon {
                display: none !important;
            }

            .detail-label {
                display: none !important;
            }

            .detail-value {
                width: 100% !important;
                margin: 0 !important;
                padding: 0 !important;
            }
        `
    };

    // استخدام القالب الموحد
    printUnifiedReport(data);
}

// إنشاء HTML للجدول
function createMeetingsTableHTML(meetings) {
    let tableHTML = `
        <table>
            <thead>
                <tr>
                    <th style="width: 5%;">#</th>
                    <th style="width: 20%;">الموضوع</th>
                    <th style="width: 10%;">النوع</th>
                    <th style="width: 10%;">التاريخ</th>
                    <th style="width: 8%;">الوقت</th>
                    <th style="width: 15%;">الموقع (GPS)</th>
                    <th style="width: 15%;">جهة الدعوة</th>
                    <th style="width: 8%;">رقم الكتاب</th>
                    <th style="width: 9%;">تاريخ الكتاب</th>
                </tr>
            </thead>
            <tbody>
    `;

    meetings.forEach((meeting, index) => {
        tableHTML += `
            <tr>
                <td class="meeting-row-number">${index + 1}</td>
                <td><strong>${meeting.subject || 'غير محدد'}</strong></td>
                <td>${meeting.meeting_type || 'غير محدد'}</td>
                <td>${meeting.meeting_date || 'غير محدد'}</td>
                <td>${meeting.meeting_time || 'غير محدد'}</td>
                <td>${meeting.location || 'غير محدد'}</td>
                <td>${meeting.inviting_party || 'غير محدد'}</td>
                <td>${meeting.book_number || 'غير محدد'}</td>
                <td>${meeting.book_date || 'غير محدد'}</td>
            </tr>
        `;

        // إضافة صف للملاحظات إذا وجدت
        if (meeting.notes && meeting.notes.trim()) {
            tableHTML += `
                <tr>
                    <td colspan="9" style="background: #fff3cd; border-top: none; padding: 4px 6px; font-style: italic;">
                        <strong>ملاحظات:</strong> ${meeting.notes}
                    </td>
                </tr>
            `;
        }
    });

    tableHTML += `
            </tbody>
        </table>
    `;

    return tableHTML;
}

// طباعة عدة اجتماعات بالتفصيل الكامل (كل اجتماع منفصل) - الوظيفة القديمة
function printMultipleMeetingsDetailed(meetings) {
    console.log('🖨️ طباعة عدة اجتماعات بالتفصيل الكامل:', meetings.length);

    if (!meetings || meetings.length === 0) {
        console.error('❌ لا توجد اجتماعات للطباعة');
        alert('لا توجد اجتماعات للطباعة');
        return;
    }

    // إنشاء محتوى HTML موحد يحتوي على جميع الاجتماعات
    let allMeetingsHTML = '';

    meetings.forEach((meeting, index) => {
        // إنشاء محتوى كل اجتماع بالتفصيل الكامل
        const meetingContent = [
            { icon: 'fas fa-file-alt', label: 'الموضوع', value: meeting.subject },
            { icon: 'fas fa-tag', label: 'نوع الفعالية', value: meeting.meeting_type },
            { icon: 'fas fa-calendar', label: 'التاريخ', value: meeting.meeting_date },
            { icon: 'fas fa-clock', label: 'الوقت', value: meeting.meeting_time },
            { icon: 'fas fa-map-marker-alt', label: 'الموقع (GPS)', value: meeting.location },
            { icon: 'fas fa-building', label: 'جهة الدعوة', value: meeting.inviting_party },
            { icon: 'fas fa-user-clock', label: 'الحضور قبل الموعد', value: `${meeting.arrival_time_before} دقيقة` },
            { icon: 'fas fa-hashtag', label: 'رقم الكتاب', value: meeting.book_number },
            { icon: 'fas fa-calendar-alt', label: 'تاريخ الكتاب', value: meeting.book_date },
            { icon: 'fas fa-user-tie', label: 'زي الحضور', value: meeting.dress_code }
        ];

        // إضافة الملاحظات إذا وجدت
        if (meeting.notes && meeting.notes.trim()) {
            meetingContent.push({
                icon: 'fas fa-sticky-note',
                label: 'ملاحظات',
                value: meeting.notes
            });
        }

        // إنشاء HTML لكل اجتماع
        const meetingData = {
            title: meeting.subject,
            subtitle: `الاجتماع رقم ${meeting.index} - تفاصيل كاملة`,
            content: meetingContent,
            showBorder: true
        };

        // إنشاء HTML للاجتماع
        const meetingHTML = createUnifiedPrintTemplate(meetingData);

        // إضافة فاصل صفحة بين الاجتماعات (إلا للاجتماع الأخير)
        if (index < meetings.length - 1) {
            allMeetingsHTML += meetingHTML.replace('</body>', '<div style="page-break-after: always;"></div></body>');
        } else {
            allMeetingsHTML += meetingHTML;
        }
    });

    // فتح نافذة طباعة جديدة
    const printWindow = window.open('', '_blank', 'width=800,height=600');

    if (!printWindow) {
        console.error('❌ تعذر فتح نافذة الطباعة');
        alert('تعذر فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.');
        return;
    }

    // كتابة المحتوى
    printWindow.document.write(allMeetingsHTML);
    printWindow.document.close();

    // انتظار تحميل المحتوى ثم الطباعة
    printWindow.onload = function() {
        setTimeout(() => {
            printWindow.focus();
            printWindow.print();

            // إغلاق النافذة بعد الطباعة (اختياري)
            printWindow.onafterprint = function() {
                setTimeout(() => {
                    printWindow.close();
                }, 1000);
            };
        }, 500);
    };

    console.log('✅ تم إنشاء تقرير مفصل لـ', meetings.length, 'اجتماع');
}

// استخراج بيانات الاجتماع من عنصر نتيجة البحث
function extractMeetingDataFromSearchResult(element) {
    console.log('🔍 استخراج بيانات الاجتماع من نتيجة البحث');

    const getTextContent = (selector) => {
        const el = element.querySelector(selector);
        return el ? el.textContent.trim() : '';
    };

    // استخراج الموضوع من .result-title
    let subject = getTextContent('.result-title');
    if (subject) {
        // إزالة الأيقونة والحالة من الموضوع
        subject = subject.replace(/^\s*[\u{1F4C5}\u{1F4C6}]?\s*/u, ''); // إزالة الأيقونات
        subject = subject.replace(/\s*(ملغي|نشط|اجتماع|ورشة عمل|مؤتمر|لقاء|اجتماع عادي|اجتماع طارئ)\s*$/gi, '').trim();
    }
    subject = subject || 'غير محدد';

    // استخراج نوع الاجتماع
    const meetingType = element.querySelector('.badge-primary')?.textContent.trim() || 'اجتماع';

    // استخراج التفاصيل من detail-item
    const detailItems = element.querySelectorAll('.detail-item');
    let meetingDate = '', meetingTime = '', location = '', invitingParty = '', bookNumber = '', dressCode = '';

    detailItems.forEach(item => {
        const text = item.textContent.trim();
        if (text.includes('التاريخ:')) {
            meetingDate = text.replace('التاريخ:', '').trim();
        } else if (text.includes('الوقت:')) {
            meetingTime = text.replace('الوقت:', '').trim();
        } else if (text.includes('المكان:')) {
            location = text.replace('المكان:', '').trim();
        } else if (text.includes('جهة الدعوة:')) {
            invitingParty = text.replace('جهة الدعوة:', '').trim();
        } else if (text.includes('رقم الكتاب:')) {
            bookNumber = text.replace('رقم الكتاب:', '').trim();
        } else if (text.includes('اللباس:')) {
            dressCode = text.replace('اللباس:', '').trim();
        }
    });

    // استخراج الملاحظات من alert-info
    const notesElement = element.querySelector('.alert-info');
    let notes = '';
    if (notesElement) {
        notes = notesElement.textContent.replace('ملاحظات:', '').trim();
    }

    const extractedData = {
        subject: subject,
        meeting_type: meetingType,
        meeting_date: meetingDate,
        meeting_time: meetingTime,
        location: location,
        inviting_party: invitingParty,
        arrival_time_before: '15',
        book_number: bookNumber,
        book_date: '', // لا يظهر في نتائج البحث
        dress_code: dressCode || 'رسمي',
        notes: notes
    };

    console.log('📋 البيانات المستخرجة:', extractedData);
    return extractedData;
}

// طباعة اجتماع واحد من نتائج البحث
function printSingleMeeting(buttonElement) {
    console.log('🖨️ طباعة اجتماع واحد باستخدام القالب الموحد');

    // العثور على عنصر النتيجة الأب
    const resultItem = buttonElement.closest('.result-item');
    if (!resultItem) {
        console.error('❌ لم يتم العثور على عنصر النتيجة');
        alert('خطأ: لم يتم العثور على بيانات الاجتماع');
        return;
    }

    console.log('✅ تم العثور على عنصر النتيجة:', resultItem);

    // استخراج بيانات الاجتماع
    const meetingData = extractMeetingDataFromSearchResult(resultItem);

    if (!meetingData.subject || meetingData.subject === 'غير محدد') {
        console.error('❌ بيانات الاجتماع غير صالحة:', meetingData);
        alert('خطأ: بيانات الاجتماع غير صالحة للطباعة');
        return;
    }

    console.log('✅ بيانات الاجتماع صالحة، بدء الطباعة...');

    // استخدام القالب الموحد
    if (typeof printMeetingReport === 'function') {
        console.log('✅ استدعاء دالة الطباعة الموحدة');
        printMeetingReport(meetingData);
    } else {
        console.error('❌ وظيفة الطباعة الموحدة غير متوفرة');
        console.log('🔍 الوظائف المتاحة:', Object.keys(window).filter(key => key.includes('print')));
        alert('خطأ: وظيفة الطباعة غير متوفرة. يرجى إعادة تحميل الصفحة.');
    }
}

// الطريقة القديمة للطباعة (احتياطي)
function printAllResultsOldWay(resultItems) {
    const printWindow = window.open('', '_blank', 'width=800,height=600');

    const printContent = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تقرير نتائج البحث</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 20px;
                    direction: rtl;
                    line-height: 1.6;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                    padding: 20px;
                    border-bottom: 3px solid #1B4332;
                }
                .header h1 {
                    color: #1B4332;
                    margin-bottom: 10px;
                }
                .result-item {
                    border: 2px solid #e9ecef;
                    margin-bottom: 20px;
                    padding: 20px;
                    border-radius: 10px;
                    page-break-inside: avoid;
                }
                .result-title {
                    font-size: 18px;
                    font-weight: bold;
                    color: #1B4332;
                    margin-bottom: 15px;
                    border-bottom: 1px solid #e9ecef;
                    padding-bottom: 10px;
                }
                .result-details {
                    display: grid;
                    grid-template-columns: repeat(2, 1fr);
                    gap: 10px;
                    margin-bottom: 15px;
                }
                .detail-item {
                    margin: 5px 0;
                    font-size: 14px;
                }
                .badge {
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 12px;
                    font-weight: bold;
                }
                .badge-success { background-color: #28a745; color: white; }
                .badge-danger { background-color: #dc3545; color: white; }
                .badge-primary { background-color: #1B4332; color: white; }
                .individual-actions { display: none; }
                .export-actions-bar { display: none; }
                .footer {
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #e9ecef;
                    font-size: 12px;
                    color: #6c757d;
                }
                @media print {
                    body { margin: 0; }
                    .result-item { break-inside: avoid; }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>تقرير نتائج البحث</h1>
                <p>مديرية الدائرة المالية - القوات المسلحة الأردنية</p>
                <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-JO', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    calendar: 'gregory'
                })}</p>
            </div>
            ${resultsContainer.innerHTML}
            <div class="footer">
                <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الاجتماعات</p>
                <p>© ${new Date().getFullYear()} مديرية الدائرة المالية</p>
            </div>
        </body>
        </html>
    `;

    try {
        printWindow.document.write(printContent);
        printWindow.document.close();

        printWindow.onload = function() {
            hideLoadingMessage();
            showSuccessMessage(`تم فتح نافذة الطباعة بنجاح! (${resultItems.length} اجتماع)`);

            setTimeout(() => {
                printWindow.print();
            }, 500);

            // إغلاق النافذة بعد الطباعة (اختياري)
            setTimeout(() => {
                try {
                    printWindow.close();
                } catch (e) {
                    console.log('تم إغلاق نافذة الطباعة بواسطة المستخدم');
                }
            }, 3000);
        };

        // في حالة فشل التحميل
        printWindow.onerror = function() {
            hideLoadingMessage();
            showErrorMessage('فشل في فتح نافذة الطباعة');
        };

    } catch (error) {
        hideLoadingMessage();
        showErrorMessage('حدث خطأ في الطباعة: ' + error.message);
        console.error('خطأ في الطباعة:', error);
    }
}

// وظيفة تصدير PDF تعمل مع النتائج الفعلية
function exportRealPDF() {
    console.log('🔴 بدء تصدير PDF للنتائج الفعلية');

    try {
        // فحص وجود النتائج في الصفحة
        const resultItems = document.querySelectorAll('.result-item');
        console.log(`📊 عدد النتائج الموجودة: ${resultItems.length}`);

        if (resultItems.length === 0) {
            showErrorMessage('لا توجد نتائج للتصدير. قم بإجراء بحث أولاً.');
            return;
        }

        // إنشاء رابط للتصدير مع معاملات البحث
        const searchParams = new URLSearchParams();
        searchParams.append('search_type', '{{ search_type or "" }}');
        searchParams.append('search_value', '{{ search_value or "" }}');
        searchParams.append('start_date', '{{ start_date or "" }}');
        searchParams.append('end_date', '{{ end_date or "" }}');

        const url = '/export_search_results_pdf?' + searchParams.toString();
        console.log('📤 رابط التصدير:', url);

        // فتح في نافذة جديدة
        window.open(url, '_blank');

        console.log('✅ تم إرسال طلب التصدير');

    } catch (error) {
        console.error('❌ خطأ في تصدير PDF:', error);
        showErrorMessage('حدث خطأ في التصدير: ' + error.message);
    }
}

// وظائف التصدير النهائية والمضمونة
function exportFinalPDF() {
    console.log('🔄 بدء تصدير PDF النهائي...');

    try {
        // جمع البيانات من العناصر المخفية
        const meetingElements = document.querySelectorAll('.meeting-data');
        console.log(`📊 عدد الاجتماعات الموجودة: ${meetingElements.length}`);

        if (meetingElements.length === 0) {
            alert('❌ لا توجد اجتماعات للتصدير. قم بإجراء بحث أولاً.');
            return;
        }

        const meetings = [];
        meetingElements.forEach((element, index) => {
            const meeting = {
                id: element.getAttribute('data-id') || '',
                subject: element.getAttribute('data-subject') || '',
                date: element.getAttribute('data-date') || '',
                time: element.getAttribute('data-time') || '',
                location: element.getAttribute('data-location') || '',
                inviting_party: element.getAttribute('data-inviting-party') || '',
                book_number: element.getAttribute('data-book-number') || '',
                dress_code: element.getAttribute('data-dress-code') || '',
                is_cancelled: element.getAttribute('data-is-cancelled') === 'True',
                is_postponed: element.getAttribute('data-is-postponed') === 'True'
            };
            meetings.push(meeting);
            console.log(`✅ تم جمع بيانات الاجتماع ${index + 1}:`, meeting);
        });

        // إنشاء نموذج وإرسال البيانات
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/final_export_pdf';
        form.style.display = 'none';

        const dataInput = document.createElement('input');
        dataInput.type = 'hidden';
        dataInput.name = 'meetings_data';
        dataInput.value = JSON.stringify(meetings);

        form.appendChild(dataInput);
        document.body.appendChild(form);

        console.log('📤 إرسال البيانات للخادم...');
        form.submit();

        // تنظيف
        setTimeout(() => {
            if (document.body.contains(form)) {
                document.body.removeChild(form);
            }
        }, 2000);

    } catch (error) {
        console.error('❌ خطأ في تصدير PDF:', error);
        alert('❌ حدث خطأ في التصدير: ' + error.message);
    }
}

function exportFinalExcel() {
    console.log('🔄 بدء تصدير Excel النهائي...');

    try {
        // جمع البيانات من العناصر المخفية
        const meetingElements = document.querySelectorAll('.meeting-data');
        console.log(`📊 عدد الاجتماعات الموجودة: ${meetingElements.length}`);

        if (meetingElements.length === 0) {
            alert('❌ لا توجد اجتماعات للتصدير. قم بإجراء بحث أولاً.');
            return;
        }

        const meetings = [];
        meetingElements.forEach((element, index) => {
            const meeting = {
                id: element.getAttribute('data-id') || '',
                subject: element.getAttribute('data-subject') || '',
                date: element.getAttribute('data-date') || '',
                time: element.getAttribute('data-time') || '',
                location: element.getAttribute('data-location') || '',
                inviting_party: element.getAttribute('data-inviting-party') || '',
                book_number: element.getAttribute('data-book-number') || '',
                dress_code: element.getAttribute('data-dress-code') || '',
                is_cancelled: element.getAttribute('data-is-cancelled') === 'True',
                is_postponed: element.getAttribute('data-is-postponed') === 'True'
            };
            meetings.push(meeting);
        });

        // إنشاء نموذج وإرسال البيانات
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/final_export_excel';
        form.style.display = 'none';

        const dataInput = document.createElement('input');
        dataInput.type = 'hidden';
        dataInput.name = 'meetings_data';
        dataInput.value = JSON.stringify(meetings);

        form.appendChild(dataInput);
        document.body.appendChild(form);

        console.log('📤 إرسال البيانات للخادم...');
        form.submit();

        // تنظيف
        setTimeout(() => {
            if (document.body.contains(form)) {
                document.body.removeChild(form);
            }
        }, 2000);

    } catch (error) {
        console.error('❌ خطأ في تصدير Excel:', error);
        alert('❌ حدث خطأ في التصدير: ' + error.message);
    }
}

// وظيفة تصدير PDF للبحث - جديدة ومحسنة
function exportSearchToPDF() {
    console.log('📄 بدء تصدير PDF لنتائج البحث');

    try {
        // جمع البيانات من النتائج المعروضة
        const results = collectSearchResultsFromPage();

        if (!results || results.length === 0) {
            alert('❌ لا توجد نتائج للتصدير');
            return;
        }

        console.log(`📊 تم جمع ${results.length} نتيجة للتصدير`);

        // إنشاء نموذج لإرسال البيانات
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/export_search_pdf';
        form.style.display = 'none';

        // إضافة البيانات
        const dataInput = document.createElement('input');
        dataInput.type = 'hidden';
        dataInput.name = 'results_data';
        dataInput.value = JSON.stringify(results);

        // إضافة معلومات البحث
        const searchInfo = document.createElement('input');
        searchInfo.type = 'hidden';
        searchInfo.name = 'search_info';
        searchInfo.value = JSON.stringify({
            search_type: '{{ search_type or "" }}',
            search_value: '{{ search_value or "" }}',
            start_date: '{{ start_date or "" }}',
            end_date: '{{ end_date or "" }}',
            total_results: results.length
        });

        form.appendChild(dataInput);
        form.appendChild(searchInfo);
        document.body.appendChild(form);

        console.log('📤 إرسال البيانات للخادم...');
        form.submit();

        // تنظيف
        setTimeout(() => {
            if (document.body.contains(form)) {
                document.body.removeChild(form);
            }
        }, 2000);

    } catch (error) {
        console.error('❌ خطأ في تصدير PDF:', error);
        alert('❌ حدث خطأ في التصدير: ' + error.message);
    }
}

// وظيفة تصدير Excel للبحث - جديدة ومحسنة
function exportSearchToExcel() {
    console.log('📊 بدء تصدير Excel لنتائج البحث');

    try {
        // جمع البيانات من النتائج المعروضة
        const results = collectSearchResultsFromPage();

        if (!results || results.length === 0) {
            alert('❌ لا توجد نتائج للتصدير');
            return;
        }

        console.log(`📊 تم جمع ${results.length} نتيجة للتصدير`);

        // إنشاء نموذج لإرسال البيانات
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/export_search_excel';
        form.style.display = 'none';

        // إضافة البيانات
        const dataInput = document.createElement('input');
        dataInput.type = 'hidden';
        dataInput.name = 'results_data';
        dataInput.value = JSON.stringify(results);

        // إضافة معلومات البحث
        const searchInfo = document.createElement('input');
        searchInfo.type = 'hidden';
        searchInfo.name = 'search_info';
        searchInfo.value = JSON.stringify({
            search_type: '{{ search_type or "" }}',
            search_value: '{{ search_value or "" }}',
            start_date: '{{ start_date or "" }}',
            end_date: '{{ end_date or "" }}',
            total_results: results.length
        });

        form.appendChild(dataInput);
        form.appendChild(searchInfo);
        document.body.appendChild(form);

        console.log('📤 إرسال البيانات للخادم...');
        form.submit();

        // تنظيف
        setTimeout(() => {
            if (document.body.contains(form)) {
                document.body.removeChild(form);
            }
        }, 2000);

    } catch (error) {
        console.error('❌ خطأ في تصدير Excel:', error);
        alert('❌ حدث خطأ في التصدير: ' + error.message);
    }
}

// وظيفة جمع البيانات من الصفحة - محسنة
function collectSearchResultsFromPage() {
    console.log('📊 جمع البيانات من نتائج البحث المعروضة');

    try {
        const results = [];
        const resultItems = document.querySelectorAll('.result-item');

        console.log(`🔍 العثور على ${resultItems.length} عنصر نتيجة`);

        if (resultItems.length === 0) {
            console.log('❌ لا توجد عناصر نتائج في الصفحة');
            return [];
        }

        resultItems.forEach((item, index) => {
            try {
                // استخراج الموضوع من العنوان
                const titleElement = item.querySelector('.result-title');
                let subject = 'بدون موضوع';
                if (titleElement) {
                    let titleText = titleElement.textContent || titleElement.innerText || '';
                    // إزالة الشارات والأيقونات
                    titleText = titleText.replace(/ملغي|نشط|مؤجل|غير محدد/g, '').trim();
                    if (titleText) {
                        subject = titleText;
                    }
                }

                // استخراج التفاصيل
                const detailItems = item.querySelectorAll('.detail-item');
                let meeting_date = '';
                let meeting_time = '';
                let location = 'غير محدد';
                let inviting_party = 'غير محدد';
                let book_number = 'غير محدد';
                let dress_code = 'غير محدد';

                detailItems.forEach(detail => {
                    const text = detail.textContent || detail.innerText || '';
                    if (text.includes('التاريخ:')) {
                        meeting_date = text.split('التاريخ:')[1]?.trim() || '';
                    } else if (text.includes('الوقت:')) {
                        meeting_time = text.split('الوقت:')[1]?.trim() || '';
                    } else if (text.includes('المكان:')) {
                        location = text.split('المكان:')[1]?.trim() || 'غير محدد';
                    } else if (text.includes('جهة الدعوة:')) {
                        inviting_party = text.split('جهة الدعوة:')[1]?.trim() || 'غير محدد';
                    } else if (text.includes('رقم الكتاب:')) {
                        book_number = text.split('رقم الكتاب:')[1]?.trim() || 'غير محدد';
                    } else if (text.includes('اللباس:')) {
                        dress_code = text.split('اللباس:')[1]?.trim() || 'غير محدد';
                    }
                });

                // استخراج النوع والحالة من الشارات
                const badges = item.querySelectorAll('.badge');
                let meeting_type = 'اجتماع';
                let status = 'نشط';
                let is_cancelled = false;
                let is_postponed = false;

                badges.forEach(badge => {
                    const badgeText = badge.textContent || badge.innerText || '';
                    if (badgeText.includes('ملغي')) {
                        status = 'ملغي';
                        is_cancelled = true;
                    } else if (badgeText.includes('مؤجل')) {
                        status = 'مؤجل';
                        is_postponed = true;
                    } else if (!badgeText.includes('نشط') && badgeText.trim()) {
                        meeting_type = badgeText.trim();
                    }
                });

                // استخراج الملاحظات
                const notesElement = item.querySelector('.alert-info');
                let notes = '';
                if (notesElement) {
                    const notesText = notesElement.textContent || notesElement.innerText || '';
                    notes = notesText.replace('ملاحظات:', '').trim();
                }

                const meetingData = {
                    id: index + 1,
                    subject: subject,
                    meeting_type: meeting_type,
                    meeting_date: meeting_date,
                    meeting_time: meeting_time,
                    location: location,
                    inviting_party: inviting_party,
                    book_number: book_number,
                    dress_code: dress_code,
                    notes: notes,
                    status: status,
                    is_cancelled: is_cancelled,
                    is_postponed: is_postponed
                };

                results.push(meetingData);
                console.log(`✅ تم جمع بيانات الاجتماع ${index + 1}:`, meetingData);

            } catch (error) {
                console.warn(`⚠️ خطأ في استخراج بيانات الاجتماع ${index + 1}:`, error);
            }
        });

        // ترتيب النتائج حسب التاريخ
        results.sort((a, b) => {
            try {
                const dateA = new Date(a.meeting_date);
                const dateB = new Date(b.meeting_date);
                return dateA - dateB;
            } catch (error) {
                return 0;
            }
        });

        console.log(`✅ تم جمع وترتيب ${results.length} نتيجة للتصدير`);
        return results;

    } catch (error) {
        console.error('❌ خطأ في جمع البيانات:', error);
        return [];
    }
}

// وظيفة تصدير PDF مبسطة جداً
function exportToPDFSimple() {
    alert('🔄 بدء تصدير PDF...');
    console.log('🚀 تم النقر على زر تصدير PDF');

    try {
        // جمع البيانات من الصفحة الحالية
        const results = [];

        // البحث عن النتائج
        const resultElements = document.querySelectorAll('.result-item');
        console.log('📊 عدد النتائج الموجودة:', resultElements.length);

        if (resultElements.length === 0) {
            alert('❌ لا توجد نتائج للتصدير');
            return;
        }

        // استخراج البيانات بأبسط طريقة ممكنة
        resultElements.forEach((element, index) => {
            const allText = element.innerText || element.textContent || '';

            // استخراج البيانات الأساسية
            const lines = allText.split('\n').map(line => line.trim()).filter(line => line);

            const meetingData = {
                id: index + 1,
                subject: lines[0] || 'بدون موضوع',
                meeting_date: extractFromLines(lines, 'التاريخ') || '',
                meeting_time: extractFromLines(lines, 'الوقت') || '',
                location: extractFromLines(lines, 'المكان') || '',
                inviting_party: extractFromLines(lines, 'جهة الدعوة') || '',
                book_number: extractFromLines(lines, 'رقم الكتاب') || '',
                dress_code: extractFromLines(lines, 'اللباس') || '',
                meeting_type: 'اجتماع',
                status: allText.includes('ملغي') ? 'ملغي' : 'نشط',
                is_cancelled: allText.includes('ملغي'),
                is_postponed: allText.includes('مؤجل')
            };

            results.push(meetingData);
        });

        console.log('✅ تم جمع البيانات:', results);

        if (results.length === 0) {
            alert('❌ فشل في جمع البيانات');
            return;
        }

        // إنشاء النموذج وإرساله
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/export_search_pdf';
        form.style.display = 'none';

        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'results_data';
        input.value = JSON.stringify(results);

        form.appendChild(input);
        document.body.appendChild(form);

        console.log('📤 إرسال النموذج...');
        form.submit();

        alert('✅ تم إرسال البيانات للتصدير!');

        // تنظيف
        setTimeout(() => {
            if (document.body.contains(form)) {
                document.body.removeChild(form);
            }
        }, 2000);

    } catch (error) {
        console.error('❌ خطأ:', error);
        alert('❌ حدث خطأ: ' + error.message);
    }
}

// وظيفة مساعدة لاستخراج البيانات من الأسطر
function extractFromLines(lines, keyword) {
    for (let line of lines) {
        if (line.includes(keyword + ':')) {
            return line.split(keyword + ':')[1]?.trim() || '';
        }
    }
    return '';
}

function exportToPDF() {
    console.log('📄 بدء تصدير PDF - الحل المبسط');

    try {
        // إظهار رسالة تحميل
        showLoadingMessage('جاري تحضير ملف PDF...');

        // جمع البيانات مباشرة من الصفحة
        const results = [];

        // البحث في عناصر النتائج
        const resultItems = document.querySelectorAll('.result-item');
        console.log(`🔍 العثور على ${resultItems.length} عنصر نتيجة`);

        if (resultItems.length === 0) {
            hideLoadingMessage();
            showErrorMessage('لا توجد نتائج للتصدير. تأكد من وجود نتائج بحث.');
            return;
        }

        // استخراج البيانات بطريقة مبسطة
        resultItems.forEach((item, index) => {
            try {
                // استخراج الموضوع
                const titleElement = item.querySelector('.result-title');
                let subject = 'بدون موضوع';
                if (titleElement) {
                    const titleText = titleElement.textContent || titleElement.innerText;
                    // إزالة النصوص الإضافية
                    subject = titleText.replace(/ملغي|نشط|مؤجل|غير محدد/g, '').trim();
                }

                // استخراج التفاصيل
                const detailItems = item.querySelectorAll('.detail-item');
                let meeting_date = '';
                let meeting_time = '';
                let location = '';
                let inviting_party = '';
                let book_number = '';
                let dress_code = '';

                detailItems.forEach(detail => {
                    const text = detail.textContent || detail.innerText;
                    if (text.includes('التاريخ:')) {
                        meeting_date = text.split('التاريخ:')[1]?.trim() || '';
                    } else if (text.includes('الوقت:')) {
                        meeting_time = text.split('الوقت:')[1]?.trim() || '';
                    } else if (text.includes('المكان:')) {
                        location = text.split('المكان:')[1]?.trim() || '';
                    } else if (text.includes('جهة الدعوة:')) {
                        inviting_party = text.split('جهة الدعوة:')[1]?.trim() || '';
                    } else if (text.includes('رقم الكتاب:')) {
                        book_number = text.split('رقم الكتاب:')[1]?.trim() || '';
                    } else if (text.includes('اللباس:')) {
                        dress_code = text.split('اللباس:')[1]?.trim() || '';
                    }
                });

                // استخراج النوع والحالة
                const badges = item.querySelectorAll('.badge');
                let meeting_type = 'غير محدد';
                let status = 'نشط';

                badges.forEach(badge => {
                    const badgeText = badge.textContent || badge.innerText;
                    if (badgeText.includes('ملغي')) {
                        status = 'ملغي';
                    } else if (badgeText.includes('مؤجل')) {
                        status = 'مؤجل';
                    } else if (!badgeText.includes('نشط')) {
                        meeting_type = badgeText.trim();
                    }
                });

                // استخراج الملاحظات
                const notesElement = item.querySelector('.alert-info');
                let notes = '';
                if (notesElement) {
                    const notesText = notesElement.textContent || notesElement.innerText;
                    notes = notesText.replace('ملاحظات:', '').trim();
                }

                const meetingData = {
                    id: index + 1,
                    subject: subject,
                    meeting_type: meeting_type,
                    meeting_date: meeting_date,
                    meeting_time: meeting_time,
                    location: location,
                    inviting_party: inviting_party,
                    book_number: book_number,
                    dress_code: dress_code,
                    notes: notes,
                    status: status,
                    is_cancelled: status === 'ملغي',
                    is_postponed: status === 'مؤجل'
                };

                results.push(meetingData);
                console.log(`✅ تم جمع بيانات الاجتماع ${index + 1}:`, meetingData);

            } catch (error) {
                console.warn(`⚠️ خطأ في استخراج بيانات الاجتماع ${index + 1}:`, error);
            }
        });

        if (results.length === 0) {
            hideLoadingMessage();
            showErrorMessage('فشل في استخراج البيانات من النتائج');
            return;
        }

        // ترتيب النتائج حسب التاريخ
        results.sort((a, b) => {
            try {
                const dateA = new Date(a.meeting_date);
                const dateB = new Date(b.meeting_date);
                return dateA - dateB;
            } catch (error) {
                return 0;
            }
        });

        console.log(`📊 تم جمع ${results.length} نتيجة للتصدير`);

        // إنشاء نموذج لإرسال البيانات
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/export_search_pdf';
        form.style.display = 'none';

        // إضافة البيانات
        const dataInput = document.createElement('input');
        dataInput.type = 'hidden';
        dataInput.name = 'results_data';
        dataInput.value = JSON.stringify(results);

        form.appendChild(dataInput);
        document.body.appendChild(form);

        console.log('📤 إرسال البيانات للخادم...');
        form.submit();

        // إزالة النموذج
        setTimeout(() => {
            if (document.body.contains(form)) {
                document.body.removeChild(form);
            }
            hideLoadingMessage();
            showSuccessMessage(`تم بدء تحميل ملف PDF بنجاح! (${results.length} اجتماع)`);
        }, 1000);

    } catch (error) {
        console.error('❌ خطأ في تصدير PDF:', error);
        hideLoadingMessage();
        showErrorMessage('حدث خطأ في تصدير PDF: ' + error.message);
    }
}

function exportToExcel() {
    console.log('📊 بدء تصدير Excel لجميع النتائج');

    try {
        // إظهار رسالة تحميل
        showLoadingMessage('جاري تحضير ملف Excel...');

        // جمع بيانات النتائج
        const results = collectSearchResults();

        if (!results || results.length === 0) {
            hideLoadingMessage();
            showErrorMessage('لا توجد نتائج للتصدير');
            return;
        }

        // إنشاء نموذج للإرسال
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/export_search_excel';
        form.style.display = 'none';

        // إضافة بيانات النتائج
        const dataInput = document.createElement('input');
        dataInput.type = 'hidden';
        dataInput.name = 'results_data';
        dataInput.value = JSON.stringify(results);

        // إضافة معلومات البحث
        const searchInfoInput = document.createElement('input');
        searchInfoInput.type = 'hidden';
        searchInfoInput.name = 'search_info';
        searchInfoInput.value = JSON.stringify({
            search_type: '{{ search_type or "" }}',
            search_value: '{{ search_value or "" }}',
            start_date: '{{ start_date or "" }}',
            end_date: '{{ end_date or "" }}',
            total_results: results.length,
            export_date: new Date().toISOString()
        });

        form.appendChild(dataInput);
        form.appendChild(searchInfoInput);
        document.body.appendChild(form);

        // إرسال النموذج
        form.submit();

        // إخفاء رسالة التحميل بعد فترة
        setTimeout(() => {
            hideLoadingMessage();
            showSuccessMessage(`تم تصدير ${results.length} نتيجة إلى Excel بنجاح!`);

            // إزالة النموذج
            if (form.parentNode) {
                form.remove();
            }
        }, 2000);

        console.log(`✅ تم إرسال ${results.length} نتيجة للتصدير`);

    } catch (error) {
        console.error('❌ خطأ في تصدير Excel:', error);
        hideLoadingMessage();
        showErrorMessage('حدث خطأ أثناء تصدير Excel. يرجى المحاولة مرة أخرى.');
    }
}

// تم حذف وظيفة طباعة الاجتماع الواحد حسب الطلب

// تم حذف وظيفة تصدير PDF للاجتماع الواحد حسب الطلب

// تم حذف وظائف الأزرار الفردية (مشاركة، تعديل) حسب الطلب

// إعداد تبديل نوع البحث - تم تبسيطه لأن نوع البحث أصبح ثابتاً
function setupSearchTypeToggle() {
    // نوع البحث أصبح ثابتاً على "subject" - لا حاجة لتبديل
    console.log('🔧 نوع البحث ثابت على "الموضوع"');

    // إظهار البحث النصي دائماً
    showTextSearch();
}

// تبديل حقول البحث حسب النوع - تم تبسيطه
function toggleSearchFields() {
    // نوع البحث أصبح ثابتاً على "subject" - إظهار البحث النصي دائماً
    showTextSearch();
}

// إظهار حقل البحث النصي
function showTextSearch() {
    const textSearchGroup = document.getElementById('textSearchGroup');
    const dateSearchGroup = document.getElementById('dateSearchGroup');

    if (textSearchGroup && dateSearchGroup) {
        textSearchGroup.style.display = 'block';
        dateSearchGroup.style.display = 'none';

        // جعل الحقل النصي مطلوب
        const searchValue = document.querySelector('input[name="search_value"]');
        if (searchValue) searchValue.required = true;

        // إزالة حقول التاريخ من المطلوب
        const startDate = document.querySelector('input[name="start_date"]');
        const endDate = document.querySelector('input[name="end_date"]');
        if (startDate) startDate.required = false;
        if (endDate) endDate.required = false;

        console.log('✅ تم تفعيل البحث النصي');
    }
}

// إعداد إرسال النموذج - يدعم البحث النصي والبحث بالتاريخ
function setupFormSubmission() {
    const form = document.getElementById('searchForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            const searchType = document.getElementById('searchType').value;

            // التحقق من صحة البيانات
            if (searchType === 'date_range') {
                // التحقق من البحث بالتاريخ
                const startDate = document.querySelector('input[name="start_date"]').value;
                const endDate = document.querySelector('input[name="end_date"]').value;

                if (!startDate || !endDate) {
                    e.preventDefault();
                    showWarningMessage('يرجى تحديد تاريخ البداية والنهاية');
                    return;
                }

                if (new Date(startDate) > new Date(endDate)) {
                    e.preventDefault();
                    showWarningMessage('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
                    return;
                }
            } else {
                // التحقق من البحث النصي
                const searchValue = document.querySelector('input[name="search_value"]').value;

                if (!searchValue.trim()) {
                    e.preventDefault();
                    showWarningMessage('يرجى إدخال قيمة للبحث');
                    return;
                }

                // تحقق خاص للبحث حسب الموضوع (نوع البحث الافتراضي)
                if (searchType === 'subject' && /[0-9]/.test(searchValue)) {
                    e.preventDefault();
                    showWarningMessage('البحث حسب الموضوع يقبل النصوص فقط، لا يُسمح بالأرقام');
                    return;
                }
            }

            // تغيير نص الزر
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري البحث...';
                submitBtn.disabled = true;
            }
        });
    }
}

// تحسين حقول التاريخ
function enhanceDateInputs() {
    const dateInputs = document.querySelectorAll('.professional-date-input');

    dateInputs.forEach(input => {
        input.addEventListener('change', function() {
            const container = this.closest('.date-input-container');
            if (container) {
                container.classList.add('selected');

                // تأثير بصري
                container.style.borderColor = '#28a745';
                container.style.boxShadow = '0 0 0 3px rgba(40, 167, 69, 0.1)';

                setTimeout(() => {
                    container.style.borderColor = '#1B4332';
                    container.style.boxShadow = '0 0 0 3px rgba(27, 67, 50, 0.1)';
                }, 1000);
            }
        });

        input.addEventListener('focus', function() {
            if (this.showPicker) {
                this.showPicker();
            }
        });
    });
}

// تأثيرات النتائج
function animateResults() {
    const resultItems = document.querySelectorAll('.result-item');
    resultItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';

        setTimeout(() => {
            item.style.transition = 'all 0.5s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// وظائف مساعدة للتواريخ السريعة المحسنة
function setQuickDateRange(type) {
    console.log('🗓️ تعيين فترة زمنية:', type);

    // إظهار رسالة تحميل
    showLoadingMessage(`جاري تعيين فترة ${getDateRangeLabel(type)}...`);

    const today = new Date();
    const startDateInput = document.getElementById('startDateInput');
    const endDateInput = document.getElementById('endDateInput');

    if (!startDateInput || !endDateInput) {
        hideLoadingMessage();
        showErrorMessage('خطأ في العثور على حقول التاريخ');
        return;
    }

    let startDate, endDate;

    switch(type) {
        case 'today':
            startDate = endDate = today;
            break;
        case 'week':
            startDate = new Date(today);
            startDate.setDate(today.getDate() - 7);
            endDate = today;
            break;
        case 'month':
            startDate = new Date(today);
            startDate.setMonth(today.getMonth() - 1);
            endDate = today;
            break;
        case 'year':
            startDate = new Date(today);
            startDate.setFullYear(today.getFullYear() - 1);
            endDate = today;
            break;
    }

    // تعيين القيم
    startDateInput.value = startDate.toISOString().split('T')[0];
    endDateInput.value = endDate.toISOString().split('T')[0];

    // تفعيل التأثيرات البصرية
    animateDateInput(startDateInput);
    animateDateInput(endDateInput);



    // إطلاق أحداث التغيير
    startDateInput.dispatchEvent(new Event('change', { bubbles: true }));
    endDateInput.dispatchEvent(new Event('change', { bubbles: true }));

    // إخفاء رسالة التحميل وإظهار رسالة نجاح
    setTimeout(() => {
        hideLoadingMessage();
        showSuccessMessage(`تم تعيين فترة ${getDateRangeLabel(type)} بنجاح!`);
    }, 500);

    console.log(`✅ تم تعيين الفترة من ${startDateInput.value} إلى ${endDateInput.value}`);
}

// وظيفة مساعدة للحصول على تسمية الفترة الزمنية
function getDateRangeLabel(type) {
    switch(type) {
        case 'today': return 'اليوم';
        case 'week': return 'آخر أسبوع';
        case 'month': return 'آخر شهر';
        case 'year': return 'آخر سنة';
        default: return 'الفترة المحددة';
    }
}



// تأثير بصري لحقول التاريخ
function animateDateInput(input) {
    const container = input.closest('.modern-date-input-container');
    if (container) {
        // تأثير وميض أخضر
        container.style.borderColor = '#28a745';
        container.style.boxShadow = '0 0 0 4px rgba(40, 167, 69, 0.2)';

        setTimeout(() => {
            container.style.borderColor = '#1B4332';
            container.style.boxShadow = '0 0 0 4px rgba(27, 67, 50, 0.1)';
        }, 1000);
    }
}

// فتح منتقي التاريخ المحسن
function openDatePicker(inputId) {
    console.log('🗓️ فتح منتقي التاريخ الاحترافي:', inputId);

    const input = document.getElementById(inputId);
    if (!input) {
        console.error('❌ لم يتم العثور على حقل التاريخ:', inputId);
        return;
    }

    // تأثير بصري على البطاقة الاحترافية
    const card = input.closest('.professional-date-card');
    if (card) {
        card.style.transform = 'translateY(-8px) scale(1.03)';
        card.style.boxShadow = '0 16px 64px rgba(27, 67, 50, 0.25)';

        // تأثير على الأيقونة
        const icon = card.querySelector('.date-icon-circle');
        if (icon) {
            icon.style.transform = 'rotate(360deg) scale(1.2)';
            icon.style.boxShadow = '0 8px 32px rgba(0,0,0,0.3)';
        }

        setTimeout(() => {
            card.style.transform = 'translateY(-5px) scale(1.02)';
            if (icon) {
                icon.style.transform = 'rotate(360deg) scale(1.1)';
            }
        }, 300);
    }

    // إخفاء overlay
    const overlay = input.nextElementSibling;
    if (overlay && overlay.classList.contains('date-display-overlay')) {
        overlay.style.opacity = '0';
        overlay.style.pointerEvents = 'none';
    }

    // محاولة فتح منتقي التاريخ
    try {
        input.focus();

        // للمتصفحات الحديثة
        if (input.showPicker && typeof input.showPicker === 'function') {
            input.showPicker();
            console.log('تم فتح منتقي التاريخ الحديث');
        } else {
            // للمتصفحات القديمة
            input.click();
            console.log('تم فتح منتقي التاريخ التقليدي');
        }

        // تأثير صوتي بصري
        playCalendarOpenEffect();

    } catch (error) {
        console.error('خطأ في فتح منتقي التاريخ:', error);

        // محاولة بديلة
        setTimeout(() => {
            input.click();
        }, 100);
    }
}

// تأثير فتح الرزنامة
function playCalendarOpenEffect() {
    // تأثير اهتزاز خفيف
    if (navigator.vibrate) {
        navigator.vibrate(50);
    }

    // تأثير بصري للصفحة
    document.body.style.transform = 'scale(1.001)';
    setTimeout(() => {
        document.body.style.transform = 'scale(1)';
    }, 100);
}

// تحسين تفاعل حقول التاريخ
function enhanceDateInputs() {
    const dateInputs = document.querySelectorAll('.modern-date-input');

    dateInputs.forEach(input => {
        // عند التغيير
        input.addEventListener('change', function() {
            console.log('تم تغيير التاريخ:', this.value);
            animateDateInput(this);


        });

        // عند التركيز
        input.addEventListener('focus', function() {
            const container = this.closest('.modern-date-input-container');
            if (container) {
                container.style.transform = 'translateY(-2px)';
                container.style.boxShadow = '0 6px 20px rgba(27, 67, 50, 0.15)';
            }
        });

        // عند فقدان التركيز
        input.addEventListener('blur', function() {
            const container = this.closest('.modern-date-input-container');
            if (container) {
                container.style.transform = 'translateY(0)';
                container.style.boxShadow = '0 2px 8px rgba(0,0,0,0.05)';
            }
        });

        // عند الكتابة
        input.addEventListener('input', function() {
            if (this.value) {
                this.style.background = 'linear-gradient(135deg, rgba(40, 167, 69, 0.05) 0%, rgba(40, 167, 69, 0.02) 100%)';
            } else {
                this.style.background = 'transparent';
            }
        });
    });
}

// وظائف الأزرار السريعة
function showDateSearch() {
    console.log('🗓️ تفعيل البحث بالتاريخ');

    // إخفاء النتائج السابقة
    hideSearchResults();

    // تنظيف العناصر الديناميكية
    cleanupDynamicElements();

    try {
        // إخفاء الأزرار السريعة وإظهار نموذج البحث
        const quickSection = document.querySelector('.quick-search-section');
        const mainForm = document.getElementById('mainSearchForm');
        const searchType = document.getElementById('searchType');

        if (!quickSection || !mainForm || !searchType) {
            console.error('❌ لم يتم العثور على العناصر المطلوبة');
            return;
        }

        // إخفاء الأزرار وإظهار النموذج
        quickSection.style.display = 'none';
        mainForm.style.display = 'block';

        // تعيين نوع البحث للتاريخ (مؤقتاً للبحث بالتاريخ)
        searchType.value = 'date_range';

        // إظهار حقول التاريخ مباشرة
        const textSearchGroup = document.getElementById('textSearchGroup');
        const dateSearchGroup = document.getElementById('dateSearchGroup');

        if (textSearchGroup && dateSearchGroup) {
            textSearchGroup.style.display = 'none';
            dateSearchGroup.style.display = 'block';

            // جعل حقول التاريخ مطلوبة
            const startDate = document.querySelector('input[name="start_date"]');
            const endDate = document.querySelector('input[name="end_date"]');
            if (startDate) startDate.required = true;
            if (endDate) endDate.required = true;

            // إزالة الحقل النصي من المطلوب
            const searchValue = document.querySelector('input[name="search_value"]');
            if (searchValue) searchValue.required = false;
        }

        // تأثير بصري محسن
        mainForm.style.opacity = '0';
        mainForm.style.transform = 'translateY(30px)';

        setTimeout(() => {
            mainForm.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            mainForm.style.opacity = '1';
            mainForm.style.transform = 'translateY(0)';
        }, 150);

        // تركيز على حقل التاريخ الأول
        setTimeout(() => {
            const startDateInput = document.getElementById('startDateInput');
            if (startDateInput) {
                startDateInput.focus();
            }
        }, 700);

        // تهيئة منتقي التاريخ المخصص
        setTimeout(() => {
            initCustomDatePickers();
        }, 100);

        console.log('✅ تم تفعيل البحث بالتاريخ بنجاح');
        showInfoMessage('تم تفعيل البحث بالتاريخ');

    } catch (error) {
        console.error('❌ خطأ في تفعيل البحث بالتاريخ:', error);
    }
}







// دالة إخفاء النتائج السابقة
function hideSearchResults() {
    console.log('🔄 إخفاء النتائج السابقة');

    try {
        // إخفاء شريط التصدير
        const exportBar = document.querySelector('.export-actions-bar');
        if (exportBar) {
            exportBar.style.display = 'none';
            console.log('✅ تم إخفاء شريط التصدير');
        }

        // إخفاء قسم النتائج
        const resultsSection = document.querySelector('.results-container');
        if (resultsSection) {
            resultsSection.style.display = 'none';
            console.log('✅ تم إخفاء قسم النتائج');
        }

        // إخفاء أي نتائج أخرى
        const searchResults = document.querySelectorAll('.search-result, .meeting-card, .result-item, .meeting-item');
        searchResults.forEach(result => {
            result.style.display = 'none';
        });

        // إخفاء رسائل "لا توجد نتائج"
        const noResultsMessages = document.querySelectorAll('.no-results, .alert-info, .alert-warning');
        noResultsMessages.forEach(message => {
            if (message.textContent.includes('لا توجد') ||
                message.textContent.includes('لم يتم العثور') ||
                message.textContent.includes('لا يوجد') ||
                message.textContent.includes('no results')) {
                message.style.display = 'none';
            }
        });

        // إخفاء أي عناصر تحتوي على كلاس results
        const resultElements = document.querySelectorAll('[class*="result"]');
        resultElements.forEach(element => {
            if (element.style.display !== 'none') {
                element.style.display = 'none';
            }
        });

        console.log('✅ تم إخفاء جميع النتائج السابقة');

    } catch (error) {
        console.error('❌ خطأ في إخفاء النتائج:', error);
    }
}

// دالة البحث حسب رقم الكتاب
function showBookNumberSearch() {
    console.log('📚 تفعيل البحث حسب رقم الكتاب');

    // إخفاء النتائج السابقة
    hideSearchResults();

    // تنظيف العناصر الديناميكية
    cleanupDynamicElements();

    try {
        // إخفاء الأزرار السريعة وإظهار نموذج البحث
        const quickSection = document.querySelector('.quick-search-section');
        const mainForm = document.getElementById('mainSearchForm');
        const searchType = document.getElementById('searchType');
        const searchValue = document.querySelector('input[name="search_value"]');

        if (!quickSection || !mainForm || !searchType || !searchValue) {
            console.error('❌ لم يتم العثور على العناصر المطلوبة');
            return;
        }

        // إخفاء الأزرار وإظهار النموذج
        quickSection.style.display = 'none';
        mainForm.style.display = 'block';

        // تعيين نوع البحث لرقم الكتاب
        searchType.value = 'book_number';

        // إظهار حقول البحث النصي مباشرة
        showTextSearch();

        // تحسين حقل البحث
        searchValue.placeholder = 'اكتب رقم الكتاب (مثال: 123/2025)...';
        searchValue.value = '';

        // تأثير بصري محسن
        mainForm.style.opacity = '0';
        mainForm.style.transform = 'translateY(30px)';

        setTimeout(() => {
            mainForm.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            mainForm.style.opacity = '1';
            mainForm.style.transform = 'translateY(0)';
        }, 150);

        // تركيز على حقل البحث مع تأخير
        setTimeout(() => {
            searchValue.focus();
            searchValue.select();
        }, 700);

        console.log('✅ تم تفعيل البحث حسب رقم الكتاب بنجاح');
        showInfoMessage('تم تفعيل البحث حسب رقم الكتاب');

    } catch (error) {
        console.error('❌ خطأ في تفعيل البحث حسب رقم الكتاب:', error);
    }
}

// دالة البحث حسب الموضوع
function showSubjectSearch() {
    console.log('📝 تفعيل البحث حسب الموضوع');

    // إخفاء النتائج السابقة
    hideSearchResults();

    // تنظيف العناصر الديناميكية
    cleanupDynamicElements();

    try {
        // إخفاء الأزرار السريعة وإظهار نموذج البحث
        const quickSection = document.querySelector('.quick-search-section');
        const mainForm = document.getElementById('mainSearchForm');
        const searchType = document.getElementById('searchType');
        const searchValue = document.querySelector('input[name="search_value"]');

        if (!quickSection || !mainForm || !searchType || !searchValue) {
            console.error('❌ لم يتم العثور على العناصر المطلوبة');
            return;
        }

        // إخفاء الأزرار وإظهار النموذج
        quickSection.style.display = 'none';
        mainForm.style.display = 'block';

        // تعيين نوع البحث للموضوع (هذا هو الافتراضي)
        searchType.value = 'subject';

        // إظهار حقول البحث النصي مباشرة
        showTextSearch();

        // تحسين حقل البحث
        searchValue.placeholder = 'اكتب موضوع الاجتماع أو جزء منه (نصوص فقط)...';
        searchValue.value = '';

        // إضافة تحقق من صحة البيانات لمنع الأرقام
        searchValue.setAttribute('pattern', '[^0-9]*');
        searchValue.setAttribute('title', 'يُسمح بالنصوص فقط، لا يُسمح بالأرقام');

        // إضافة مستمع للأحداث لمنع إدخال الأرقام
        searchValue.addEventListener('input', function(e) {
            // إزالة أي أرقام من النص المدخل
            this.value = this.value.replace(/[0-9]/g, '');

            // إظهار تحذير إذا تم محاولة إدخال أرقام
            if (/[0-9]/.test(e.data)) {
                showWarningMessage('لا يُسمح بإدخال الأرقام في البحث حسب الموضوع');
            }
        });

        // منع لصق النصوص التي تحتوي على أرقام
        searchValue.addEventListener('paste', function(e) {
            e.preventDefault();
            let paste = (e.clipboardData || window.clipboardData).getData('text');
            // إزالة الأرقام من النص الملصق
            paste = paste.replace(/[0-9]/g, '');
            this.value = paste;

            if (/[0-9]/.test((e.clipboardData || window.clipboardData).getData('text'))) {
                showWarningMessage('تم إزالة الأرقام من النص الملصق');
            }
        });

        // تأثير بصري محسن
        mainForm.style.opacity = '0';
        mainForm.style.transform = 'translateY(30px)';

        setTimeout(() => {
            mainForm.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            mainForm.style.opacity = '1';
            mainForm.style.transform = 'translateY(0)';
        }, 150);

        // تركيز على حقل البحث مع تأخير
        setTimeout(() => {
            searchValue.focus();
            searchValue.select();
        }, 700);

        console.log('✅ تم تفعيل البحث حسب الموضوع بنجاح');
        showInfoMessage('تم تفعيل البحث حسب الموضوع');

    } catch (error) {
        console.error('❌ خطأ في تفعيل البحث حسب الموضوع:', error);
    }
}

// دالة البحث حسب جهة الدعوة (مبسطة)
function showInvitingPartySearch() {
    console.log('🏢 تفعيل البحث حسب جهة الدعوة');

    // إخفاء النتائج السابقة
    hideSearchResults();

    try {
        // إخفاء الأزرار السريعة وإظهار نموذج البحث
        const quickSection = document.querySelector('.quick-search-section');
        const mainForm = document.getElementById('mainSearchForm');
        const searchType = document.getElementById('searchType');
        const searchValueInput = document.getElementById('searchValueInput');

        if (!quickSection || !mainForm || !searchType || !searchValueInput) {
            throw new Error('عناصر النموذج غير موجودة');
        }

        // إخفاء الأزرار وإظهار النموذج
        quickSection.style.display = 'none';
        mainForm.style.display = 'block';

        // تعيين نوع البحث لجهة الدعوة
        searchType.value = 'inviting_party';

        // إظهار حقول البحث النصي مباشرة
        showTextSearch();

        // تحديث placeholder وتفعيل autocomplete
        searchValueInput.placeholder = 'ابحث أو اكتب جهة الدعوة...';
        searchValueInput.focus();

        // تحميل جهات الدعوة وعرض القائمة
        loadSearchInvitingParties().then(() => {
            const filteredParties = filterSearchInvitingParties('');
            showSearchInvitingPartiesDropdown(filteredParties, searchValueInput);
        });

        console.log('✅ تم تفعيل البحث حسب جهة الدعوة بنجاح');
        showInfoMessage('تم تفعيل البحث حسب جهة الدعوة - اختر من القائمة أو اكتب للبحث');

    } catch (error) {
        console.error('❌ خطأ في تفعيل البحث حسب جهة الدعوة:', error);
        showErrorMessage('خطأ في تفعيل البحث: ' + error.message);
    }
}

// دالة إنشاء قائمة منسدلة لجهات الدعوة
async function createInvitingPartyDropdown() {
    console.log('📋 إنشاء قائمة منسدلة لجهات الدعوة');

    try {
        const textSearchGroup = document.getElementById('textSearchGroup');
        const searchValueInput = document.querySelector('input[name="search_value"]');

        if (!textSearchGroup || !searchValueInput) {
            throw new Error('عناصر النموذج غير موجودة');
        }

        // إخفاء حقل النص وإنشاء قائمة منسدلة
        searchValueInput.style.display = 'none';

        // إنشاء قائمة منسدلة
        const selectElement = document.createElement('select');
        selectElement.name = 'search_value';
        selectElement.className = 'form-control';
        selectElement.id = 'invitingPartySelect';
        selectElement.required = true;

        // إضافة خيار افتراضي
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = 'جاري تحميل جهات الدعوة...';
        selectElement.appendChild(defaultOption);

        // إدراج القائمة المنسدلة
        searchValueInput.parentNode.insertBefore(selectElement, searchValueInput.nextSibling);

        // جلب جهات الدعوة من الخادم
        console.log('🌐 جلب جهات الدعوة من الخادم...');
        console.log('🔗 المسار:', '/api/inviting_parties');

        const response = await fetch('/api/inviting_parties', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin'
        });
        console.log('📡 استجابة الخادم:', response.status, response.statusText);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('📊 بيانات جهات الدعوة:', data);

        if (data.success && data.inviting_parties && Array.isArray(data.inviting_parties)) {
            // مسح الخيار الافتراضي
            selectElement.innerHTML = '';

            // إضافة خيار "اختر جهة الدعوة"
            const chooseOption = document.createElement('option');
            chooseOption.value = '';
            chooseOption.textContent = 'اختر جهة الدعوة';
            selectElement.appendChild(chooseOption);

            // التحقق من وجود جهات دعوة
            if (data.inviting_parties.length === 0) {
                // إضافة رسالة عدم وجود جهات دعوة
                const noDataOption = document.createElement('option');
                noDataOption.value = '';
                noDataOption.textContent = 'لا توجد جهات دعوة مسجلة';
                noDataOption.disabled = true;
                selectElement.appendChild(noDataOption);

                console.log('⚠️ لا توجد جهات دعوة في قاعدة البيانات');
            } else {
                // إضافة جهات الدعوة
                data.inviting_parties.forEach(party => {
                    const option = document.createElement('option');
                    option.value = party;
                    option.textContent = party;
                    selectElement.appendChild(option);
                });

                console.log(`✅ تم تحميل ${data.inviting_parties.length} جهة دعوة`);
            showSuccessMessage(`تم تحميل ${data.inviting_parties.length} جهة دعوة`);
            }

            // تأثير بصري
            selectElement.style.transform = 'scale(0.95)';
            selectElement.style.transition = 'all 0.3s ease';

            setTimeout(() => {
                selectElement.style.transform = 'scale(1)';
                selectElement.style.boxShadow = '0 0 20px rgba(253, 126, 20, 0.3)';
                selectElement.focus();
            }, 100);

        } else {
            // معالجة حالة فشل الاستجابة
            const errorMessage = data.message || 'فشل في جلب جهات الدعوة';
            console.error('❌ خطأ من الخادم:', errorMessage);
            throw new Error(errorMessage);
        }

    } catch (error) {
        console.error('❌ خطأ في إنشاء قائمة جهات الدعوة:', error);

        // محاولة حل بديل: استخدام قائمة ثابتة من جهات الدعوة الشائعة
        console.log('🔄 محاولة استخدام قائمة بديلة...');

        const selectElement = document.getElementById('invitingPartySelect');
        if (selectElement) {
            try {
                // قائمة جهات دعوة افتراضية شائعة
                const defaultParties = [
                    'وزارة التعليم',
                    'وزارة الصحة',
                    'وزارة الداخلية',
                    'وزارة المالية',
                    'وزارة العدل',
                    'وزارة الدفاع',
                    'القوات المسلحة الأردنية',
                    'مديرية الدائرة المالية',
                    'إدارة الموارد البشرية',
                    'المجلس الأعلى للتعليم',
                    'دائرة الأحوال المدنية',
                    'وزارة التنمية الاجتماعية'
                ];

                // مسح المحتوى السابق
                selectElement.innerHTML = '';

                // إضافة خيار "اختر جهة الدعوة"
                const chooseOption = document.createElement('option');
                chooseOption.value = '';
                chooseOption.textContent = 'اختر جهة الدعوة';
                selectElement.appendChild(chooseOption);

                // إضافة الجهات الافتراضية
                defaultParties.forEach(party => {
                    const option = document.createElement('option');
                    option.value = party;
                    option.textContent = party;
                    selectElement.appendChild(option);
                });

                // إضافة خيار "أخرى"
                const otherOption = document.createElement('option');
                otherOption.value = 'أخرى';
                otherOption.textContent = 'أخرى (يرجى كتابة الاسم)';
                selectElement.appendChild(otherOption);

                console.log('✅ تم تحميل القائمة البديلة بنجاح');

                // إضافة معالج لخيار "أخرى"
                selectElement.addEventListener('change', async function() {
                    if (this.value === 'أخرى') {
                        try {
                            const customParty = await showCustomModal(
                                'إدخال جهة دعوة مخصصة',
                                'مثال: وزارة التعليم العالي'
                            );

                            if (customParty && customParty.trim()) {
                                // إنشاء خيار جديد للجهة المخصصة
                                const customOption = document.createElement('option');
                                customOption.value = customParty.trim();
                                customOption.textContent = customParty.trim();
                                customOption.selected = true;

                                // إدراج الخيار قبل "أخرى"
                                this.insertBefore(customOption, this.lastElementChild);

                                showSuccessMessage(`تم إضافة جهة الدعوة: ${customParty.trim()}`);
                            } else {
                                // إعادة تعيين القيمة إذا لم يدخل المستخدم شيئاً
                                this.value = '';
                            }
                        } catch (error) {
                            console.error('خطأ في النافذة المخصصة:', error);
                            this.value = '';
                        }
                    }
                });

                // إظهار رسالة تحذير للمستخدم
                showWarningMessage('تم تحميل قائمة افتراضية لجهات الدعوة');

            } catch (fallbackError) {
                console.error('❌ فشل في تحميل القائمة البديلة:', fallbackError);
                selectElement.innerHTML = `<option value="">خطأ: ${error.message}</option>`;
                selectElement.style.borderColor = '#dc3545';
                selectElement.style.color = '#dc3545';
            }
        }
    }
}

// إضافة زر العودة للأزرار السريعة
function showQuickButtons() {
    // إخفاء النتائج السابقة عند العودة للأزرار السريعة
    hideSearchResults();

    // تنظيف أي قوائم منسدلة تم إنشاؤها
    cleanupDynamicElements();

    document.querySelector('.quick-search-section').style.display = 'block';
    document.getElementById('mainSearchForm').style.display = 'none';

    showInfoMessage('تم العودة للأزرار السريعة');
}

// دالة تنظيف العناصر الديناميكية
function cleanupDynamicElements() {
    // إزالة قائمة جهات الدعوة إذا كانت موجودة
    const invitingPartySelect = document.getElementById('invitingPartySelect');
    if (invitingPartySelect) {
        invitingPartySelect.remove();
    }

    // إظهار حقل النص الأصلي
    const searchValueInput = document.querySelector('input[name="search_value"]');
    if (searchValueInput) {
        searchValueInput.style.display = 'block';
        searchValueInput.value = '';
        searchValueInput.placeholder = 'أدخل النص للبحث...';
    }
}

// تحسين منتقي التاريخ المخصص
function initCustomDatePickers() {
    const datePickers = document.querySelectorAll('.custom-date-picker');

    datePickers.forEach(picker => {
        const input = picker.querySelector('.custom-date-input');
        const overlay = picker.querySelector('.date-picker-overlay');
        const textSpan = overlay.querySelector('.date-picker-text');

        // النقر على الـ overlay لفتح منتقي التاريخ
        overlay.addEventListener('click', () => {
            input.focus();
            if (input.showPicker) {
                input.showPicker();
            }
        });

        // تحديث النص عند تغيير التاريخ
        input.addEventListener('change', () => {
            if (input.value) {
                // تحويل التاريخ إلى تنسيق رقمي ميلادي
                const date = new Date(input.value);

                const day = date.getDate();
                const month = date.getMonth() + 1; // الشهر يبدأ من 0
                const year = date.getFullYear();

                // تنسيق YYYY/M/D
                textSpan.textContent = `${year}/${month}/${day}`;
                textSpan.style.color = '#495057';
                textSpan.style.fontWeight = '500';
            } else {
                // إعادة النص الأصلي حسب نوع الحقل
                const isStartDate = input.name === 'start_date';
                textSpan.textContent = isStartDate ? 'من تاريخ' : 'إلى تاريخ';
                textSpan.style.color = '#1B4332';
                textSpan.style.fontWeight = '600';
            }
        });

        // تحديث النص عند تحميل الصفحة
        if (input.value) {
            input.dispatchEvent(new Event('change'));
        } else {
            // تعيين النص الأولي حسب نوع الحقل
            const isStartDate = input.name === 'start_date';
            textSpan.textContent = isStartDate ? 'من تاريخ' : 'إلى تاريخ';
            textSpan.style.color = '#1B4332';
            textSpan.style.fontWeight = '600';
        }
    });
}

// تحسين تأثيرات الأزرار الرئيسية للبحث
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة منتقي التاريخ المخصص
    initCustomDatePickers();

    const mainButtons = document.querySelectorAll('.main-search-card');

    mainButtons.forEach((button, index) => {
        // تأثير ظهور متدرج
        button.style.opacity = '0';
        button.style.transform = 'translateY(40px)';

        setTimeout(() => {
            button.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
            button.style.opacity = '1';
            button.style.transform = 'translateY(0)';
        }, index * 200);

        // تأثيرات التفاعل المحسنة
        button.addEventListener('click', function(e) {
            // منع النقر المتكرر
            if (this.classList.contains('processing')) {
                e.preventDefault();
                return;
            }

            this.classList.add('processing');

            // تأثير نقر قوي
            this.style.transform = 'translateY(-12px) scale(0.97)';

            // تأثير اهتزاز خفيف
            if (navigator.vibrate) {
                navigator.vibrate([50, 30, 50]);
            }

            // تأثير ضوئي
            const ripple = document.createElement('div');
            ripple.style.cssText = `
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.6);
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            `;

            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = (e.clientX - rect.left - size / 2) + 'px';
            ripple.style.top = (e.clientY - rect.top - size / 2) + 'px';

            this.appendChild(ripple);

            setTimeout(() => {
                this.style.transform = 'translateY(-12px) scale(1.03)';
                if (ripple.parentNode) {
                    ripple.remove();
                }
                this.classList.remove('processing');
            }, 200);
        });

        // تأثير تمرير محسن
        button.addEventListener('mouseenter', function() {
            const icon = this.querySelector('.search-card-icon');
            if (icon) {
                icon.style.transform = 'rotate(8deg) scale(1.15)';
            }
        });

        button.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.search-card-icon');
            if (icon) {
                icon.style.transform = 'rotate(0deg) scale(1)';
            }
        });

        // دعم لوحة المفاتيح
        button.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });

        // تحسين التركيز
        button.addEventListener('focus', function() {
            this.style.outline = '3px solid rgba(27, 67, 50, 0.5)';
            this.style.outlineOffset = '2px';
        });

        button.addEventListener('blur', function() {
            this.style.outline = 'none';
            this.style.outlineOffset = '0';
        });
    });

    // اختصارات لوحة المفاتيح العامة
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey || e.metaKey) {
            switch(e.key) {
                case '1':
                    e.preventDefault();
                    showDateSearch();
                    break;

                case '2':
                    e.preventDefault();
                    showBookNumberSearch();
                    break;
                case '3':
                    e.preventDefault();
                    showSubjectSearch();
                    break;

            }
        }
    });
});

// وظائف مساعدة للرسائل والتحميل
function showLoadingMessage(message) {
    // إزالة أي رسالة سابقة
    hideLoadingMessage();

    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'loadingMessage';
    loadingDiv.className = 'loading-message';
    loadingDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(135deg, #1B4332 0%, #2D5A3D 100%);
        color: white;
        padding: 2rem 3rem;
        border-radius: 16px;
        box-shadow: 0 20px 60px rgba(27, 67, 50, 0.3);
        z-index: 10000;
        text-align: center;
        min-width: 300px;
        border: 2px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
    `;

    loadingDiv.innerHTML = `
        <div style="display: flex; align-items: center; gap: 1rem; justify-content: center;">
            <div class="spinner" style="
                width: 24px;
                height: 24px;
                border: 3px solid rgba(255,255,255,0.3);
                border-top: 3px solid white;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            "></div>
            <span style="font-weight: 500; font-size: 1.1rem;">${message}</span>
        </div>
    `;

    // إضافة CSS للدوران
    if (!document.getElementById('spinnerCSS')) {
        const style = document.createElement('style');
        style.id = 'spinnerCSS';
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(loadingDiv);

    // تأثير ظهور
    setTimeout(() => {
        loadingDiv.style.opacity = '1';
        loadingDiv.style.transform = 'translate(-50%, -50%) scale(1)';
    }, 100);
}

function hideLoadingMessage() {
    const loadingDiv = document.getElementById('loadingMessage');
    if (loadingDiv) {
        loadingDiv.style.opacity = '0';
        loadingDiv.style.transform = 'translate(-50%, -50%) scale(0.9)';
        setTimeout(() => {
            loadingDiv.remove();
        }, 300);
    }
}

function showErrorMessage(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(220, 53, 69, 0.3);
        z-index: 9999;
        min-width: 300px;
        transform: translateX(400px);
        transition: all 0.4s ease;
        border: 2px solid rgba(255, 255, 255, 0.2);
    `;

    errorDiv.innerHTML = `
        <div style="display: flex; align-items: center; gap: 0.75rem;">
            <i class="fas fa-exclamation-triangle" style="font-size: 1.2rem;"></i>
            <span style="font-weight: 500;">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()"
                    style="background: none; border: none; color: white; font-size: 1.2rem; cursor: pointer; margin-right: auto;">
                ×
            </button>
        </div>
    `;

    document.body.appendChild(errorDiv);

    // تأثير الظهور
    setTimeout(() => {
        errorDiv.style.transform = 'translateX(0)';
    }, 100);

    // إزالة تلقائية
    setTimeout(() => {
        errorDiv.style.transform = 'translateX(400px)';
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 400);
    }, 5000);
}

function showWarningMessage(message) {
    // إزالة أي رسالة تحذير سابقة
    const existingWarning = document.getElementById('warningMessage');
    if (existingWarning) {
        existingWarning.remove();
    }

    const warningDiv = document.createElement('div');
    warningDiv.id = 'warningMessage';
    warningDiv.className = 'warning-message';
    warningDiv.style.cssText = `
        position: fixed;
        top: 20px;
        left: 20px;
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        color: #212529;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(255, 193, 7, 0.3);
        z-index: 9999;
        min-width: 300px;
        transform: translateX(-400px);
        transition: all 0.4s ease;
        border: 2px solid rgba(255, 255, 255, 0.3);
    `;

    warningDiv.innerHTML = `
        <div style="display: flex; align-items: center; gap: 0.75rem;">
            <i class="fas fa-exclamation-circle" style="font-size: 1.2rem; color: #856404;"></i>
            <span style="font-weight: 500;">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()"
                    style="background: none; border: none; color: #212529; font-size: 1.2rem; cursor: pointer; margin-right: auto;">
                ×
            </button>
        </div>
    `;

    document.body.appendChild(warningDiv);

    // تأثير الظهور
    setTimeout(() => {
        warningDiv.style.transform = 'translateX(0)';
    }, 100);

    // إزالة الرسالة تلقائياً بعد 3 ثوان
    setTimeout(() => {
        if (warningDiv && warningDiv.parentNode) {
            warningDiv.style.transform = 'translateX(-400px)';
            setTimeout(() => {
                warningDiv.remove();
            }, 400);
        }
    }, 3000);
}

function showSuccessMessage(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        left: 20px;
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(40, 167, 69, 0.3);
        z-index: 9999;
        min-width: 300px;
        transform: translateX(-400px);
        transition: all 0.4s ease;
        border: 2px solid rgba(255, 255, 255, 0.2);
    `;

    successDiv.innerHTML = `
        <div style="display: flex; align-items: center; gap: 0.75rem;">
            <i class="fas fa-check-circle" style="font-size: 1.2rem;"></i>
            <span style="font-weight: 500;">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()"
                    style="background: none; border: none; color: white; font-size: 1.2rem; cursor: pointer; margin-right: auto;">
                ×
            </button>
        </div>
    `;

    document.body.appendChild(successDiv);

    // تأثير الظهور
    setTimeout(() => {
        successDiv.style.transform = 'translateX(0)';
    }, 100);

    // إزالة تلقائية
    setTimeout(() => {
        successDiv.style.transform = 'translateX(-400px)';
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.remove();
            }
        }, 400);
    }, 3000);
}

// وظيفة جمع بيانات النتائج للتصدير - محدثة لشاشة البحث
function collectSearchResults() {
    console.log('📊 جمع بيانات النتائج للتصدير - شاشة البحث');

    try {
        const results = [];

        // البحث عن عناصر النتائج في شاشة البحث
        const resultItems = document.querySelectorAll('.result-item');
        console.log(`🔍 العثور على ${resultItems.length} عنصر نتيجة`);

        if (resultItems.length === 0) {
            // البحث في البطاقات كبديل
            const meetingCards = document.querySelectorAll('.meeting-card');
            console.log(`🔍 البحث في البطاقات: ${meetingCards.length} بطاقة`);

            meetingCards.forEach((card, index) => {
                try {
                    const meetingData = extractMeetingDataFromCard(card, index);
                    if (meetingData) {
                        results.push(meetingData);
                    }
                } catch (error) {
                    console.warn(`⚠️ خطأ في استخراج بيانات البطاقة ${index + 1}:`, error);
                }
            });
        } else {
            // استخراج البيانات من عناصر النتائج
            resultItems.forEach((item, index) => {
                try {
                    const meetingData = {
                        id: index + 1,
                        subject: extractTextFromResultItem(item, '.result-title') || 'بدون موضوع',
                        meeting_type: extractBadgeText(item, '.badge-primary') || 'غير محدد',
                        meeting_date: extractDetailValue(item, 'التاريخ:') || '',
                        meeting_time: extractDetailValue(item, 'الوقت:') || '',
                        location: extractDetailValue(item, 'المكان:') || 'غير محدد',
                        inviting_party: extractDetailValue(item, 'جهة الدعوة:') || 'غير محدد',
                        book_number: extractDetailValue(item, 'رقم الكتاب:') || 'غير محدد',
                        dress_code: extractDetailValue(item, 'اللباس:') || 'غير محدد',
                        notes: extractNotesFromItem(item) || '',
                        is_cancelled: item.textContent.includes('ملغي') || false,
                        is_postponed: item.textContent.includes('مؤجل') || false,
                        status: item.textContent.includes('ملغي') ? 'ملغي' :
                               item.textContent.includes('مؤجل') ? 'مؤجل' : 'نشط'
                    };

                    // تنظيف البيانات
                    Object.keys(meetingData).forEach(key => {
                        if (typeof meetingData[key] === 'string') {
                            meetingData[key] = meetingData[key].replace(/\s+/g, ' ').trim();
                        }
                    });

                    results.push(meetingData);
                    console.log(`✅ تم جمع بيانات النتيجة ${index + 1}:`, meetingData);

                } catch (error) {
                    console.warn(`⚠️ خطأ في استخراج بيانات النتيجة ${index + 1}:`, error);
                }
            });
        }

        // ترتيب النتائج حسب التاريخ (الأقرب أولاً)
        results.sort((a, b) => {
            try {
                const dateA = new Date(a.meeting_date);
                const dateB = new Date(b.meeting_date);
                return dateA - dateB; // ترتيب تصاعدي (الأقرب أولاً)
            } catch (error) {
                return 0; // في حالة خطأ في التاريخ، احتفظ بالترتيب الأصلي
            }
        });

        console.log(`✅ تم جمع وترتيب ${results.length} نتيجة للتصدير`);
        console.log('📋 البيانات المجمعة:', results);

        return results;

    } catch (error) {
        console.error('❌ خطأ في جمع بيانات النتائج:', error);
        return [];
    }
}

// وظائف مساعدة لاستخراج البيانات من عناصر النتائج

// استخراج النص من عنصر النتيجة
function extractTextFromResultItem(item, selector) {
    try {
        const element = item.querySelector(selector);
        if (element) {
            // إزالة النصوص الإضافية مثل الأيقونات والشارات
            const clone = element.cloneNode(true);
            const badges = clone.querySelectorAll('.badge');
            badges.forEach(badge => badge.remove());
            const icons = clone.querySelectorAll('i');
            icons.forEach(icon => icon.remove());
            return clone.textContent.trim();
        }
        return '';
    } catch (error) {
        return '';
    }
}

// استخراج نص الشارة
function extractBadgeText(item, selector) {
    try {
        const badge = item.querySelector(selector);
        return badge ? badge.textContent.trim() : '';
    } catch (error) {
        return '';
    }
}

// استخراج قيمة التفاصيل
function extractDetailValue(item, labelText) {
    try {
        const detailItems = item.querySelectorAll('.detail-item');
        for (let detail of detailItems) {
            if (detail.textContent.includes(labelText)) {
                const text = detail.textContent;
                const colonIndex = text.indexOf(':');
                if (colonIndex !== -1) {
                    return text.substring(colonIndex + 1).trim();
                }
            }
        }
        return '';
    } catch (error) {
        return '';
    }
}

// استخراج الملاحظات
function extractNotesFromItem(item) {
    try {
        const notesAlert = item.querySelector('.alert-info');
        if (notesAlert) {
            const text = notesAlert.textContent;
            const colonIndex = text.indexOf(':');
            if (colonIndex !== -1) {
                return text.substring(colonIndex + 1).trim();
            }
        }
        return '';
    } catch (error) {
        return '';
    }
}

// استخراج البيانات من البطاقة (للتوافق مع الإصدارات السابقة)
function extractMeetingDataFromCard(card, index) {
    try {
        return {
            id: card.dataset.meetingId || (index + 1),
            meeting_type: extractTextContent(card, '.meeting-type') ||
                         extractTextContent(card, '[class*="type"]') ||
                         extractTextContent(card, '.badge') || 'غير محدد',
            meeting_date: extractTextContent(card, '.meeting-date') ||
                         extractTextContent(card, '[class*="date"]') || '',
            meeting_time: extractTextContent(card, '.meeting-time') ||
                         extractTextContent(card, '[class*="time"]') || '',
            location: extractTextContent(card, '.meeting-location') ||
                     extractTextContent(card, '[class*="location"]') ||
                     extractTextContent(card, '.location') || '',
            subject: extractTextContent(card, '.meeting-subject') ||
                    extractTextContent(card, '[class*="subject"]') ||
                    extractTextContent(card, 'h5') ||
                    extractTextContent(card, 'h4') || '',
            inviting_party: extractTextContent(card, '.meeting-inviting-party') ||
                           extractTextContent(card, '[class*="inviting"]') ||
                           extractTextContent(card, '[class*="party"]') || '',
            book_number: extractTextContent(card, '.meeting-book-number') ||
                        extractTextContent(card, '[class*="book"]') || '',
            dress_code: extractTextContent(card, '.meeting-dress-code') ||
                       extractTextContent(card, '[class*="dress"]') || '',
            is_cancelled: card.classList.contains('cancelled') ||
                         card.classList.contains('canceled') ||
                         card.textContent.includes('ملغي') || false,
            is_postponed: card.classList.contains('postponed') ||
                         card.textContent.includes('مؤجل') || false,
            status: determineMeetingStatus(card)
        };
    } catch (error) {
        console.warn('خطأ في استخراج بيانات البطاقة:', error);
        return null;
    }
}

// وظيفة مساعدة لاستخراج النص من العناصر (للتوافق)
function extractTextContent(parentElement, selector) {
    try {
        const element = parentElement.querySelector(selector);
        return element ? element.textContent.trim() : '';
    } catch (error) {
        return '';
    }
}

// وظيفة تحديد حالة الاجتماع
function determineMeetingStatus(card) {
    if (card.classList.contains('cancelled')) {
        return 'ملغي';
    } else if (card.classList.contains('postponed')) {
        return 'مؤجل';
    } else {
        // تحديد الحالة بناءً على التاريخ
        const dateText = extractTextContent(card, '.meeting-date');
        if (dateText) {
            try {
                const meetingDate = new Date(dateText);
                const today = new Date();
                today.setHours(0, 0, 0, 0);

                if (meetingDate < today) {
                    return 'مكتمل';
                } else if (meetingDate.toDateString() === today.toDateString()) {
                    return 'اليوم';
                } else {
                    return 'قادم';
                }
            } catch (error) {
                return 'غير محدد';
            }
        }
        return 'غير محدد';
    }
}

// وظيفة عرض رسالة التطوير العصرية
function showDevelopmentMessage(feature, featureName) {
    const devModal = document.createElement('div');
    devModal.className = 'development-modal';
    devModal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        backdrop-filter: blur(5px);
    `;

    devModal.innerHTML = `
        <div style="
            background: white;
            border-radius: 20px;
            padding: 3rem 2.5rem;
            max-width: 500px;
            width: 90%;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            position: relative;
            overflow: hidden;
        ">
            <div style="
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 4px;
                background: linear-gradient(90deg, #1B4332, #2D5A3D, #40916C, #2D5A3D, #1B4332);
                background-size: 200% 100%;
                animation: gradientMove 3s ease-in-out infinite;
            "></div>

            <div style="
                width: 80px;
                height: 80px;
                background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
                border-radius: 50%;
                margin: 0 auto 1.5rem;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 2rem;
                color: white;
                box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
                animation: bounce 2s infinite;
            ">
                <i class="fas fa-tools"></i>
            </div>

            <h3 style="
                color: #1B4332;
                font-size: 1.5rem;
                font-weight: 700;
                margin-bottom: 1rem;
                letter-spacing: 0.5px;
            ">ميزة قيد التطوير</h3>

            <p style="
                color: #6c757d;
                font-size: 1.1rem;
                line-height: 1.6;
                margin-bottom: 2rem;
            ">
                ميزة <strong style="color: #1B4332;">${featureName}</strong> قيد التطوير حالياً<br>
                وستكون متاحة في التحديث القادم
            </p>

            <div style="
                display: flex;
                gap: 1rem;
                justify-content: center;
                flex-wrap: wrap;
            ">
                <button onclick="this.closest('.development-modal').remove()" style="
                    background: linear-gradient(135deg, #1B4332 0%, #2D5A3D 100%);
                    color: white;
                    border: none;
                    border-radius: 12px;
                    padding: 0.75rem 2rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    box-shadow: 0 4px 15px rgba(27, 67, 50, 0.3);
                " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 25px rgba(27, 67, 50, 0.4)'"
                   onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(27, 67, 50, 0.3)'">
                    <i class="fas fa-check me-2"></i>
                    فهمت
                </button>

                <button onclick="this.closest('.development-modal').remove(); showSuccessMessage('شكراً لك! سنعمل على تطوير هذه الميزة قريباً')" style="
                    background: white;
                    color: #1B4332;
                    border: 2px solid #1B4332;
                    border-radius: 12px;
                    padding: 0.75rem 2rem;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s ease;
                " onmouseover="this.style.background='#1B4332'; this.style.color='white'"
                   onmouseout="this.style.background='white'; this.style.color='#1B4332'">
                    <i class="fas fa-heart me-2"></i>
                    أتطلع للتحديث
                </button>
            </div>
        </div>
    `;

    // إضافة CSS للحركات
    if (!document.getElementById('devModalCSS')) {
        const style = document.createElement('style');
        style.id = 'devModalCSS';
        style.textContent = `
            @keyframes gradientMove {
                0%, 100% { background-position: 0% 50%; }
                50% { background-position: 100% 50%; }
            }
            @keyframes bounce {
                0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                40% { transform: translateY(-10px); }
                60% { transform: translateY(-5px); }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(devModal);

    // تأثير ظهور
    setTimeout(() => {
        devModal.style.opacity = '1';
    }, 100);

    // إغلاق عند النقر خارج المودال
    devModal.addEventListener('click', function(e) {
        if (e.target === devModal) {
            devModal.remove();
        }
    });
}

// وظيفة تصدير CSV بـ JavaScript
function exportToCSV() {
    try {
        console.log('🔄 بدء تصدير CSV...');

        // إنشاء بيانات CSV
        let csvContent = "الرقم,الموضوع,التاريخ,الوقت,الموقع (GPS),الحالة\n";

        // جلب بيانات الاجتماعات من النتائج المعروضة
        const resultItems = document.querySelectorAll('.result-item');

        if (resultItems.length === 0) {
            csvContent += "لا توجد اجتماعات,,,,,\n";
        } else {
            resultItems.forEach((item, index) => {
                try {
                    // استخراج الموضوع
                    const titleElement = item.querySelector('.result-title');
                    let subject = 'بدون موضوع';
                    if (titleElement) {
                        const titleText = titleElement.textContent || titleElement.innerText;
                        subject = titleText.replace(/ملغي|نشط|مؤجل|غير محدد/g, '').trim();
                    }

                    // استخراج التفاصيل
                    const detailItems = item.querySelectorAll('.detail-item');
                    let meeting_date = '';
                    let meeting_time = '';
                    let location = 'غير محدد';

                    detailItems.forEach(detail => {
                        const text = detail.textContent || detail.innerText || '';
                        if (text.includes('التاريخ:')) {
                            meeting_date = text.split('التاريخ:')[1]?.trim() || '';
                        } else if (text.includes('الوقت:')) {
                            meeting_time = text.split('الوقت:')[1]?.trim() || '';
                        } else if (text.includes('المكان:')) {
                            location = text.split('المكان:')[1]?.trim() || 'غير محدد';
                        }
                    });

                    // استخراج الحالة
                    const badges = item.querySelectorAll('.badge');
                    let status = 'نشط';
                    badges.forEach(badge => {
                        const badgeText = badge.textContent || badge.innerText || '';
                        if (badgeText.includes('ملغي')) {
                            status = 'ملغي';
                        } else if (badgeText.includes('مؤجل')) {
                            status = 'مؤجل';
                        }
                    });

                    csvContent += `${index + 1},"${subject}","${meeting_date}","${meeting_time}","${location}","${status}"\n`;
                } catch (error) {
                    console.warn(`خطأ في استخراج بيانات الاجتماع ${index + 1}:`, error);
                }
            });
        }

        // إنشاء وتحميل الملف
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', 'meetings.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log('✅ تم تصدير CSV بنجاح');
        showSuccessMessage('تم تصدير البيانات بنجاح!');

    } catch (error) {
        console.error('❌ خطأ في التصدير:', error);
        showErrorMessage('حدث خطأ أثناء التصدير');
    }
}

// ===== نظام الإشعارات الموحد =====

// دوال مساعدة للإشعارات
function showSuccessMessage(message) {
    if (window.UnifiedNotifications) {
        window.UnifiedNotifications.showSuccess(message);
    } else {
        console.log('✅ نجاح:', message);
        alert('✅ ' + message);
    }
}

function showErrorMessage(message) {
    if (window.UnifiedNotifications) {
        window.UnifiedNotifications.showError(message);
    } else {
        console.log('❌ خطأ:', message);
        alert('❌ ' + message);
    }
}

function showWarningMessage(message) {
    if (window.UnifiedNotifications) {
        window.UnifiedNotifications.showWarning(message);
    } else {
        console.log('⚠️ تحذير:', message);
        alert('⚠️ ' + message);
    }
}

function showInfoMessage(message) {
    if (window.UnifiedNotifications) {
        window.UnifiedNotifications.showInfo(message);
    } else {
        console.log('ℹ️ معلومات:', message);
        alert('ℹ️ ' + message);
    }
}

// دالة عامة للإشعارات
function showNotification(message, type = 'info', duration = 3000) {
    if (window.UnifiedNotifications) {
        window.UnifiedNotifications.showNotification(message, type, duration);
    } else {
        console.log('📢 إشعار:', message, type);
        alert(message);
    }
}

console.log('🔍 تم تحميل صفحة البحث الجديدة مع نظام الإشعارات الموحد!');

// ===== نظام النافذة المخصصة =====

let customModalResolve = null;

// فتح النافذة المخصصة
function showCustomModal(title = 'إدخال جهة الدعوة', placeholder = 'مثال: وزارة التعليم') {
    return new Promise((resolve) => {
        customModalResolve = resolve;

        // تحديث النص والعنوان
        document.querySelector('.custom-modal-title').textContent = title;
        document.getElementById('customModalInput').placeholder = placeholder;
        document.getElementById('customModalInput').value = '';

        // إظهار النافذة
        const modal = document.getElementById('customModal');
        modal.classList.add('show');

        // التركيز على الحقل
        setTimeout(() => {
            document.getElementById('customModalInput').focus();
        }, 300);

        // معالج Enter
        const input = document.getElementById('customModalInput');
        const enterHandler = (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                confirmCustomModal();
            }
        };

        input.addEventListener('keydown', enterHandler);

        // معالج Escape
        const escapeHandler = (e) => {
            if (e.key === 'Escape') {
                closeCustomModal();
            }
        };

        document.addEventListener('keydown', escapeHandler);

        // معالج النقر خارج النافذة
        const clickOutsideHandler = (e) => {
            if (e.target === modal) {
                closeCustomModal();
            }
        };

        modal.addEventListener('click', clickOutsideHandler);

        // تنظيف المعالجات عند الإغلاق
        modal.addEventListener('transitionend', function cleanup() {
            if (!modal.classList.contains('show')) {
                input.removeEventListener('keydown', enterHandler);
                document.removeEventListener('keydown', escapeHandler);
                modal.removeEventListener('click', clickOutsideHandler);
                modal.removeEventListener('transitionend', cleanup);
            }
        });
    });
}

// إغلاق النافذة المخصصة
function closeCustomModal() {
    const modal = document.getElementById('customModal');
    modal.classList.remove('show');

    if (customModalResolve) {
        customModalResolve(null);
        customModalResolve = null;
    }
}

// تأكيد النافذة المخصصة
function confirmCustomModal() {
    const input = document.getElementById('customModalInput');
    const value = input.value.trim();

    if (!value) {
        // تأثير اهتزاز للحقل الفارغ
        input.style.animation = 'shake 0.5s ease-in-out';
        setTimeout(() => {
            input.style.animation = '';
        }, 500);

        showWarningMessage('يرجى إدخال اسم جهة الدعوة');
        return;
    }

    const modal = document.getElementById('customModal');
    modal.classList.remove('show');

    if (customModalResolve) {
        customModalResolve(value);
        customModalResolve = null;
    }
}

// إضافة CSS للاهتزاز
const shakeStyle = document.createElement('style');
shakeStyle.textContent = `
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-10px); }
        75% { transform: translateX(10px); }
    }
`;
document.head.appendChild(shakeStyle);

// معالج انتهاء الجلسة
document.addEventListener('DOMContentLoaded', function() {
    const searchForm = document.getElementById('searchForm');

    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            console.log('🚀 تم إرسال نموذج البحث');

            // طباعة بيانات النموذج للتحقق
            const formData = new FormData(searchForm);
            console.log('📋 بيانات النموذج:');
            for (let [key, value] of formData.entries()) {
                console.log(`   ${key}: ${value}`);
            }

            // إضافة مؤشر التحميل
            const submitButton = searchForm.querySelector('button[type="submit"]');
            if (submitButton) {
                const originalText = submitButton.innerHTML;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري البحث...';
                submitButton.disabled = true;

                // إعادة تفعيل الزر بعد 10 ثوان في حالة عدم الاستجابة
                setTimeout(() => {
                    submitButton.innerHTML = originalText;
                    submitButton.disabled = false;
                }, 10000);
            }
        });
    }

    // التحقق من انتهاء الجلسة عند تحميل الصفحة
    if (window.location.href.includes('/login')) {
        console.warn('⚠️ تم إعادة التوجيه لصفحة تسجيل الدخول - انتهت صلاحية الجلسة');

        // عرض رسالة للمستخدم
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: '⚠️ انتهت صلاحية الجلسة',
                text: 'يرجى تسجيل الدخول مرة أخرى للمتابعة',
                icon: 'warning',
                confirmButtonText: 'موافق',
                confirmButtonColor: '#f39c12'
            });
        }
    }

    // تفعيل autocomplete لجهة الدعوة في البحث
    setupInvitingPartyAutocomplete();
});

// متغيرات لجهات الدعوة في البحث
let searchInvitingParties = [];
let isLoadingSearchParties = false;

// وظيفة إعداد autocomplete لجهة الدعوة في البحث
function setupInvitingPartyAutocomplete() {
    const searchTypeSelect = document.getElementById('searchType');
    const searchValueInput = document.getElementById('searchValueInput');
    const searchValueDropdown = document.getElementById('search_value_dropdown');

    if (!searchTypeSelect || !searchValueInput || !searchValueDropdown) {
        return;
    }

    // تفعيل autocomplete عند اختيار "جهة الدعوة" - تم تبسيطه
    // نوع البحث يتم تعيينه من الدوال المختلفة، لذا نتحقق من القيمة الحالية
    function checkAndSetupAutocomplete() {
        if (searchTypeSelect.value === 'inviting_party') {
            searchValueInput.placeholder = 'ابحث أو اكتب جهة الدعوة...';
            loadSearchInvitingParties();
        } else {
            searchValueInput.placeholder = 'أدخل النص للبحث...';
            searchValueDropdown.style.display = 'none';
        }
    }

    // تحقق دوري من نوع البحث
    setInterval(checkAndSetupAutocomplete, 500);

    // تفعيل البحث عند التركيز والكتابة
    searchValueInput.addEventListener('focus', function() {
        if (searchTypeSelect.value === 'inviting_party') {
            loadSearchInvitingParties().then(() => {
                const filteredParties = filterSearchInvitingParties(this.value);
                showSearchInvitingPartiesDropdown(filteredParties, this);
            });
        }
    });

    searchValueInput.addEventListener('input', function() {
        if (searchTypeSelect.value === 'inviting_party') {
            const filteredParties = filterSearchInvitingParties(this.value);
            showSearchInvitingPartiesDropdown(filteredParties, this);
        }
    });

    searchValueInput.addEventListener('blur', function() {
        setTimeout(() => {
            searchValueDropdown.style.display = 'none';
        }, 200);
    });

    searchValueInput.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            searchValueDropdown.style.display = 'none';
        }
    });
}

// وظيفة جلب جهات الدعوة للبحث
async function loadSearchInvitingParties() {
    if (isLoadingSearchParties || searchInvitingParties.length > 0) {
        return searchInvitingParties;
    }

    isLoadingSearchParties = true;
    const loadingSpinner = document.getElementById('search_value_loading');

    try {
        if (loadingSpinner) loadingSpinner.style.display = 'block';

        const response = await fetch('/api/inviting_parties', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin'
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success && data.inviting_parties && Array.isArray(data.inviting_parties)) {
            searchInvitingParties = data.inviting_parties;
            return searchInvitingParties;
        } else {
            return [];
        }
    } catch (error) {
        console.error('❌ خطأ في جلب جهات الدعوة للبحث:', error);
        return [];
    } finally {
        isLoadingSearchParties = false;
        if (loadingSpinner) loadingSpinner.style.display = 'none';
    }
}

// وظيفة تصفية جهات الدعوة للبحث
function filterSearchInvitingParties(searchText) {
    if (!searchText || searchText.length < 1) {
        return searchInvitingParties.slice(0, 10);
    }

    const filtered = searchInvitingParties.filter(party =>
        party.toLowerCase().includes(searchText.toLowerCase())
    );

    return filtered.slice(0, 10);
}

// وظيفة عرض قائمة جهات الدعوة للبحث
function showSearchInvitingPartiesDropdown(filteredParties, inputElement) {
    const dropdown = document.getElementById('search_value_dropdown');
    if (!dropdown) return;

    dropdown.innerHTML = '';

    if (filteredParties.length === 0) {
        const noResultsItem = document.createElement('div');
        noResultsItem.className = 'dropdown-item text-muted';
        noResultsItem.textContent = 'لا توجد نتائج';
        dropdown.appendChild(noResultsItem);
    } else {
        filteredParties.forEach(party => {
            const item = document.createElement('div');
            item.className = 'dropdown-item';
            item.style.cursor = 'pointer';
            item.textContent = party;

            item.addEventListener('click', function() {
                inputElement.value = party;
                dropdown.style.display = 'none';
            });

            dropdown.appendChild(item);
        });
    }

    dropdown.style.display = 'block';
}

// تنظيف موضوع الاجتماع من حالة الاجتماع
function cleanMeetingSubject(subject) {
    if (!subject || subject === 'غير محدد') {
        return subject;
    }

    // قائمة بحالات الاجتماعات التي يجب إزالتها
    const meetingStates = [
        'اجتماع عادي',
        'اجتماع بلوتو',
        'اجتماع نشط',
        'اجتماع ملغي',
        'اجتماع مؤجل',
        'اجتماع طارئ',
        'اجتماع استثنائي',
        'اجتماع دوري',
        'اجتماع أسبوعي',
        'اجتماع شهري',
        'اجتماع ربعي',
        'اجتماع سنوي'
    ];

    let cleanedSubject = subject;

    // إزالة حالات الاجتماع من بداية أو نهاية الموضوع
    meetingStates.forEach(state => {
        // إزالة من البداية
        if (cleanedSubject.startsWith(state)) {
            cleanedSubject = cleanedSubject.substring(state.length).trim();
        }

        // إزالة من النهاية
        if (cleanedSubject.endsWith(state)) {
            cleanedSubject = cleanedSubject.substring(0, cleanedSubject.length - state.length).trim();
        }

        // إزالة من الوسط مع فواصل
        cleanedSubject = cleanedSubject.replace(new RegExp(`\\s*${state}\\s*`, 'gi'), ' ').trim();
    });

    // إزالة الفواصل الزائدة والمسافات المتعددة
    cleanedSubject = cleanedSubject
        .replace(/\s*-\s*/g, ' ') // إزالة الشرطات
        .replace(/\s*:\s*/g, ' ') // إزالة النقطتين
        .replace(/\s*،\s*/g, ' ') // إزالة الفواصل العربية
        .replace(/\s*,\s*/g, ' ') // إزالة الفواصل الإنجليزية
        .replace(/\s+/g, ' ') // إزالة المسافات المتعددة
        .trim();

    // إذا أصبح الموضوع فارغاً، إرجاع النص الأصلي
    if (!cleanedSubject || cleanedSubject.length < 3) {
        return subject;
    }

    console.log(`🧹 تنظيف الموضوع: "${subject}" → "${cleanedSubject}"`);
    return cleanedSubject;
}

// تصدير الوظائف للنطاق العام
window.printAllMeetingsInTable = printAllMeetingsInTable;
window.createMeetingsTableHTML = createMeetingsTableHTML;
window.printMultipleMeetingsDetailed = printMultipleMeetingsDetailed;
window.printSingleMeeting = printSingleMeeting;
window.extractMeetingDataFromSearchResult = extractMeetingDataFromSearchResult;
window.cleanMeetingSubject = cleanMeetingSubject;

console.log('✅ تم تحميل وظائف طباعة البحث المحدثة');

</script>

<!-- قالب الطباعة الموحد -->
<script src="{{ url_for('static', filename='js/unified-print-template.js') }}"></script>

{% endblock %}
