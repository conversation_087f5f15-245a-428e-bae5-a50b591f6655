/**
 * جافا سكريبت صفحة جميع الاجتماعات - نسخة مبسطة وفعالة 100%
 * All Meetings Page JavaScript - Simple & Working Version
 */

console.log('🚀 بدء تحميل نظام الأزرار...');

// متغيرات عامة
let currentFilter = 'all';
let allMeetings = [];

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 تم تحميل الصفحة، بدء التهيئة...');
    
    // تأخير قصير لضمان تحميل جميع العناصر
    setTimeout(function() {
        initializeSystem();
    }, 200);
});

// تهيئة النظام الكامل
function initializeSystem() {
    console.log('⚙️ تهيئة النظام...');
    
    // جمع جميع عناصر الاجتماعات
    allMeetings = document.querySelectorAll('.meeting-item');
    console.log(`📋 تم العثور على ${allMeetings.length} اجتماع`);
    
    // تهيئة البطاقات الإحصائية
    setupStatsCards();
    
    // تهيئة أزرار الفلترة
    setupFilterButtons();
    
    // تهيئة البحث
    setupSearch();
    
    console.log('✅ تم تهيئة النظام بنجاح!');
}

// تهيئة البطاقات الإحصائية
function setupStatsCards() {
    console.log('📊 تهيئة البطاقات الإحصائية...');
    
    const statsCards = document.querySelectorAll('.stats-card');
    
    statsCards.forEach(function(card, index) {
        // إضافة مستمع النقر
        card.addEventListener('click', function() {
            console.log(`🖱️ تم النقر على البطاقة ${index}`);
            
            // تحديد نوع الفلتر
            let filterType = 'all';
            if (index === 0) filterType = 'all';        // إجمالي الاجتماعات
            if (index === 1) filterType = 'active';     // اجتماعات نشطة
            if (index === 2) filterType = 'postponed';  // اجتماعات مؤجلة
            if (index === 3) filterType = 'cancelled';  // اجتماعات ملغية
            
            // تطبيق الفلتر
            applyFilter(filterType);
            
            // تحديث مظهر البطاقات
            updateCardsAppearance(index);
            
            // تحديث أزرار الفلترة
            updateButtonsAppearance(filterType);
            
            // إظهار رسالة تأكيد
            showMessage(`تم تطبيق فلتر: ${getFilterName(filterType)}`);
        });
        
        // تأثيرات التمرير
        card.addEventListener('mouseenter', function() {
            if (!card.classList.contains('selected')) {
                card.style.transform = 'translateY(-5px) scale(1.02)';
                card.style.boxShadow = '0 8px 25px rgba(0,0,0,0.2)';
                card.style.transition = 'all 0.3s ease';
            }
        });
        
        card.addEventListener('mouseleave', function() {
            if (!card.classList.contains('selected')) {
                card.style.transform = 'translateY(0) scale(1)';
                card.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
            }
        });
    });
    
    console.log('✅ تم تهيئة البطاقات الإحصائية');
}

// تهيئة أزرار الفلترة
function setupFilterButtons() {
    console.log('🔘 تهيئة أزرار الفلترة...');
    
    const filterButtons = document.querySelectorAll('.filter-tab');
    
    filterButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            const filterType = button.getAttribute('data-filter');
            console.log(`🖱️ تم النقر على زر: ${filterType}`);
            
            // تطبيق الفلتر
            applyFilter(filterType);
            
            // تحديث مظهر الأزرار
            updateButtonsAppearance(filterType);
            
            // تحديث البطاقات
            updateCardsAppearance(getCardIndex(filterType));
            
            // إظهار رسالة تأكيد
            showMessage(`تم تطبيق فلتر: ${getFilterName(filterType)}`);
        });
        
        // تأثيرات التمرير
        button.addEventListener('mouseenter', function() {
            if (!button.classList.contains('active')) {
                button.style.backgroundColor = 'rgba(27, 67, 50, 0.1)';
                button.style.transform = 'translateY(-2px)';
                button.style.transition = 'all 0.3s ease';
            }
        });
        
        button.addEventListener('mouseleave', function() {
            if (!button.classList.contains('active')) {
                button.style.backgroundColor = 'transparent';
                button.style.transform = 'translateY(0)';
            }
        });
    });
    
    console.log('✅ تم تهيئة أزرار الفلترة');
}

// تهيئة البحث
function setupSearch() {
    console.log('🔍 تهيئة البحث...');
    
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            applySearchAndFilter(searchTerm);
        });
    }
    
    console.log('✅ تم تهيئة البحث');
}

// تطبيق الفلتر الرئيسي
function applyFilter(filterType) {
    console.log(`🔍 تطبيق فلتر: ${filterType}`);
    
    currentFilter = filterType;
    
    let visibleCount = 0;
    
    allMeetings.forEach(function(meeting) {
        const meetingStatus = meeting.getAttribute('data-status') || 'active';
        let shouldShow = false;
        
        if (filterType === 'all') {
            shouldShow = true;
        } else {
            shouldShow = (meetingStatus === filterType);
        }
        
        if (shouldShow) {
            meeting.style.display = 'block';
            meeting.style.opacity = '1';
            meeting.style.transform = 'scale(1)';
            visibleCount++;
        } else {
            meeting.style.opacity = '0';
            meeting.style.transform = 'scale(0.95)';
            setTimeout(function() {
                meeting.style.display = 'none';
            }, 300);
        }
    });
    
    // تحديث العداد
    updateCounter(visibleCount);
    
    console.log(`✅ تم عرض ${visibleCount} من ${allMeetings.length} اجتماع`);
}

// تطبيق البحث مع الفلتر
function applySearchAndFilter(searchTerm) {
    console.log(`🔍 البحث: "${searchTerm}" مع فلتر: ${currentFilter}`);
    
    let visibleCount = 0;
    
    allMeetings.forEach(function(meeting) {
        const searchData = meeting.getAttribute('data-search') || '';
        const meetingStatus = meeting.getAttribute('data-status') || 'active';
        
        // فحص البحث
        const matchesSearch = !searchTerm || searchData.toLowerCase().includes(searchTerm);
        
        // فحص الفلتر
        const matchesFilter = currentFilter === 'all' || meetingStatus === currentFilter;
        
        const shouldShow = matchesSearch && matchesFilter;
        
        if (shouldShow) {
            meeting.style.display = 'block';
            meeting.style.opacity = '1';
            meeting.style.transform = 'scale(1)';
            visibleCount++;
        } else {
            meeting.style.opacity = '0';
            meeting.style.transform = 'scale(0.95)';
            setTimeout(function() {
                meeting.style.display = 'none';
            }, 300);
        }
    });
    
    // تحديث العداد
    updateCounter(visibleCount);
}

// تحديث مظهر البطاقات
function updateCardsAppearance(selectedIndex) {
    const statsCards = document.querySelectorAll('.stats-card');
    
    statsCards.forEach(function(card, index) {
        if (index === selectedIndex) {
            card.classList.add('selected');
            card.style.transform = 'translateY(-8px) scale(1.02)';
            card.style.boxShadow = '0 12px 35px rgba(27, 67, 50, 0.4)';
        } else {
            card.classList.remove('selected');
            card.style.transform = 'translateY(0) scale(1)';
            card.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
        }
    });
}

// تحديث مظهر الأزرار
function updateButtonsAppearance(activeFilter) {
    const filterButtons = document.querySelectorAll('.filter-tab');
    
    filterButtons.forEach(function(button) {
        const buttonFilter = button.getAttribute('data-filter');
        if (buttonFilter === activeFilter) {
            button.classList.add('active');
        } else {
            button.classList.remove('active');
        }
    });
}

// الحصول على فهرس البطاقة
function getCardIndex(filterType) {
    if (filterType === 'all') return 0;
    if (filterType === 'active') return 1;
    if (filterType === 'postponed') return 2;
    if (filterType === 'cancelled') return 3;
    return 0;
}

// الحصول على اسم الفلتر
function getFilterName(filterType) {
    if (filterType === 'all') return 'جميع الاجتماعات';
    if (filterType === 'active') return 'الاجتماعات النشطة';
    if (filterType === 'postponed') return 'الاجتماعات المؤجلة';
    if (filterType === 'cancelled') return 'الاجتماعات الملغية';
    return 'غير محدد';
}

// تحديث العداد
function updateCounter(count) {
    const pageTitle = document.querySelector('.page-title');
    if (pageTitle) {
        const icon = pageTitle.querySelector('i');
        const iconHtml = icon ? icon.outerHTML : '<i class="fas fa-calendar-alt me-3"></i>';
        pageTitle.innerHTML = iconHtml + 'كل الاجتماعات (' + count + ')';
    }
}

// إظهار رسالة تأكيد - استخدام النظام الموحد
function showMessage(message) {
    if (window.UnifiedNotifications) {
        window.UnifiedNotifications.showSuccess(message);
    } else {
        console.log('رسالة:', message);
        alert(message);
    }
}

console.log('✅ تم تحميل نظام الأزرار بنجاح!');
