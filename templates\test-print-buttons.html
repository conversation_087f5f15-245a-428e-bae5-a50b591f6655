<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أزرار الطباعة الجديدة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', 'Tahoma', 'Arial', sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .test-card {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            background: white;
        }
        .status-active { border-color: #28a745; background: #f8fff8; }
        .status-postponed { border-color: #ffc107; background: #fffdf0; }
        .status-cancelled { border-color: #dc3545; background: #fff8f8; }
        .status-finished { border-color: #6c757d; background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">🖨️ اختبار أزرار الطباعة الجديدة</h1>
        
        <!-- بطاقات الإحصائيات مع أزرار الطباعة -->
        <div class="row mb-4">
            <div class="col">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h6>إجمالي الاجتماعات</h6>
                        <h4>12</h4>
                    </div>
                </div>
                <div class="text-center mt-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="testPrintAll('all')">
                        <i class="fas fa-print me-1"></i>
                        طباعة الكل
                    </button>
                </div>
            </div>
            <div class="col">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h6>الاجتماعات النشطة</h6>
                        <h4>5</h4>
                    </div>
                </div>
                <div class="text-center mt-2">
                    <button class="btn btn-outline-success btn-sm" onclick="testPrintAll('active')">
                        <i class="fas fa-print me-1"></i>
                        طباعة الكل
                    </button>
                </div>
            </div>
            <div class="col">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h6>الاجتماعات المؤجلة</h6>
                        <h4>2</h4>
                    </div>
                </div>
                <div class="text-center mt-2">
                    <button class="btn btn-outline-warning btn-sm" onclick="testPrintAll('postponed')">
                        <i class="fas fa-print me-1"></i>
                        طباعة الكل
                    </button>
                </div>
            </div>
            <div class="col">
                <div class="card bg-secondary text-white">
                    <div class="card-body text-center">
                        <h6>الاجتماعات الملغية</h6>
                        <h4>3</h4>
                    </div>
                </div>
                <div class="text-center mt-2">
                    <button class="btn btn-outline-secondary btn-sm" onclick="testPrintAll('cancelled')">
                        <i class="fas fa-print me-1"></i>
                        طباعة الكل
                    </button>
                </div>
            </div>
            <div class="col">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <h6>الاجتماعات المنتهية</h6>
                        <h4>2</h4>
                    </div>
                </div>
                <div class="text-center mt-2">
                    <button class="btn btn-outline-danger btn-sm" onclick="testPrintAll('finished')">
                        <i class="fas fa-print me-1"></i>
                        طباعة الكل
                    </button>
                </div>
            </div>
        </div>

        <hr class="my-4">

        <!-- اختبار الطباعة الفردية مع حالات مختلفة -->
        <h3 class="mb-3">اختبار الطباعة الفردية مع الحالات المختلفة:</h3>

        <!-- اجتماع نشط -->
        <div class="test-card status-active meeting-item" data-status="active" data-date="2025-08-15">
            <h5 class="card-title">اجتماع مجلس الإدارة الشهري</h5>
            <p class="text-muted">📅 2025-08-15 🕐 10:30</p>
            <button class="btn btn-success btn-sm" onclick="testPrintSingle(this, 'نشط')">
                <i class="fas fa-print me-1"></i>
                طباعة (نشط)
            </button>
        </div>

        <!-- اجتماع مؤجل -->
        <div class="test-card status-postponed meeting-item" data-status="postponed" data-date="2025-07-20">
            <h5 class="card-title">اجتماع التخطيط السنوي</h5>
            <p class="text-muted">📅 2025-07-20 🕐 09:00</p>
            <button class="btn btn-warning btn-sm" onclick="testPrintSingle(this, 'مؤجل')">
                <i class="fas fa-print me-1"></i>
                طباعة (مؤجل)
            </button>
        </div>

        <!-- اجتماع ملغي -->
        <div class="test-card status-cancelled meeting-item" data-status="cancelled" data-date="2025-07-25">
            <h5 class="card-title">اجتماع المتابعة الأسبوعية</h5>
            <p class="text-muted">📅 2025-07-25 🕐 11:00</p>
            <button class="btn btn-secondary btn-sm" onclick="testPrintSingle(this, 'ملغي')">
                <i class="fas fa-print me-1"></i>
                طباعة (ملغي)
            </button>
        </div>

        <!-- اجتماع منتهي -->
        <div class="test-card status-finished meeting-item" data-status="finished" data-date="2025-06-15">
            <h5 class="card-title">اجتماع المراجعة الربعية</h5>
            <p class="text-muted">📅 2025-06-15 🕐 14:00</p>
            <button class="btn btn-danger btn-sm" onclick="testPrintSingle(this, 'منتهي')">
                <i class="fas fa-print me-1"></i>
                طباعة (منتهي)
            </button>
        </div>

        <div class="mt-4 alert alert-info">
            <h5>📋 ملاحظات:</h5>
            <ul>
                <li>اختبر كل زر "طباعة الكل" لرؤية التقارير المجمعة</li>
                <li>اختبر الطباعة الفردية لرؤية الحالة الصحيحة في العنوان الفرعي</li>
                <li>تحقق من أن العنوان الفرعي يظهر الحالة الصحيحة (نشط، مؤجل، ملغي، منتهي)</li>
                <li>افتح Developer Console لرؤية تفاصيل التشخيص</li>
            </ul>
        </div>
    </div>

    <!-- تحميل القالب الموحد -->
    <script src="{{ url_for('static', filename='js/unified-print-template.js') }}"></script>

    <script>
        // اختبار طباعة جميع الاجتماعات حسب النوع
        function testPrintAll(filterType) {
            console.log('🖨️ اختبار طباعة الكل:', filterType);
            
            // إنشاء بيانات تجريبية
            const meetings = generateTestMeetings(filterType);
            
            if (meetings.length === 0) {
                alert('لا توجد اجتماعات من هذا النوع');
                return;
            }
            
            // تحديد عنوان التقرير
            let reportTitle = '';
            switch(filterType) {
                case 'all': reportTitle = 'تقرير جميع الاجتماعات'; break;
                case 'active': reportTitle = 'تقرير الاجتماعات النشطة'; break;
                case 'postponed': reportTitle = 'تقرير الاجتماعات المؤجلة'; break;
                case 'cancelled': reportTitle = 'تقرير الاجتماعات الملغية'; break;
                case 'finished': reportTitle = 'تقرير الاجتماعات المنتهية'; break;
            }
            
            // إنشاء HTML للطباعة
            let printHTML = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>${reportTitle}</title>
                    <style>
                        body { font-family: 'Cairo', 'Tahoma', Arial, sans-serif; direction: rtl; text-align: right; padding: 20px; }
                        .header { text-align: center; margin-bottom: 30px; padding: 20px; border-bottom: 2px solid #1B4332; }
                        .meeting { margin-bottom: 30px; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
                        .status { font-weight: bold; color: #007bff; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>${reportTitle}</h1>
                        <p>عدد الاجتماعات: ${meetings.length}</p>
                        <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</p>
                    </div>
            `;
            
            meetings.forEach((meeting, index) => {
                printHTML += `
                    <div class="meeting">
                        <h3>الاجتماع ${index + 1}: ${meeting.subject}</h3>
                        <p><strong>الحالة:</strong> <span class="status">${meeting.status}</span></p>
                        <p><strong>التاريخ:</strong> ${meeting.meeting_date}</p>
                        <p><strong>الوقت:</strong> ${meeting.meeting_time}</p>
                        <p><strong>المكان:</strong> ${meeting.location}</p>
                    </div>
                `;
            });
            
            printHTML += '</body></html>';
            
            // فتح نافذة طباعة
            const printWindow = window.open('', '_blank');
            printWindow.document.write(printHTML);
            printWindow.document.close();
            setTimeout(() => printWindow.print(), 500);
        }

        // اختبار طباعة اجتماع واحد
        function testPrintSingle(button, expectedStatus) {
            console.log('🖨️ اختبار طباعة فردية، الحالة المتوقعة:', expectedStatus);
            
            const meetingCard = button.closest('.meeting-item');
            const status = meetingCard.getAttribute('data-status');
            const subject = meetingCard.querySelector('.card-title').textContent;
            const date = meetingCard.getAttribute('data-date');
            
            const meetingData = {
                subject: subject,
                status: expectedStatus,
                meeting_date: date,
                meeting_time: '10:30',
                location: 'قاعة الاجتماعات الرئيسية',
                inviting_party: 'مديرية الدائرة المالية',
                is_cancelled: status === 'cancelled',
                is_postponed: status === 'postponed'
            };
            
            console.log('📋 بيانات الاجتماع للطباعة:', meetingData);
            
            if (typeof printMeetingReport === 'function') {
                printMeetingReport(meetingData);
            } else {
                alert('خطأ: دالة الطباعة غير متوفرة');
            }
        }

        // إنشاء بيانات تجريبية
        function generateTestMeetings(filterType) {
            const allMeetings = [
                { subject: 'اجتماع مجلس الإدارة', status: 'نشط', meeting_date: '2025-08-15', meeting_time: '10:30', location: 'قاعة الإدارة' },
                { subject: 'اجتماع التخطيط', status: 'مؤجل', meeting_date: '2025-07-20', meeting_time: '09:00', location: 'قاعة التخطيط' },
                { subject: 'اجتماع المتابعة', status: 'ملغي', meeting_date: '2025-07-25', meeting_time: '11:00', location: 'قاعة المتابعة' },
                { subject: 'اجتماع المراجعة', status: 'منتهي', meeting_date: '2025-06-15', meeting_time: '14:00', location: 'قاعة المراجعة' }
            ];
            
            if (filterType === 'all') return allMeetings;
            
            const statusMap = {
                'active': 'نشط',
                'postponed': 'مؤجل', 
                'cancelled': 'ملغي',
                'finished': 'منتهي'
            };
            
            return allMeetings.filter(m => m.status === statusMap[filterType]);
        }

        // التحقق من تحميل القالب الموحد
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                if (typeof printMeetingReport === 'function') {
                    console.log('✅ تم تحميل القالب الموحد بنجاح');
                } else {
                    console.error('❌ فشل في تحميل القالب الموحد');
                }
            }, 500);
        });
    </script>
</body>
</html>
