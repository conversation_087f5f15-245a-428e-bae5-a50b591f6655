// حل مشكلة طباعة البطاقات المختلفة
console.log('🚀 تحميل حل طباعة البطاقات المحسن');

// دالة لطباعة بطاقة محددة
function printSpecificCard(cardType) {
    console.log('🖨️ طباعة البطاقة:', cardType);
    
    let title = '';
    let value = '';
    let description = '';
    let color = '#007bff';
    
    // تحديد البيانات حسب نوع البطاقة
    switch(cardType) {
        case 'total-meetings':
            const totalEl = document.getElementById('totalMeetings');
            title = 'إجمالي الاجتماعات';
            value = totalEl ? totalEl.textContent.trim() : '0';
            description = 'العدد الكلي للاجتماعات المسجلة في النظام';
            color = '#007bff';
            break;
            
        case 'upcoming-meetings':
            const upcomingEl = document.getElementById('upcomingMeetings');
            title = 'الاجتماعات القادمة';
            value = upcomingEl ? upcomingEl.textContent.trim() : '0';
            description = 'الاجتماعات المجدولة في المستقبل';
            color = '#28a745';
            break;
            
        case 'completed-meetings':
            const completedEl = document.getElementById('completedMeetings');
            title = 'الاجتماعات المكتملة';
            value = completedEl ? completedEl.textContent.trim() : '0';
            description = 'الاجتماعات التي تم عقدها بنجاح';
            color = '#17a2b8';
            break;
            
        case 'cancelled-meetings':
            const cancelledEl = document.getElementById('cancelledMeetings');
            title = 'الاجتماعات الملغية';
            value = cancelledEl ? cancelledEl.textContent.trim() : '0';
            description = 'الاجتماعات التي تم إلغاؤها';
            color = '#dc3545';
            break;
            
        case 'monthly-average':
            const avgEl = document.getElementById('monthlyAverage');
            title = 'المتوسط الشهري';
            value = avgEl ? avgEl.textContent.trim() : '0';
            description = 'معدل الاجتماعات شهرياً';
            color = '#6f42c1';
            break;
            
        case 'most-active-month':
            const activeEl = document.getElementById('mostActiveMonth');
            title = 'أكثر الشهور نشاطاً';
            value = activeEl ? activeEl.textContent.trim() : 'غير محدد';
            description = 'الشهر الذي سجل أعلى عدد من الاجتماعات';
            color = '#fd7e14';
            break;
    }
    
    console.log('📊 البيانات المستخرجة:', { title, value, description, color });
    
    // إنشاء HTML للطباعة
    const printHTML = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>طباعة - ${title}</title>
            <style>
                body { font-family: Arial, sans-serif; direction: rtl; padding: 40px; }
                .header { text-align: center; margin-bottom: 40px; border-bottom: 3px solid ${color}; padding-bottom: 20px; }
                .main-title { font-size: 28px; font-weight: bold; color: ${color}; margin-bottom: 10px; }
                .subtitle { font-size: 16px; color: #666; }
                .card { background: #f8f9fa; border: 2px solid ${color}; border-radius: 12px; padding: 40px; margin: 30px 0; text-align: center; }
                .value { font-size: 48px; font-weight: bold; color: ${color}; margin-bottom: 20px; }
                .card-title { font-size: 24px; font-weight: bold; color: #333; margin-bottom: 15px; }
                .card-description { font-size: 16px; color: #666; line-height: 1.6; }
                .footer { margin-top: 40px; text-align: center; font-size: 14px; color: #999; border-top: 1px solid #dee2e6; padding-top: 20px; }
            </style>
        </head>
        <body>
            <div class="header">
                <div class="main-title">🏛️ نظام إدارة الاجتماعات</div>
                <div class="subtitle">📊 تقرير إحصائي - ${title}</div>
            </div>
            <div class="card">
                <div class="value">${value}</div>
                <div class="card-title">${title}</div>
                <div class="card-description">${description}</div>
            </div>
            <div class="footer">
                <p><strong>📅 التاريخ:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
                <p><strong>🕐 الوقت:</strong> ${new Date().toLocaleTimeString('ar-SA')}</p>
                <p><strong>🏛️ المؤسسة:</strong> القوات المسلحة الأردنية</p>
            </div>
        </body>
        </html>
    `;
    
    // فتح نافذة طباعة
    const printWindow = window.open('', '_blank');
    printWindow.document.write(printHTML);
    printWindow.document.close();
    setTimeout(() => {
        printWindow.print();
        printWindow.close();
    }, 500);
}

// ربط الأحداث مباشرة بالعناصر
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 ربط أحداث الطباعة المحسنة...');
    
    // قائمة البطاقات والعناصر المرتبطة
    const cards = [
        { id: 'totalMeetings', type: 'total-meetings' },
        { id: 'upcomingMeetings', type: 'upcoming-meetings' },
        { id: 'completedMeetings', type: 'completed-meetings' },
        { id: 'cancelledMeetings', type: 'cancelled-meetings' },
        { id: 'monthlyAverage', type: 'monthly-average' },
        { id: 'mostActiveMonth', type: 'most-active-month' }
    ];
    
    cards.forEach(card => {
        const element = document.getElementById(card.id);
        if (element) {
            // البحث عن البطاقة الأب
            const parentCard = element.closest('.stat-card, .card, .dashboard-card, .col-md-4, .col-lg-4');
            if (parentCard) {
                // البحث عن أزرار الطباعة والـ PDF في البطاقة
                const allButtons = parentCard.querySelectorAll('button, a, .btn, [onclick]');

                allButtons.forEach(btn => {
                    const onclick = btn.getAttribute('onclick') || '';
                    const title = btn.getAttribute('title') || '';
                    const text = btn.textContent || '';

                    // تحديد نوع الزر (طباعة أم PDF)
                    if (onclick.includes('printStatCard') || title.includes('طباعة') || text.includes('طباعة')) {
                        console.log(`✅ ربط زر الطباعة للبطاقة: ${card.type}`);

                        // إزالة الأحداث القديمة
                        btn.removeAttribute('onclick');

                        // إضافة حدث جديد للطباعة
                        btn.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            console.log(`🎯 طباعة البطاقة: ${card.type}`);
                            printSpecificCard(card.type);
                        });

                    } else if (onclick.includes('exportStatCardToPDF') || title.includes('PDF') || text.includes('PDF')) {
                        console.log(`✅ ربط زر PDF للبطاقة: ${card.type}`);

                        // إزالة الأحداث القديمة
                        btn.removeAttribute('onclick');

                        // إضافة حدث جديد للـ PDF
                        btn.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            console.log(`📄 تصدير PDF للبطاقة: ${card.type}`);
                            exportSpecificCardToPDF(card.type);
                        });
                    }
                });
            }
        }
    });
    
    // طريقة احتياطية: البحث عن جميع الأزرار وربطها حسب المحتوى
    setTimeout(() => {
        const allButtons = document.querySelectorAll('button, a, .btn');
        allButtons.forEach(btn => {
            const onclick = btn.getAttribute('onclick') || '';

            // ربط أزرار الطباعة
            if (onclick.includes('printStatCard')) {
                const match = onclick.match(/printStatCard\(['"]([^'"]+)['"]\)/);
                if (match) {
                    const cardType = match[1];
                    console.log(`🔄 إعادة ربط زر الطباعة للبطاقة: ${cardType}`);

                    btn.removeAttribute('onclick');
                    btn.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log(`🎯 طباعة البطاقة (احتياطي): ${cardType}`);
                        printSpecificCard(cardType);
                    });
                }
            }

            // ربط أزرار PDF
            else if (onclick.includes('exportStatCardToPDF')) {
                const match = onclick.match(/exportStatCardToPDF\(['"]([^'"]+)['"]\)/);
                if (match) {
                    const cardType = match[1];
                    console.log(`🔄 إعادة ربط زر PDF للبطاقة: ${cardType}`);

                    btn.removeAttribute('onclick');
                    btn.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log(`📄 تصدير PDF للبطاقة (احتياطي): ${cardType}`);
                        exportSpecificCardToPDF(cardType);
                    });
                }
            }
        });
    }, 1000);
});

// دالة لتصدير بطاقة محددة إلى PDF
function exportSpecificCardToPDF(cardType) {
    console.log('📄 تصدير البطاقة إلى PDF:', cardType);

    let title = '';
    let value = '';
    let description = '';
    let color = '#007bff';

    // تحديد البيانات حسب نوع البطاقة
    switch(cardType) {
        case 'total-meetings':
            const totalEl = document.getElementById('totalMeetings');
            title = 'إجمالي الاجتماعات';
            value = totalEl ? totalEl.textContent.trim() : '0';
            description = 'العدد الكلي للاجتماعات المسجلة في النظام';
            color = '#007bff';
            break;

        case 'upcoming-meetings':
            const upcomingEl = document.getElementById('upcomingMeetings');
            title = 'الاجتماعات القادمة';
            value = upcomingEl ? upcomingEl.textContent.trim() : '0';
            description = 'الاجتماعات المجدولة في المستقبل';
            color = '#28a745';
            break;

        case 'completed-meetings':
            const completedEl = document.getElementById('completedMeetings');
            title = 'الاجتماعات المكتملة';
            value = completedEl ? completedEl.textContent.trim() : '0';
            description = 'الاجتماعات التي تم عقدها بنجاح';
            color = '#17a2b8';
            break;

        case 'cancelled-meetings':
            const cancelledEl = document.getElementById('cancelledMeetings');
            title = 'الاجتماعات الملغية';
            value = cancelledEl ? cancelledEl.textContent.trim() : '0';
            description = 'الاجتماعات التي تم إلغاؤها';
            color = '#dc3545';
            break;

        case 'monthly-average':
            const avgEl = document.getElementById('monthlyAverage');
            title = 'المتوسط الشهري';
            value = avgEl ? avgEl.textContent.trim() : '0';
            description = 'معدل الاجتماعات شهرياً';
            color = '#6f42c1';
            break;

        case 'most-active-month':
            const activeEl = document.getElementById('mostActiveMonth');
            title = 'أكثر الشهور نشاطاً';
            value = activeEl ? activeEl.textContent.trim() : 'غير محدد';
            description = 'الشهر الذي سجل أعلى عدد من الاجتماعات';
            color = '#fd7e14';
            break;
    }

    console.log('📊 البيانات المستخرجة للـ PDF:', { title, value, description, color });

    // إنشاء HTML محسن للـ PDF
    const pdfHTML = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تصدير PDF - ${title}</title>
            <style>
                @page {
                    size: A4;
                    margin: 2cm;
                    @top-center { content: "نظام إدارة الاجتماعات"; }
                    @bottom-center { content: "صفحة " counter(page); }
                }

                body {
                    font-family: 'Arial', 'Tahoma', sans-serif;
                    direction: rtl;
                    padding: 0;
                    margin: 0;
                    background: white;
                    color: #333;
                    line-height: 1.6;
                }

                .pdf-container {
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 20px;
                }

                .header {
                    text-align: center;
                    margin-bottom: 50px;
                    border-bottom: 4px solid ${color};
                    padding-bottom: 30px;
                    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                    border-radius: 12px 12px 0 0;
                    padding: 40px;
                }

                .main-title {
                    font-size: 32px;
                    font-weight: bold;
                    color: ${color};
                    margin-bottom: 15px;
                    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
                }

                .subtitle {
                    font-size: 18px;
                    color: #666;
                    font-weight: 500;
                }

                .card {
                    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
                    border: 3px solid ${color};
                    border-radius: 16px;
                    padding: 60px;
                    margin: 40px 0;
                    text-align: center;
                    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
                    position: relative;
                    overflow: hidden;
                }

                .card::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 8px;
                    background: linear-gradient(90deg, ${color}, ${color}aa);
                }

                .value {
                    font-size: 72px;
                    font-weight: bold;
                    color: ${color};
                    margin-bottom: 30px;
                    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
                    display: block;
                }

                .card-title {
                    font-size: 28px;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 20px;
                    border-bottom: 2px solid #eee;
                    padding-bottom: 15px;
                }

                .card-description {
                    font-size: 18px;
                    color: #666;
                    line-height: 1.8;
                    font-style: italic;
                }

                .footer {
                    margin-top: 60px;
                    text-align: center;
                    font-size: 14px;
                    color: #999;
                    border-top: 2px solid #dee2e6;
                    padding-top: 30px;
                    background: #f8f9fa;
                    border-radius: 8px;
                    padding: 30px;
                }

                .footer-info {
                    margin: 10px 0;
                    font-weight: 500;
                }

                .footer-org {
                    font-weight: bold;
                    color: ${color};
                    font-size: 16px;
                    margin-top: 20px;
                }

                .stats-summary {
                    background: #f8f9fa;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    padding: 20px;
                    margin: 30px 0;
                    text-align: right;
                }

                .stats-summary h3 {
                    color: ${color};
                    margin-bottom: 15px;
                    font-size: 20px;
                }

                @media print {
                    body { padding: 0; margin: 0; }
                    .pdf-container { padding: 10px; }
                    .header { margin-bottom: 30px; padding: 20px; }
                    .card { padding: 40px; margin: 20px 0; }
                    .footer { padding: 20px; }
                }
            </style>
        </head>
        <body>
            <div class="pdf-container">
                <div class="header">
                    <div class="main-title">🏛️ نظام إدارة الاجتماعات</div>
                    <div class="subtitle">📄 تقرير PDF مفصل - ${title}</div>
                </div>

                <div class="card">
                    <div class="value">${value}</div>
                    <div class="card-title">${title}</div>
                    <div class="card-description">${description}</div>
                </div>

                <div class="stats-summary">
                    <h3>📊 ملخص الإحصائية</h3>
                    <p><strong>النوع:</strong> ${title}</p>
                    <p><strong>القيمة:</strong> ${value}</p>
                    <p><strong>الوصف:</strong> ${description}</p>
                    <p><strong>تاريخ الإنشاء:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
                    <p><strong>وقت الإنشاء:</strong> ${new Date().toLocaleTimeString('ar-SA')}</p>
                </div>

                <div class="footer">
                    <div class="footer-info"><strong>📅 تاريخ التصدير:</strong> ${new Date().toLocaleDateString('ar-SA', { year: 'numeric', month: 'long', day: 'numeric' })}</div>
                    <div class="footer-info"><strong>🕐 وقت التصدير:</strong> ${new Date().toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })}</div>
                    <div class="footer-info"><strong>👤 المستخدم:</strong> مدير النظام</div>
                    <div class="footer-info"><strong>📄 نوع التقرير:</strong> تقرير إحصائي فردي</div>
                    <div class="footer-org">القوات المسلحة الأردنية - نظام إدارة الاجتماعات</div>
                </div>
            </div>
        </body>
        </html>
    `;

    // فتح نافذة PDF جديدة
    const pdfWindow = window.open('', '_blank');
    pdfWindow.document.write(pdfHTML);
    pdfWindow.document.close();

    // انتظار قصير ثم فتح حوار الطباعة (سيتم حفظه كـ PDF)
    setTimeout(() => {
        pdfWindow.print();
        // لا نغلق النافذة تلقائياً للسماح للمستخدم بحفظ PDF
    }, 1000);
}

console.log('✅ تم تحميل حل طباعة البطاقات المحسن بنجاح');
