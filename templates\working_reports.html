<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقارير الاجتماعات - يعمل مضمون</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container { 
            background: white; 
            border-radius: 20px; 
            padding: 30px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.2); 
            max-width: 1200px;
        }
        .page-title { 
            background: linear-gradient(135deg, #2c3e50, #34495e); 
            color: white; 
            padding: 25px; 
            border-radius: 15px; 
            margin-bottom: 30px; 
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .stats-card { 
            background: linear-gradient(135deg, #f8f9fa, #e9ecef); 
            border-radius: 15px; 
            padding: 30px; 
            margin: 15px 0; 
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-left: 6px solid #007bff;
            transition: transform 0.3s ease;
            text-align: center;
        }
        .stats-card:hover { transform: translateY(-5px); }
        .stat-number { 
            font-size: 4rem; 
            font-weight: bold; 
            color: #2c3e50; 
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 10px;
        }
        .stat-label { 
            color: #6c757d; 
            font-size: 1.3rem; 
            font-weight: 600; 
        }
        .success { border-left-color: #28a745; }
        .success .stat-number { color: #28a745; }
        .info { border-left-color: #17a2b8; }
        .info .stat-number { color: #17a2b8; }
        .danger { border-left-color: #dc3545; }
        .danger .stat-number { color: #dc3545; }
        .warning { border-left-color: #ffc107; }
        .warning .stat-number { color: #e0a800; }
        
        .year-controls {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-radius: 15px;
            border: 2px solid #ffc107;
        }
        .year-btn {
            background: linear-gradient(135deg, #b8860b, #daa520);
            color: white;
            border: none;
            padding: 12px 25px;
            margin: 5px;
            border-radius: 10px;
            font-weight: bold;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .year-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(184, 134, 11, 0.4);
            color: white;
            text-decoration: none;
        }
        .year-btn.active {
            background: linear-gradient(135deg, #28a745, #20c997);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }
        
        .meetings-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin: 30px 0;
        }
        .table-header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .table th {
            background: linear-gradient(135deg, #495057, #6c757d);
            color: white;
            font-weight: bold;
            text-align: center;
            padding: 15px;
            border: none;
        }
        .table td {
            text-align: center;
            padding: 15px;
            vertical-align: middle;
            border-color: #dee2e6;
        }
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        .badge-custom {
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }
        .print-section {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-radius: 15px;
            border: 2px solid #2196f3;
        }
        .btn-print {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 10px;
            font-weight: bold;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-print:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }
        .btn-pdf {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 10px;
            font-weight: bold;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-pdf:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
        }
        @media print {
            body { background: white !important; }
            .year-controls, .print-section { display: none !important; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-title">
            <h1><i class="fas fa-chart-bar me-3"></i>تقارير الاجتماعات</h1>
            <h4>القوات المسلحة الأردنية - مديرية الدائرة المالية</h4>
            <p class="mb-0">تقرير شامل لجميع الاجتماعات حسب السنة المختارة</p>
        </div>

        <!-- اختيار السنة -->
        <div class="year-controls">
            <h5><i class="fas fa-calendar-year me-2"></i>اختر السنة لعرض التقرير:</h5>
            <a href="/working-reports/2024" class="year-btn {{ 'active' if selected_year == 2024 else '' }}">
                📅 2024
            </a>
            <a href="/working-reports/2025" class="year-btn {{ 'active' if selected_year == 2025 else '' }}">
                📅 2025
            </a>
            <a href="/working-reports/2026" class="year-btn {{ 'active' if selected_year == 2026 else '' }}">
                📅 2026
            </a>
            <a href="/working-reports/2027" class="year-btn {{ 'active' if selected_year == 2027 else '' }}">
                📅 2027
            </a>
            <a href="/working-reports/2028" class="year-btn {{ 'active' if selected_year == 2028 else '' }}">
                📅 2028
            </a>
            <a href="/working-reports/2029" class="year-btn {{ 'active' if selected_year == 2029 else '' }}">
                📅 2029
            </a>
            <a href="/working-reports/2030" class="year-btn {{ 'active' if selected_year == 2030 else '' }}">
                📅 2030
            </a>
        </div>

        <!-- البطاقات الإحصائية -->
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stat-number">{{ total_meetings }}</div>
                    <div class="stat-label">
                        <i class="fas fa-clipboard-list me-2"></i>
                        إجمالي الاجتماعات
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card success">
                    <div class="stat-number">{{ completed_meetings }}</div>
                    <div class="stat-label">
                        <i class="fas fa-check-circle me-2"></i>
                        اجتماعات مكتملة
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card info">
                    <div class="stat-number">{{ upcoming_meetings }}</div>
                    <div class="stat-label">
                        <i class="fas fa-clock me-2"></i>
                        اجتماعات قادمة
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card danger">
                    <div class="stat-number">{{ cancelled_meetings }}</div>
                    <div class="stat-label">
                        <i class="fas fa-times-circle me-2"></i>
                        اجتماعات ملغاة
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول الاجتماعات -->
        <div class="meetings-table">
            <div class="table-header">
                <h4><i class="fas fa-list me-2"></i>تفاصيل اجتماعات سنة {{ selected_year }}</h4>
            </div>
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th><i class="fas fa-hashtag me-1"></i>م</th>
                        <th><i class="fas fa-file-alt me-1"></i>عنوان الاجتماع</th>
                        <th><i class="fas fa-calendar me-1"></i>التاريخ</th>
                        <th><i class="fas fa-clock me-1"></i>الوقت</th>
                        <th><i class="fas fa-map-marker-alt me-1"></i>الموقع (GPS)</th>
                        <th><i class="fas fa-info-circle me-1"></i>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    {% if meetings %}
                        {% for meeting in meetings %}
                        <tr>
                            <td><strong>{{ loop.index }}</strong></td>
                            <td><strong>{{ meeting.subject }}</strong></td>
                            <td>{{ meeting.meeting_date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ meeting.meeting_time | military_time if meeting.meeting_time else 'غير محدد' }}</td>
                            <td>{{ meeting.location }}</td>
                            <td>
                                {% if meeting.is_cancelled %}
                                    <span class="badge bg-danger badge-custom">ملغي</span>
                                {% elif meeting.meeting_date < today %}
                                    <span class="badge bg-success badge-custom">مكتمل</span>
                                {% else %}
                                    <span class="badge bg-primary badge-custom">قادم</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="6" class="text-center text-muted py-5">
                                <i class="fas fa-calendar-times fa-3x mb-3 d-block"></i>
                                <h5>لا توجد اجتماعات في سنة {{ selected_year }}</h5>
                                <p>جرب اختيار سنة أخرى من الأزرار أعلاه</p>
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>

        <!-- أزرار الطباعة -->
        <div class="print-section">
            <h5><i class="fas fa-print me-2"></i>طباعة وتصدير التقرير</h5>
            <button class="btn-print" onclick="window.print()">
                <i class="fas fa-print me-2"></i>طباعة التقرير
            </button>
            <button class="btn-pdf" onclick="exportToPDF()">
                <i class="fas fa-file-pdf me-2"></i>تصدير PDF
            </button>
        </div>
    </div>

    <script>
        function exportToPDF() {
            alert('💡 لحفظ التقرير كـ PDF:\n\n1. اضغط Ctrl+P (أو Cmd+P على Mac)\n2. اختر "حفظ كـ PDF" من قائمة الطابعات\n3. اضغط "حفظ"');
            setTimeout(() => window.print(), 1000);
        }
    </script>
</body>
</html>
