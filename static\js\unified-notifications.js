/**
 * نظام الإشعارات الموحد - ذهبي شفاف
 * نظام إدارة المواعيد - القوات المسلحة الأردنية
 */

// نظام الإشعارات الموحد
window.UnifiedNotifications = {
    // عرض إشعار نجاح
    showSuccess: function(message, duration = 3000) {
        this.showNotification(message, 'success', duration);
    },
    
    // عرض إشعار خطأ
    showError: function(message, duration = 4000) {
        this.showNotification(message, 'error', duration);
    },
    
    // عرض إشعار معلومات
    showInfo: function(message, duration = 3000) {
        this.showNotification(message, 'info', duration);
    },
    
    // عرض إشعار تحذير
    showWarning: function(message, duration = 3500) {
        this.showNotification(message, 'warning', duration);
    },
    
    // الوظيفة الرئيسية لعرض الإشعارات - مفعلة
    showNotification: function(message, type = 'success', duration = 3000) {
        try {
            console.log('📢 عرض إشعار:', message, type);

            // إزالة الإشعارات السابقة
            const existingNotifications = document.querySelectorAll('.unified-notification');
            existingNotifications.forEach(notification => notification.remove());

            // إنشاء الإشعار
            const notification = document.createElement('div');
            notification.className = 'unified-notification';

            // تحديد لون الخلفية حسب النوع
            let bgColor = '#28a745'; // أخضر للنجاح
            let icon = '✅';

            switch(type) {
                case 'error':
                    bgColor = '#dc3545'; // أحمر للخطأ
                    icon = '❌';
                    break;
                case 'warning':
                    bgColor = '#ffc107'; // أصفر للتحذير
                    icon = '⚠️';
                    break;
                case 'info':
                    bgColor = '#17a2b8'; // أزرق للمعلومات
                    icon = 'ℹ️';
                    break;
                default:
                    bgColor = '#28a745'; // أخضر للنجاح
                    icon = '✅';
            }

            // محتوى الإشعار
            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="font-size: 18px;">${icon}</span>
                    <span style="flex: 1; font-weight: 500;">${message}</span>
                </div>
            `;

            // تنسيق الإشعار
            notification.style.cssText = `
                position: fixed !important;
                top: 20px !important;
                left: 20px !important;
                background: ${bgColor} !important;
                color: white !important;
                padding: 16px 20px !important;
                border-radius: 10px !important;
                box-shadow: 0 6px 20px rgba(0,0,0,0.15) !important;
                z-index: 99999 !important;
                min-width: 320px !important;
                max-width: 450px !important;
                font-size: 14px !important;
                direction: rtl !important;
                text-align: right !important;
                opacity: 0 !important;
                transform: translateX(-100px) !important;
                transition: all 0.3s ease !important;
                border: 2px solid rgba(255,255,255,0.2) !important;
                display: block !important;
                visibility: visible !important;
                font-family: 'Cairo', 'Tahoma', Arial, sans-serif !important;
            `;

            // إضافة الإشعار للصفحة
            document.body.appendChild(notification);

            // تأثير الظهور
            setTimeout(() => {
                notification.style.opacity = '1';
                notification.style.transform = 'translateX(0)';
            }, 10);

            // إزالة تلقائية
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.opacity = '0';
                    notification.style.transform = 'translateX(-100px)';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.remove();
                        }
                    }, 300);
                }
            }, duration);

        } catch (error) {
            console.error('خطأ في عرض الإشعار:', error);
            // fallback للإشعارات البسيطة
            alert(message);
        }
    },


};

// إتاحة النظام عالمياً
window.showNotification = window.UnifiedNotifications.showNotification.bind(window.UnifiedNotifications);
window.showSuccessNotification = window.UnifiedNotifications.showSuccess.bind(window.UnifiedNotifications);
window.showErrorNotification = window.UnifiedNotifications.showError.bind(window.UnifiedNotifications);
window.showInfoNotification = window.UnifiedNotifications.showInfo.bind(window.UnifiedNotifications);
window.showWarningNotification = window.UnifiedNotifications.showWarning.bind(window.UnifiedNotifications);

// للتوافق مع الأنظمة القديمة
window.NotificationUtils = window.UnifiedNotifications;

console.log('✅ تم تحميل نظام الإشعارات الموحد');
