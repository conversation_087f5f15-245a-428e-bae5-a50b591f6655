/**
 * أنماط الطباعة الموحدة لجميع التقارير
 * نظام إدارة المواعيد - القوات المسلحة الأردنية
 */

/* متغيرات الألوان الموحدة */
:root {
    --primary-color: #1B4332;
    --secondary-color: #2D5A3D;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 12px;
    --shadow: 0 4px 12px rgba(0,0,0,0.1);
}

/* إعدادات الطباعة العامة */
@media print {
    @page {
        size: A4;
        margin: 0.5cm;
        direction: rtl;
    }

    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    body {
        font-family: 'Cairo', 'Tahoma', 'Arial', sans-serif !important;
        font-size: 10pt !important;
        line-height: 1.4 !important;
        color: #333 !important;
        background: white !important;
        direction: rtl !important;
        text-align: right !important;
    }

    /* إخفاء العناصر غير المرغوب فيها */
    .no-print,
    .btn, .button,
    .navbar, .nav,
    .header, .top-header, .main-header,
    .sidebar, .side-nav,
    .footer, .bottom-footer,
    .breadcrumb,
    .alert, .toast, .modal,
    .dropdown, .tooltip, .popover,
    .back-to-top, .scroll-top,
    .social-links,
    .pagination,
    .form-control, .form-group,
    input, select, textarea,
    .card-header.bg-warning,
    .card-header.bg-info,
    .modern-back-btn,
    .print-btn,
    .action-btn {
        display: none !important;
    }

    /* إظهار محتوى الطباعة فقط */
    .print-only {
        display: block !important;
    }

    .print-container {
        display: block !important;
        width: 100% !important;
        max-width: none !important;
        margin: 0 !important;
        padding: 0 !important;
        background: white !important;
        box-shadow: none !important;
        border: none !important;
    }
}

/* أنماط الطباعة للشاشة (معاينة) */
.print-preview {
    background: white;
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin: 20px auto;
    max-width: 210mm;
    min-height: 297mm;
    padding: 20mm;
    font-family: 'Cairo', 'Tahoma', 'Arial', sans-serif;
    direction: rtl;
    text-align: right;
}

/* هيدر الطباعة الموحد - بدون شعار */
.unified-print-header {
    text-align: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--primary-color);
    background: linear-gradient(135deg, var(--light-color) 0%, #e9ecef 100%);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    padding: 10px;
}

.unified-print-header .main-title {
    font-size: 18px;
    font-weight: bold;
    color: var(--primary-color);
    margin: 10px 0 5px 0;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
}

.unified-print-header .sub-title {
    font-size: 12px;
    color: var(--secondary-color);
    font-weight: 500;
    margin: 0;
}

/* عنوان تفاصيل الاجتماع */
.meeting-details-header {
    margin: 15px 0 10px 0;
    text-align: center;
}

.details-title {
    font-size: 18px;
    font-weight: bold;
    color: var(--primary-color);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    border-bottom: 1px solid var(--primary-color);
    display: inline-block;
    padding-bottom: 3px;
}

/* محتوى الطباعة */
.unified-print-content {
    margin: 25px 0;
}

.unified-detail-row {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px;
    background: var(--light-color);
    border-radius: 8px;
    border-right: 3px solid var(--primary-color);
    box-shadow: 0 1px 3px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

/* تنسيق خاص للعناصر المؤطرة */
.boxed-detail {
    border: 2px solid var(--primary-color);
    background-color: #f8f9fa;
    box-shadow: 0 3px 8px rgba(0,0,0,0.15);
}

.unified-detail-row:hover {
    transform: translateX(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.12);
}

.unified-detail-icon {
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 50%;
    margin-left: 12px;
    font-size: 14px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid var(--light-color);
}

.unified-detail-content {
    flex: 1;
}

.unified-detail-label {
    font-weight: bold;
    color: var(--dark-color);
    margin-bottom: 2px;
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.unified-detail-value {
    color: #555;
    font-size: 12px;
    line-height: 1.3;
    word-wrap: break-word;
}

/* تذييل الطباعة المحدث */
.unified-print-footer {
    margin-top: 40px;
    border-top: 2px solid var(--primary-color);
    padding-top: 15px;
    position: absolute;
    bottom: 15px;
    left: 20px;
    right: 20px;
}

.unified-footer-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 11px;
    color: #666;
    width: 100%;
}

.unified-footer-info .footer-left {
    flex: 1;
    text-align: left;
}

.unified-footer-info .footer-center {
    flex: 1;
    text-align: center;
    font-weight: bold;
    color: var(--primary-color);
}

.unified-footer-info .footer-right {
    flex: 1;
    text-align: right;
}

/* أنماط خاصة للإحصائيات */
.stat-print-value {
    font-size: 28px !important;
    font-weight: bold !important;
    color: var(--primary-color) !important;
    text-align: center !important;
    margin: 10px 0 !important;
}

.stat-print-description {
    font-size: 12px !important;
    color: #666 !important;
    text-align: center !important;
    font-style: italic !important;
}

/* أنماط الجداول للطباعة */
.unified-print-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    font-size: 11px;
    direction: rtl;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
}

.unified-print-table th {
    background: var(--primary-color);
    color: white;
    font-weight: bold;
    padding: 12px 8px;
    text-align: center;
    border: 1px solid var(--primary-color);
}

.unified-print-table td {
    padding: 10px 8px;
    border: 1px solid #ddd;
    text-align: right;
    background: white;
}

.unified-print-table tr:nth-child(even) td {
    background: var(--light-color);
}

.unified-print-table tr:hover td {
    background: #e3f2fd;
}

/* أنماط متجاوبة للطباعة */
@media print and (max-width: 210mm) {
    .unified-footer-info {
        flex-direction: column;
        gap: 8px;
    }

    .unified-footer-info .footer-left,
    .unified-footer-info .footer-center,
    .unified-footer-info .footer-right {
        text-align: center;
    }

    .unified-detail-row {
        flex-direction: column;
        text-align: center;
    }

    .unified-detail-icon {
        margin: 0 0 10px 0;
    }
}

/* أنماط للألوان المختلفة */
.color-success .unified-detail-icon { color: var(--success-color); }
.color-info .unified-detail-icon { color: var(--info-color); }
.color-warning .unified-detail-icon { color: var(--warning-color); }
.color-danger .unified-detail-icon { color: var(--danger-color); }

.color-success .unified-detail-row { border-right-color: var(--success-color); }
.color-info .unified-detail-row { border-right-color: var(--info-color); }
.color-warning .unified-detail-row { border-right-color: var(--warning-color); }
.color-danger .unified-detail-row { border-right-color: var(--danger-color); }

/* تحسينات إضافية */
.print-page-break {
    page-break-before: always;
}

.print-no-break {
    page-break-inside: avoid;
}

.print-signature-area {
    margin-top: 50px;
    border-top: 1px solid #ccc;
    padding-top: 20px;
    text-align: left;
}

.print-signature-line {
    border-bottom: 1px solid #333;
    width: 200px;
    margin: 20px 0 5px 0;
    display: inline-block;
}

.print-signature-label {
    font-size: 10px;
    color: #666;
    margin-right: 10px;
}
