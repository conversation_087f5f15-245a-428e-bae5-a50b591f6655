{% extends "base.html" %}

{% block title %}عرض المرفق - {{ attachment.original_filename }}{% endblock %}

{% block extra_css %}
<style>
    :root {
        --jaf-primary: #1b4332;
        --jaf-secondary: #2d5a3d;
        --jaf-success: #40916c;
        --jaf-warning: #f39c12;
        --jaf-gradient: linear-gradient(135deg, var(--jaf-primary), var(--jaf-secondary));
    }

    .attachment-viewer {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        overflow: hidden;
        margin: 2rem 0;
    }

    .attachment-header {
        background: var(--jaf-gradient);
        color: white;
        padding: 2rem;
        text-align: center;
    }

    .attachment-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.9;
    }

    .attachment-info {
        padding: 2rem;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 600;
        color: var(--jaf-primary);
    }

    .info-value {
        color: #6c757d;
    }

    .action-buttons {
        padding: 2rem;
        background: #f8f9fa;
        text-align: center;
    }

    .btn-action {
        margin: 0 0.5rem;
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .file-preview {
        max-width: 100%;
        max-height: 600px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="fw-bold text-primary mb-1">
                <i class="fas fa-file-alt me-2"></i>
                عرض المرفق
            </h2>
            <p class="text-muted mb-0">{{ attachment.original_filename }}</p>
        </div>
        <div>
            <a href="{{ url_for('meeting_details', meeting_id=attachment.meeting_id) }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للاجتماع
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="attachment-viewer">
                <!-- Header -->
                <div class="attachment-header">
                    {% if attachment.file_type.startswith('image/') %}
                        <i class="fas fa-image attachment-icon"></i>
                    {% elif attachment.file_type == 'application/pdf' %}
                        <i class="fas fa-file-pdf attachment-icon"></i>
                    {% elif attachment.file_type.startswith('application/vnd.ms-') or attachment.file_type.startswith('application/vnd.openxmlformats-') %}
                        <i class="fas fa-file-word attachment-icon"></i>
                    {% else %}
                        <i class="fas fa-file attachment-icon"></i>
                    {% endif %}
                    <h3 class="mb-2">{{ attachment.original_filename }}</h3>
                    <p class="mb-0 opacity-75">{{ attachment.file_type }}</p>
                </div>

                <!-- File Info -->
                <div class="attachment-info">
                    <div class="info-item">
                        <span class="info-label">
                            <i class="fas fa-file me-2"></i>
                            اسم الملف
                        </span>
                        <span class="info-value">{{ attachment.original_filename }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">
                            <i class="fas fa-hdd me-2"></i>
                            حجم الملف
                        </span>
                        <span class="info-value">{{ "%.1f"|format(attachment.file_size / 1024) }} KB</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">
                            <i class="fas fa-calendar me-2"></i>
                            تاريخ الرفع
                        </span>
                        <span class="info-value">{{ attachment.uploaded_at.strftime('%Y-%m-%d %H:%M') }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">
                            <i class="fas fa-user me-2"></i>
                            رفع بواسطة
                        </span>
                        <span class="info-value">{{ attachment.uploader.username if attachment.uploader else 'غير معروف' }}</span>
                    </div>
                </div>

                <!-- Preview for images -->
                {% if attachment.file_type.startswith('image/') %}
                <div class="text-center p-4">
                    <img src="{{ url_for('static', filename=attachment.file_path.replace('static/', '')) }}" 
                         alt="{{ attachment.original_filename }}" 
                         class="file-preview">
                </div>
                {% endif %}

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <a href="{{ url_for('download_attachment', attachment_id=attachment.id) }}" 
                       class="btn btn-primary btn-action">
                        <i class="fas fa-download me-2"></i>
                        تحميل الملف
                    </a>
                    
                    {% if attachment.file_type == 'application/pdf' %}
                    <a href="{{ url_for('view_attachment', attachment_id=attachment.id) }}" 
                       class="btn btn-info btn-action" target="_blank">
                        <i class="fas fa-external-link-alt me-2"></i>
                        فتح في نافذة جديدة
                    </a>
                    {% endif %}
                    
                    {% if current_user.id == attachment.uploaded_by %}
                    <a href="{{ url_for('delete_attachment', attachment_id=attachment.id) }}" 
                       class="btn btn-danger btn-action"
                       onclick="return confirm('هل تريد حذف هذا المرفق نهائياً؟')">
                        <i class="fas fa-trash me-2"></i>
                        حذف الملف
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // تأثير hover للأزرار
    const buttons = document.querySelectorAll('.btn-action');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // تحسين عرض الصور
    const images = document.querySelectorAll('.file-preview');
    images.forEach(img => {
        img.addEventListener('click', function() {
            // فتح الصورة في نافذة جديدة للعرض بحجم كامل
            window.open(this.src, '_blank');
        });
        
        img.style.cursor = 'pointer';
        img.title = 'انقر للعرض بحجم كامل';
    });
});
</script>
{% endblock %}
