/**
 * تحسينات منتقي التاريخ للنظام
 * Date Picker Enhancement for JAF Meeting System
 */

document.addEventListener('DOMContentLoaded', function() {
    // تحسين حقول التاريخ
    const dateInputs = document.querySelectorAll('input[type="date"]');
    
    dateInputs.forEach(function(input) {
        // إضافة تنسيق عربي للتاريخ
        input.addEventListener('change', function() {
            if (this.value) {
                const date = new Date(this.value);
                const arabicDate = date.toLocaleDateString('ar-JO');
                
                // إضافة تلميح بالتاريخ العربي
                this.title = `التاريخ: ${arabicDate}`;
            }
        });
        
        // تحسين المظهر
        input.style.direction = 'ltr';
        input.style.textAlign = 'center';
    });
    
    // تحسين حقول الوقت
    const timeInputs = document.querySelectorAll('input[type="time"]');
    
    timeInputs.forEach(function(input) {
        input.style.direction = 'ltr';
        input.style.textAlign = 'center';
        
        // عرض الوقت بالنظام العسكري فوراً
        input.addEventListener('change', function() {
            if (this.value) {
                const [hours, minutes] = this.value.split(':');
                const militaryTime = `${hours.padStart(2, '0')}${minutes.padStart(2, '0')}`;

                // إنشاء عنصر لعرض الوقت العسكري
                let militaryDisplay = this.parentNode.querySelector('.military-time-display');
                if (!militaryDisplay) {
                    militaryDisplay = document.createElement('div');
                    militaryDisplay.className = 'military-time-display';
                    militaryDisplay.style.cssText = `
                        position: absolute;
                        top: 50%;
                        right: 40px;
                        transform: translateY(-50%);
                        background: #1B4332;
                        color: white;
                        padding: 2px 8px;
                        border-radius: 4px;
                        font-size: 12px;
                        font-weight: bold;
                        z-index: 10;
                        pointer-events: none;
                    `;
                    this.parentNode.style.position = 'relative';
                    this.parentNode.appendChild(militaryDisplay);
                }

                militaryDisplay.textContent = militaryTime;
                militaryDisplay.style.display = 'block';
            } else {
                // إخفاء العرض إذا لم يكن هناك وقت
                const militaryDisplay = this.parentNode.querySelector('.military-time-display');
                if (militaryDisplay) {
                    militaryDisplay.style.display = 'none';
                }
            }
        });

        // عرض الوقت العسكري أثناء الكتابة أيضاً
        input.addEventListener('input', function() {
            if (this.value && this.value.includes(':')) {
                const [hours, minutes] = this.value.split(':');
                if (hours && minutes && hours.length === 2 && minutes.length === 2) {
                    const militaryTime = `${hours.padStart(2, '0')}${minutes.padStart(2, '0')}`;

                    let militaryDisplay = this.parentNode.querySelector('.military-time-display');
                    if (!militaryDisplay) {
                        militaryDisplay = document.createElement('div');
                        militaryDisplay.className = 'military-time-display';
                        militaryDisplay.style.cssText = `
                            position: absolute;
                            top: 50%;
                            right: 40px;
                            transform: translateY(-50%);
                            background: #1B4332;
                            color: white;
                            padding: 2px 8px;
                            border-radius: 4px;
                            font-size: 12px;
                            font-weight: bold;
                            z-index: 10;
                            pointer-events: none;
                        `;
                        this.parentNode.style.position = 'relative';
                        this.parentNode.appendChild(militaryDisplay);
                    }

                    militaryDisplay.textContent = militaryTime;
                    militaryDisplay.style.display = 'block';
                }
            }
        });
    });
});

// دالة لتحويل التاريخ إلى العربية
function formatDateToArabic(dateString) {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-JO', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// دالة لتحويل الوقت إلى النظام العسكري
function formatTimeToMilitary(timeString) {
    if (!timeString) return '';

    const [hours, minutes] = timeString.split(':');
    return `${hours.padStart(2, '0')}${minutes.padStart(2, '0')}`;
}
