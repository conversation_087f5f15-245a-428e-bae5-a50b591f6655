/**
 * تحسينات منتقي التاريخ للنظام
 * Date Picker Enhancement for JAF Meeting System
 */

document.addEventListener('DOMContentLoaded', function() {
    // تحسين حقول التاريخ
    const dateInputs = document.querySelectorAll('input[type="date"]');
    
    dateInputs.forEach(function(input) {
        // إضافة تنسيق عربي للتاريخ
        input.addEventListener('change', function() {
            if (this.value) {
                const date = new Date(this.value);
                const arabicDate = date.toLocaleDateString('ar-JO');
                
                // إضافة تلميح بالتاريخ العربي
                this.title = `التاريخ: ${arabicDate}`;
            }
        });
        
        // تحسين المظهر
        input.style.direction = 'ltr';
        input.style.textAlign = 'center';
    });
    
    // تحسين حقول الوقت
    const timeInputs = document.querySelectorAll('input[type="time"]');
    
    timeInputs.forEach(function(input) {
        input.style.direction = 'ltr';
        input.style.textAlign = 'center';
        
        // إضافة تلميح بالوقت العسكري
        input.addEventListener('change', function() {
            if (this.value) {
                const [hours, minutes] = this.value.split(':');
                const militaryTime = `${hours.padStart(2, '0')}${minutes.padStart(2, '0')}`;
                this.title = `النظام العسكري: ${militaryTime}`;

                // إضافة تلميح بصري
                const existingHint = this.parentNode.querySelector('.military-time-hint');
                if (existingHint) {
                    existingHint.remove();
                }

                const hint = document.createElement('small');
                hint.className = 'military-time-hint text-muted d-block mt-1';
                hint.innerHTML = `<i class="fas fa-info-circle me-1"></i>النظام العسكري: ${militaryTime}`;
                this.parentNode.appendChild(hint);
            }
        });
    });
});

// دالة لتحويل التاريخ إلى العربية
function formatDateToArabic(dateString) {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-JO', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// دالة لتحويل الوقت إلى النظام العسكري
function formatTimeToMilitary(timeString) {
    if (!timeString) return '';

    const [hours, minutes] = timeString.split(':');
    return `${hours.padStart(2, '0')}${minutes.padStart(2, '0')}`;
}
