/**
 * تحسينات منتقي التاريخ للنظام
 * Date Picker Enhancement for JAF Meeting System
 */

document.addEventListener('DOMContentLoaded', function() {
    // تحسين حقول التاريخ
    const dateInputs = document.querySelectorAll('input[type="date"]');
    
    dateInputs.forEach(function(input) {
        // إضافة تنسيق عربي للتاريخ
        input.addEventListener('change', function() {
            if (this.value) {
                const date = new Date(this.value);
                const arabicDate = date.toLocaleDateString('ar-JO');
                
                // إضافة تلميح بالتاريخ العربي
                this.title = `التاريخ: ${arabicDate}`;
            }
        });
        
        // تحسين المظهر
        input.style.direction = 'ltr';
        input.style.textAlign = 'center';
    });
    
    // تحسين حقول الوقت
    const timeInputs = document.querySelectorAll('input[type="time"]');
    
    timeInputs.forEach(function(input) {
        input.style.direction = 'ltr';
        input.style.textAlign = 'center';
        
        // عرض الوقت بالنظام العسكري داخل حقل الإدخال
        input.addEventListener('change', function() {
            updateTimeDisplay(this);
        });

        input.addEventListener('input', function() {
            updateTimeDisplay(this);
        });

        // دالة لتحديث عرض الوقت
        function updateTimeDisplay(timeInput) {
            if (timeInput.value && timeInput.value.includes(':')) {
                const [hours, minutes] = timeInput.value.split(':');
                if (hours && minutes && hours.length === 2 && minutes.length === 2) {
                    const militaryTime = `${hours.padStart(2, '0')}${minutes.padStart(2, '0')}`;

                    // تغيير قيمة الحقل لعرض الوقت العسكري
                    timeInput.setAttribute('data-original-value', timeInput.value);
                    timeInput.style.color = '#1B4332';
                    timeInput.style.fontWeight = 'bold';

                    // إنشاء placeholder مخصص
                    if (!timeInput.hasAttribute('data-military-mode')) {
                        timeInput.setAttribute('data-military-mode', 'true');

                        // إضافة مستمع للتركيز لإظهار الوقت الأصلي
                        timeInput.addEventListener('focus', function() {
                            if (this.getAttribute('data-original-value')) {
                                this.value = this.getAttribute('data-original-value');
                                this.style.color = '';
                                this.style.fontWeight = '';
                            }
                        });

                        // إضافة مستمع لفقدان التركيز لإظهار الوقت العسكري
                        timeInput.addEventListener('blur', function() {
                            updateTimeDisplay(this);
                        });
                    }

                    // عرض الوقت العسكري عند عدم التركيز
                    if (document.activeElement !== timeInput) {
                        timeInput.value = militaryTime;
                    }
                }
            }
        }
    });
});

// دالة لتحويل التاريخ إلى العربية
function formatDateToArabic(dateString) {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-JO', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// دالة لتحويل الوقت إلى النظام العسكري
function formatTimeToMilitary(timeString) {
    if (!timeString) return '';

    const [hours, minutes] = timeString.split(':');
    return `${hours.padStart(2, '0')}${minutes.padStart(2, '0')}`;
}
