#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
تطبيق تقارير بسيط ومضمون
"""

from flask import Flask, render_template_string, jsonify
import sqlite3
import os
from datetime import datetime

app = Flask(__name__)

# HTML للتقارير
REPORTS_HTML = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقارير الاجتماعات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f8f9fa; }
        .stats-card { background: white; border-radius: 15px; padding: 20px; margin: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .stat-number { font-size: 3rem; font-weight: bold; color: #2c3e50; }
        .stat-label { color: #6c757d; font-size: 1.1rem; }
        .year-select { background: linear-gradient(135deg, #b8860b, #daa520); color: white; border: none; padding: 10px 20px; border-radius: 10px; font-weight: bold; }
        .btn-print { background: linear-gradient(135deg, #28a745, #20c997); color: white; border: none; padding: 10px 20px; border-radius: 8px; margin: 5px; }
        .btn-pdf { background: linear-gradient(135deg, #dc3545, #c82333); color: white; border: none; padding: 10px 20px; border-radius: 8px; margin: 5px; }
        .monthly-table { width: 100%; margin: 20px 0; }
        .monthly-table th, .monthly-table td { padding: 12px; text-align: center; border: 1px solid #dee2e6; }
        .monthly-table th { background: #f8f9fa; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="text-center mb-4">
            <h1>🏛️ تقارير الاجتماعات</h1>
            <h3>القوات المسلحة الأردنية - مديرية الدائرة المالية</h3>
        </div>
        
        <div class="text-center mb-4">
            <label for="yearSelect" class="me-2"><strong>السنة:</strong></label>
            <select id="yearSelect" class="year-select" onchange="loadData()">
                <option value="2024">📅 2024</option>
                <option value="2025">📅 2025</option>
                <option value="2026">📅 2026</option>
                <option value="2027">📅 2027</option>
                <option value="2028">📅 2028</option>
                <option value="2029">📅 2029</option>
                <option value="2030">📅 2030</option>
            </select>
            <button class="btn-print" onclick="printReport()"><i class="fas fa-print"></i> طباعة</button>
            <button class="btn-pdf" onclick="exportPDF()"><i class="fas fa-file-pdf"></i> PDF</button>
        </div>

        <!-- البطاقات الإحصائية -->
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stat-number" id="totalMeetings">0</div>
                    <div class="stat-label">إجمالي الاجتماعات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stat-number" id="completedMeetings">0</div>
                    <div class="stat-label">اجتماعات مكتملة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stat-number" id="upcomingMeetings">0</div>
                    <div class="stat-label">اجتماعات قادمة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stat-number" id="cancelledMeetings">0</div>
                    <div class="stat-label">اجتماعات ملغاة</div>
                </div>
            </div>
        </div>

        <!-- الجدول الشهري -->
        <div class="stats-card mt-4">
            <h4>📊 التفصيل الشهري</h4>
            <table class="monthly-table table">
                <thead>
                    <tr>
                        <th>الشهر</th>
                        <th>مكتملة</th>
                        <th>قادمة</th>
                        <th>ملغاة</th>
                        <th>المجموع</th>
                    </tr>
                </thead>
                <tbody id="monthlyTableBody">
                    <tr><td colspan="5">جاري التحميل...</td></tr>
                </tbody>
            </table>
        </div>

        <!-- رسالة الحالة -->
        <div id="statusMessage" class="alert alert-info mt-3" style="display: none;"></div>
    </div>

    <script>
        // تحميل البيانات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
        });

        // دالة تحميل البيانات
        async function loadData() {
            const year = document.getElementById('yearSelect').value;
            showStatus('جاري تحميل البيانات...', 'info');
            
            try {
                // جلب الإحصائيات العامة
                const statsResponse = await fetch('/api/simple-stats');
                const statsData = await statsResponse.json();
                
                if (statsData.success) {
                    document.getElementById('totalMeetings').textContent = statsData.total_meetings;
                    document.getElementById('completedMeetings').textContent = statsData.completed_meetings;
                    document.getElementById('upcomingMeetings').textContent = statsData.upcoming_meetings;
                    document.getElementById('cancelledMeetings').textContent = statsData.cancelled_meetings;
                }

                // جلب البيانات الشهرية
                const monthlyResponse = await fetch(`/api/simple-monthly/${year}`);
                const monthlyData = await monthlyResponse.json();
                
                if (monthlyData.success) {
                    updateMonthlyTable(monthlyData.months);
                    showStatus(`تم تحميل بيانات سنة ${year} بنجاح!`, 'success');
                } else {
                    showStatus('فشل في تحميل البيانات', 'danger');
                }
                
            } catch (error) {
                console.error('خطأ:', error);
                showStatus('حدث خطأ في تحميل البيانات', 'danger');
            }
        }

        // تحديث الجدول الشهري
        function updateMonthlyTable(months) {
            const tbody = document.getElementById('monthlyTableBody');
            let html = '';
            
            months.forEach(month => {
                html += `
                    <tr>
                        <td>${month.month}</td>
                        <td>${month.completed}</td>
                        <td>${month.upcoming}</td>
                        <td>${month.cancelled}</td>
                        <td>${month.total}</td>
                    </tr>
                `;
            });
            
            if (html === '') {
                html = '<tr><td colspan="5">لا توجد بيانات لهذه السنة</td></tr>';
            }
            
            tbody.innerHTML = html;
        }

        // إظهار رسالة الحالة
        function showStatus(message, type) {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.className = `alert alert-${type} mt-3`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
            
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 3000);
        }

        // طباعة التقرير
        function printReport() {
            window.print();
        }

        // تصدير PDF
        function exportPDF() {
            showStatus('اضغط Ctrl+P واختر "حفظ كـ PDF"', 'info');
            setTimeout(() => window.print(), 1000);
        }
    </script>
</body>
</html>
"""

@app.route('/simple-reports')
def simple_reports():
    return render_template_string(REPORTS_HTML)

@app.route('/api/simple-stats')
def get_simple_stats():
    try:
        db_path = 'instance/jaf_meetings.db'
        
        if not os.path.exists(db_path):
            return jsonify({'success': False, 'error': 'قاعدة البيانات غير موجودة'})
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # إجمالي الاجتماعات
        cursor.execute("SELECT COUNT(*) FROM meeting")
        total_meetings = cursor.fetchone()[0]
        
        # الاجتماعات المكتملة (تاريخها في الماضي وغير ملغاة)
        current_date = datetime.now().date()
        cursor.execute("SELECT COUNT(*) FROM meeting WHERE meeting_date < ? AND is_cancelled = 0", (current_date,))
        completed_meetings = cursor.fetchone()[0]
        
        # الاجتماعات القادمة (تاريخها في المستقبل وغير ملغاة)
        cursor.execute("SELECT COUNT(*) FROM meeting WHERE meeting_date >= ? AND is_cancelled = 0", (current_date,))
        upcoming_meetings = cursor.fetchone()[0]
        
        # الاجتماعات الملغاة
        cursor.execute("SELECT COUNT(*) FROM meeting WHERE is_cancelled = 1")
        cancelled_meetings = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"📊 الإحصائيات: مجموع={total_meetings}, مكتملة={completed_meetings}, قادمة={upcoming_meetings}, ملغاة={cancelled_meetings}")
        
        return jsonify({
            'success': True,
            'total_meetings': total_meetings,
            'completed_meetings': completed_meetings,
            'upcoming_meetings': upcoming_meetings,
            'cancelled_meetings': cancelled_meetings
        })
        
    except Exception as e:
        print(f"❌ خطأ في الإحصائيات: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/simple-monthly/<int:year>')
def get_simple_monthly(year):
    try:
        db_path = 'instance/jaf_meetings.db'
        
        if not os.path.exists(db_path):
            return jsonify({'success': False, 'error': 'قاعدة البيانات غير موجودة'})
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # أسماء الأشهر العربية
        arabic_months = {
            1: 'كانون الثاني', 2: 'شباط', 3: 'آذار', 4: 'نيسان',
            5: 'أيار', 6: 'حزيران', 7: 'تموز', 8: 'آب',
            9: 'أيلول', 10: 'تشرين الأول', 11: 'تشرين الثاني', 12: 'كانون الأول'
        }
        
        months_data = []
        current_date = datetime.now().date()
        
        for month_num in range(1, 13):
            # جلب اجتماعات الشهر
            cursor.execute("""
                SELECT * FROM meeting 
                WHERE meeting_date LIKE ?
            """, (f'{year}-{month_num:02d}%',))
            
            month_meetings = cursor.fetchall()
            
            completed = 0
            upcoming = 0
            cancelled = 0
            
            for meeting in month_meetings:
                meeting_date_str = meeting[3]  # meeting_date column
                is_cancelled = meeting[12]     # is_cancelled column
                
                if is_cancelled:
                    cancelled += 1
                else:
                    meeting_date = datetime.strptime(meeting_date_str, '%Y-%m-%d').date()
                    if meeting_date < current_date:
                        completed += 1
                    else:
                        upcoming += 1
            
            total = completed + upcoming + cancelled
            
            if total > 0:  # إظهار الأشهر التي تحتوي على اجتماعات فقط
                months_data.append({
                    'month': arabic_months[month_num],
                    'completed': completed,
                    'upcoming': upcoming,
                    'cancelled': cancelled,
                    'total': total
                })
        
        conn.close()
        
        print(f"📅 بيانات سنة {year}: {len(months_data)} شهر يحتوي على اجتماعات")
        
        return jsonify({
            'success': True,
            'year': year,
            'months': months_data
        })
        
    except Exception as e:
        print(f"❌ خطأ في البيانات الشهرية: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    print("🚀 تشغيل تطبيق التقارير البسيط...")
    print("📍 الرابط: http://127.0.0.1:5000/simple-reports")
    app.run(host='127.0.0.1', port=5000, debug=True)
