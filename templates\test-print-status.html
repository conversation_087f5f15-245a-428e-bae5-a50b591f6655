<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار طباعة الحالة الحقيقية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', 'Tahoma', 'Arial', sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .test-button {
            margin: 10px;
            padding: 15px 25px;
            font-size: 16px;
            border-radius: 8px;
        }
        .status-active { background: #28a745; color: white; }
        .status-postponed { background: #ffc107; color: black; }
        .status-cancelled { background: #dc3545; color: white; }
        .status-completed { background: #6c757d; color: white; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">🖨️ اختبار طباعة الحالة الحقيقية</h1>
        
        <div class="row">
            <div class="col-md-6 mb-3">
                <button class="btn test-button status-active w-100" onclick="testActiveMeeting()">
                    <i class="fas fa-check-circle me-2"></i>
                    اختبار اجتماع نشط
                </button>
            </div>
            <div class="col-md-6 mb-3">
                <button class="btn test-button status-completed w-100" onclick="testCompletedMeeting()">
                    <i class="fas fa-flag-checkered me-2"></i>
                    اختبار اجتماع منتهي
                </button>
            </div>
            <div class="col-md-6 mb-3">
                <button class="btn test-button status-postponed w-100" onclick="testPostponedMeeting()">
                    <i class="fas fa-clock me-2"></i>
                    اختبار اجتماع مؤجل
                </button>
            </div>
            <div class="col-md-6 mb-3">
                <button class="btn test-button status-cancelled w-100" onclick="testCancelledMeeting()">
                    <i class="fas fa-times-circle me-2"></i>
                    اختبار اجتماع ملغي
                </button>
            </div>
        </div>
        
        <hr class="my-4">
        
        <div class="text-center">
            <button class="btn btn-primary btn-lg" onclick="testRealMeeting()">
                <i class="fas fa-database me-2"></i>
                اختبار مع بيانات حقيقية من قاعدة البيانات
            </button>
        </div>
        
        <div class="mt-4">
            <h5>📋 ملاحظات:</h5>
            <ul>
                <li>الاجتماع النشط: تاريخه في المستقبل وغير ملغي أو مؤجل</li>
                <li>الاجتماع المنتهي: تاريخه في الماضي وغير ملغي أو مؤجل</li>
                <li>الاجتماع المؤجل: تم تعيين علامة التأجيل</li>
                <li>الاجتماع الملغي: تم تعيين علامة الإلغاء</li>
            </ul>
        </div>
    </div>

    <!-- تحميل القالب الموحد -->
    <script src="{{ url_for('static', filename='js/unified-print-template.js') }}"></script>

    <script>
        // اختبار اجتماع نشط
        function testActiveMeeting() {
            const meeting = {
                subject: 'اجتماع مجلس الإدارة الشهري',
                meeting_type: 'اجتماع إداري',
                meeting_date: '2025-08-15', // تاريخ في المستقبل
                meeting_time: '10:30',
                location: 'قاعة الاجتماعات الرئيسية',
                inviting_party: 'مديرية الدائرة المالية',
                arrival_time_before: '15',
                book_number: '456/2025',
                book_date: '2025-07-28',
                dress_code: 'زي رسمي',
                notes: 'اجتماع دوري لمناقشة الأمور الإدارية',
                is_cancelled: false,
                is_postponed: false,
                status: 'نشط'
            };

            printMeetingReport(meeting);
        }

        // اختبار اجتماع منتهي
        function testCompletedMeeting() {
            const meeting = {
                subject: 'اجتماع المراجعة الربعية',
                meeting_type: 'اجتماع مراجعة',
                meeting_date: '2025-06-15', // تاريخ في الماضي
                meeting_time: '14:00',
                location: 'قاعة المؤتمرات',
                inviting_party: 'مديرية الدائرة المالية',
                arrival_time_before: '10',
                book_number: '123/2025',
                book_date: '2025-06-10',
                dress_code: 'زي رسمي',
                notes: 'تمت مناقشة النتائج الربعية',
                is_cancelled: false,
                is_postponed: false,
                status: 'منتهي'
            };

            printMeetingReport(meeting);
        }

        // اختبار اجتماع مؤجل
        function testPostponedMeeting() {
            const meeting = {
                subject: 'اجتماع التخطيط السنوي',
                meeting_type: 'اجتماع تخطيط',
                meeting_date: '2025-07-20',
                meeting_time: '09:00',
                location: 'قاعة الاجتماعات الكبرى',
                inviting_party: 'مديرية الدائرة المالية',
                arrival_time_before: '20',
                book_number: '789/2025',
                book_date: '2025-07-15',
                dress_code: 'زي رسمي',
                notes: 'تم تأجيل الاجتماع لظروف طارئة',
                is_cancelled: false,
                is_postponed: true,
                status: 'مؤجل',
                postpone_reason: 'ظروف طارئة',
                new_date: '2025-08-20',
                new_time: '10:00'
            };

            printMeetingReport(meeting);
        }

        // اختبار اجتماع ملغي
        function testCancelledMeeting() {
            const meeting = {
                subject: 'اجتماع المتابعة الأسبوعية',
                meeting_type: 'اجتماع متابعة',
                meeting_date: '2025-07-25',
                meeting_time: '11:00',
                location: 'قاعة الاجتماعات الصغرى',
                inviting_party: 'مديرية الدائرة المالية',
                arrival_time_before: '15',
                book_number: '321/2025',
                book_date: '2025-07-20',
                dress_code: 'زي عادي',
                notes: 'تم إلغاء الاجتماع نهائياً',
                is_cancelled: true,
                is_postponed: false,
                status: 'ملغي',
                cancellation_reason: 'عدم توفر النصاب'
            };

            printMeetingReport(meeting);
        }

        // اختبار مع بيانات حقيقية
        async function testRealMeeting() {
            try {
                const response = await fetch('/api/meetings/latest-with-attachments');
                const data = await response.json();
                
                if (data.success && data.meeting) {
                    console.log('📋 بيانات الاجتماع الحقيقية:', data.meeting);
                    printMeetingReport(data.meeting);
                } else {
                    alert('لا توجد اجتماعات في قاعدة البيانات');
                }
            } catch (error) {
                console.error('خطأ في جلب البيانات:', error);
                alert('حدث خطأ في جلب البيانات');
            }
        }

        // التحقق من تحميل القالب الموحد
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                if (typeof printMeetingReport === 'function') {
                    console.log('✅ تم تحميل القالب الموحد بنجاح');
                } else {
                    console.error('❌ فشل في تحميل القالب الموحد');
                    alert('خطأ: لم يتم تحميل القالب الموحد');
                }
            }, 500);
        });
    </script>
</body>
</html>
