/* إعدادات الألوان والتصميم العسكري - القوات المسلحة الأردنية */
/* Military Theme Configuration - Jordanian Armed Forces */

:root {
    /* ===== الألوان الأساسية - Primary Colors ===== */
    --jaf-primary: #1B4332;        /* الأخضر العسكري الأساسي */
    --jaf-secondary: #2C5530;      /* الأخضر العسكري الثانوي */
    --jaf-accent: #40916C;         /* الأخضر المميز */
    --jaf-gold: #FFD700;           /* الذهبي الملكي */
    --jaf-silver: #C0C0C0;         /* الفضي */
    
    /* ===== الألوان الداكنة - Dark Colors ===== */
    --jaf-dark: #081C15;           /* الأخضر الداكن جداً */
    --jaf-darker: #040E0A;         /* الأخضر الأسود */
    --jaf-light: #2D5016;          /* الأخضر الفاتح */
    
    /* ===== ألوان الحدود والإضاءة - Border & Glow ===== */
    --jaf-border: #52B788;         /* لون الحدود */
    --jaf-glow: 0 0 25px;          /* تأثير الإضاءة */
    --jaf-shadow: 0 8px 32px rgba(0, 0, 0, 0.3); /* الظلال */
    
    /* ===== ألوان النصوص - Text Colors ===== */
    --jaf-text: #F8F9FA;           /* النص الأساسي */
    --jaf-text-dim: #95A5A6;       /* النص الخافت */
    --jaf-text-dark: #212529;      /* النص الداكن */
    
    /* ===== ألوان الحالات - Status Colors ===== */
    --jaf-success: #74C69D;        /* النجاح */
    --jaf-warning: #FFB700;        /* التحذير */
    --jaf-danger: #E63946;         /* الخطر */
    --jaf-info: #17A2B8;           /* المعلومات */
    
    /* ===== التدرجات اللونية - Gradients ===== */
    --gradient-primary: linear-gradient(135deg, var(--jaf-primary), var(--jaf-accent));
    --gradient-secondary: linear-gradient(135deg, var(--jaf-secondary), var(--jaf-light));
    --gradient-gold: linear-gradient(135deg, var(--jaf-gold), #FFA500);
    --gradient-silver: linear-gradient(135deg, var(--jaf-silver), #A8A8A8);
    --gradient-bg: radial-gradient(ellipse at center, var(--jaf-darker) 0%, var(--jaf-dark) 100%);
    --gradient-overlay: linear-gradient(45deg, transparent 30%, rgba(255, 215, 0, 0.1) 50%, transparent 70%);
    
    /* ===== تأثيرات الخلفية - Background Effects ===== */
    --bg-pattern: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 2px,
        rgba(27, 67, 50, 0.03) 2px,
        rgba(27, 67, 50, 0.03) 4px
    );
    
    /* ===== إعدادات الخطوط - Font Settings ===== */
    --font-primary: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-secondary: 'Orbitron', 'Courier New', monospace;
    --font-size-xs: 0.75rem;       /* 12px */
    --font-size-sm: 0.875rem;      /* 14px */
    --font-size-base: 1rem;        /* 16px */
    --font-size-lg: 1.125rem;      /* 18px */
    --font-size-xl: 1.25rem;       /* 20px */
    --font-size-2xl: 1.5rem;       /* 24px */
    --font-size-3xl: 1.875rem;     /* 30px */
    
    /* ===== إعدادات المسافات - Spacing ===== */
    --spacing-xs: 0.25rem;         /* 4px */
    --spacing-sm: 0.5rem;          /* 8px */
    --spacing-md: 1rem;            /* 16px */
    --spacing-lg: 1.5rem;          /* 24px */
    --spacing-xl: 2rem;            /* 32px */
    --spacing-2xl: 3rem;           /* 48px */
    --spacing-3xl: 4rem;           /* 64px */
    
    /* ===== إعدادات الحدود - Border Settings ===== */
    --border-width-thin: 1px;
    --border-width-medium: 2px;
    --border-width-thick: 3px;
    --border-width-extra: 4px;
    --border-radius-sm: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 20px;
    --border-radius-full: 50%;
    
    /* ===== إعدادات الانتقالات - Transition Settings ===== */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    --transition-extra-slow: 0.8s ease;
    
    /* ===== إعدادات الشفافية - Opacity Settings ===== */
    --opacity-disabled: 0.6;
    --opacity-muted: 0.7;
    --opacity-normal: 0.8;
    --opacity-bright: 0.9;
    --opacity-full: 1;
    
    /* ===== إعدادات Z-Index - Layer Settings ===== */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

/* ===== الألوان البديلة للوضع الليلي - Dark Mode Alternative ===== */
[data-theme="dark"] {
    --jaf-primary: #2D5016;
    --jaf-secondary: #1B4332;
    --jaf-text: #E8F5E8;
    --jaf-text-dim: #B8C5B8;
    --jaf-bg: var(--jaf-darker);
}

/* ===== الألوان البديلة للوضع الفاتح - Light Mode Alternative ===== */
[data-theme="light"] {
    --jaf-primary: #40916C;
    --jaf-secondary: #52B788;
    --jaf-text: #1B4332;
    --jaf-text-dim: #2C5530;
    --jaf-bg: #F8F9FA;
}

/* ===== إعدادات الطباعة - Print Settings ===== */
@media print {
    :root {
        --jaf-primary: #000000;
        --jaf-secondary: #333333;
        --jaf-text: #000000;
        --jaf-bg: #FFFFFF;
    }
}

/* ===== إعدادات إمكانية الوصول - Accessibility Settings ===== */
@media (prefers-reduced-motion: reduce) {
    :root {
        --transition-fast: 0s;
        --transition-normal: 0s;
        --transition-slow: 0s;
        --transition-extra-slow: 0s;
    }
}

@media (prefers-contrast: high) {
    :root {
        --jaf-primary: #000000;
        --jaf-secondary: #333333;
        --jaf-accent: #006600;
        --jaf-text: #000000;
        --jaf-bg: #FFFFFF;
    }
}

/* ===== متغيرات مخصصة للمكونات - Component Variables ===== */
:root {
    /* أزرار - Buttons */
    --btn-padding-y: var(--spacing-md);
    --btn-padding-x: var(--spacing-xl);
    --btn-border-radius: var(--border-radius-md);
    --btn-font-weight: 600;
    
    /* حقول الإدخال - Input Fields */
    --input-padding-y: var(--spacing-lg);
    --input-padding-x: var(--spacing-xl);
    --input-border-radius: var(--border-radius-md);
    --input-border-width: var(--border-width-medium);
    
    /* البطاقات - Cards */
    --card-padding: var(--spacing-xl);
    --card-border-radius: var(--border-radius-lg);
    --card-shadow: var(--jaf-shadow);
    
    /* الشعار - Logo */
    --logo-size: 100px;
    --logo-border-width: var(--border-width-extra);
    --logo-animation-duration: 8s;
    
    /* المطر الرقمي - Matrix Rain */
    --matrix-columns: 80;
    --matrix-speed: 4s;
    --matrix-opacity: 0.4;
    
    /* الرادار - Radar */
    --radar-size: 30px;
    --radar-speed: 3s;
    --radar-color: var(--jaf-accent);
}

/* ===== فئات مساعدة للألوان - Color Helper Classes ===== */
.text-primary { color: var(--jaf-primary) !important; }
.text-secondary { color: var(--jaf-secondary) !important; }
.text-accent { color: var(--jaf-accent) !important; }
.text-gold { color: var(--jaf-gold) !important; }
.text-success { color: var(--jaf-success) !important; }
.text-warning { color: var(--jaf-warning) !important; }
.text-danger { color: var(--jaf-danger) !important; }

.bg-primary { background-color: var(--jaf-primary) !important; }
.bg-secondary { background-color: var(--jaf-secondary) !important; }
.bg-accent { background-color: var(--jaf-accent) !important; }
.bg-gold { background-color: var(--jaf-gold) !important; }

.border-primary { border-color: var(--jaf-primary) !important; }
.border-secondary { border-color: var(--jaf-secondary) !important; }
.border-accent { border-color: var(--jaf-accent) !important; }
.border-gold { border-color: var(--jaf-gold) !important; }

/* ===== فئات مساعدة للتأثيرات - Effect Helper Classes ===== */
.glow-primary { box-shadow: var(--jaf-glow) var(--jaf-primary); }
.glow-gold { box-shadow: var(--jaf-glow) var(--jaf-gold); }
.glow-accent { box-shadow: var(--jaf-glow) var(--jaf-accent); }

.gradient-primary { background: var(--gradient-primary); }
.gradient-secondary { background: var(--gradient-secondary); }
.gradient-gold { background: var(--gradient-gold); }

/* ===== تعليقات للمطورين - Developer Notes ===== */
/*
هذا الملف يحتوي على جميع إعدادات الألوان والتصميم للنظام العسكري.
يمكن تخصيص الألوان والإعدادات من هنا دون الحاجة لتعديل ملفات CSS الأخرى.

للتخصيص:
1. قم بتعديل المتغيرات في :root
2. استخدم الفئات المساعدة في HTML
3. أضف متغيرات جديدة حسب الحاجة

ملاحظة: لا تحذف المتغيرات الموجودة لأنها مستخدمة في ملفات أخرى.
*/
