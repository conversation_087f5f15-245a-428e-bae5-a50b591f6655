{% extends "base.html" %}

{% block title %}إضافة اجتماع جديد - نظام اجتماعات المدير{% endblock %}

{% block extra_css %}
<!-- SweetAlert2 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<style>
    /* تنسيق نافذة التنبيه */
    .rtl-alert {
        font-family: 'Cairo', sans-serif !important;
        direction: rtl !important;
    }
    
    .rtl-alert .swal2-title {
        font-size: 1.5rem !important;
        font-weight: 600 !important;
        color: #dc3545 !important;
    }
    
    .rtl-alert .swal2-text {
        text-align: right !important;
        font-size: 1.1rem !important;
        line-height: 1.6 !important;
    }
    
    .rtl-alert .swal2-confirm {
        font-family: 'Cairo', sans-serif !important;
        font-weight: 500 !important;
        padding: 0.5rem 2rem !important;
    }
    /* 🎨 متغيرات الألوان المحسنة */
    :root {
        --jaf-primary: #1B4332;
        --jaf-secondary: #2D5A3D;
        --jaf-success: #40916C;
        --jaf-gold: #DAA520;
        --jaf-light: #f8f9fa;
        --input-height: 58px;
        --input-border-radius: 16px;
        --input-padding: 1.5rem;
        --input-font-size: 1.05rem;
    }

    /* 🎨 تحسين شامل لجميع حقول الإدخال */
    .professional-input,
    .form-control,
    .form-select,
    input[type="text"],
    input[type="number"],
    input[type="email"],
    input[type="tel"],
    textarea,
    select {
        height: var(--input-height) !important;
        border: 2px solid #e9ecef !important;
        border-radius: var(--input-border-radius) !important;
        padding: 0 var(--input-padding) !important;
        font-size: var(--input-font-size) !important;
        font-weight: 500 !important;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 0 4px 15px rgba(0,0,0,0.08) !important;
        color: #495057 !important;
        direction: rtl !important;
        line-height: 1.6 !important;
    }

    /* إجبار الأرقام على الظهور بالإنجليزية */
    input[type="number"] {
        direction: ltr !important;
        text-align: left !important;
        font-variant-numeric: lining-nums !important;
        -webkit-font-feature-settings: "lnum" !important;
        font-feature-settings: "lnum" !important;
    }

    /* تحسين textarea */
    textarea.professional-input,
    textarea.form-control {
        height: 120px !important;
        padding: 1rem var(--input-padding) !important;
        resize: vertical !important;
        line-height: 1.6 !important;
    }

    /* تحسين حالات التفاعل */
    .professional-input:focus,
    .form-control:focus,
    .form-select:focus {
        border-color: var(--jaf-success) !important;
        box-shadow: 0 0 0 0.25rem rgba(64, 145, 108, 0.15), 0 6px 20px rgba(0,0,0,0.12) !important;
        background: linear-gradient(135deg, #ffffff 0%, rgba(64, 145, 108, 0.02) 100%) !important;
        transform: translateY(-2px) !important;
        outline: none !important;
    }

    .professional-input:hover,
    .form-control:hover,
    .form-select:hover {
        border-color: #ced4da !important;
        box-shadow: 0 6px 18px rgba(0,0,0,0.1) !important;
        transform: translateY(-1px) !important;
    }

    /* 🏷️ تحسين Labels الاحترافية */
    .professional-label {
        font-weight: 700 !important;
        color: #2c3e50 !important;
        margin-bottom: 0.75rem !important;
        display: flex !important;
        align-items: center !important;
        font-size: 1.05rem !important;
        letter-spacing: 0.3px !important;
        line-height: 1.5 !important;
    }

    .professional-label i {
        margin-left: 0.5rem !important;
        color: var(--jaf-success) !important;
        font-size: 1.1rem !important;
    }

    /* 📦 تحسين مجموعات الإدخال */
    .professional-input-group {
        margin-bottom: 1.5rem !important;
    }

    .input-wrapper {
        position: relative !important;
    }

    .input-icon {
        position: absolute !important;
        left: 1rem !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        color: #6c757d !important;
        font-size: 1.1rem !important;
        pointer-events: none !important;
    }

    /* 🔽 أنماط القائمة المنسدلة لجهة الدعوة */
    #inviting_party_dropdown {
        position: absolute !important;
        top: 100% !important;
        left: 0 !important;
        right: 0 !important;
        z-index: 1050 !important;
        background: white !important;
        border: 1px solid #dee2e6 !important;
        border-radius: 12px !important;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
        margin-top: 4px !important;
        max-height: 200px !important;
        overflow-y: auto !important;
    }

    #inviting_party_dropdown .dropdown-item {
        padding: 12px 16px !important;
        border: none !important;
        background: transparent !important;
        color: #333 !important;
        font-size: 1rem !important;
        transition: all 0.2s ease !important;
        cursor: pointer !important;
        border-bottom: 1px solid rgba(0,0,0,0.05) !important;
    }

    #inviting_party_dropdown .dropdown-item:last-child {
        border-bottom: none !important;
    }

    #inviting_party_dropdown .dropdown-item:hover {
        background: linear-gradient(135deg, var(--jaf-success) 0%, #52c085 100%) !important;
        color: white !important;
        transform: translateX(4px) !important;
    }

    #inviting_party_dropdown .dropdown-item.text-muted {
        color: #6c757d !important;
        font-style: italic !important;
        cursor: default !important;
    }

    #inviting_party_dropdown .dropdown-item.text-muted:hover {
        background: transparent !important;
        color: #6c757d !important;
        transform: none !important;
    }

    /* تحسين شريط التمرير للقائمة المنسدلة */
    #inviting_party_dropdown::-webkit-scrollbar {
        width: 6px !important;
    }

    #inviting_party_dropdown::-webkit-scrollbar-track {
        background: #f1f1f1 !important;
        border-radius: 6px !important;
    }

    #inviting_party_dropdown::-webkit-scrollbar-thumb {
        background: var(--jaf-success) !important;
        border-radius: 6px !important;
    }

    #inviting_party_dropdown::-webkit-scrollbar-thumb:hover {
        background: var(--jaf-secondary) !important;
    }

    .form-section {
        background: white;
        border-radius: 16px;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
        margin-bottom: 1rem;
        overflow: hidden;
        border: 1px solid rgba(0,0,0,0.05);
        transition: all 0.3s ease;
    }

    .form-section:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
    }
    
    .section-header {
        background: linear-gradient(135deg, #1B4332 0%, #2D5A3D 100%);
        color: white;
        padding: 0.75rem 1.25rem;
        font-weight: 700;
        font-size: 1rem;
        display: flex;
        align-items: center;
    }

    .section-header i {
        font-size: 1.1rem;
    }

    .section-body {
        padding: 1.25rem;
    }
    
    .form-floating {
        margin-bottom: 1.5rem;
    }
    
    .form-floating .form-control,
    .form-floating .form-select {
        height: 60px;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        font-size: 1rem;
    }
    
    .form-floating .form-control:focus,
    .form-floating .form-select:focus {
        border-color: var(--jaf-primary);
        box-shadow: 0 0 0 0.2rem rgba(27, 67, 50, 0.25);
    }
    
    .form-floating label {
        color: #6c757d;
        font-weight: 500;
    }
    
    .file-upload-area {
        border: 2px dashed #dee2e6;
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        background: #fafafa;
        position: relative;
        overflow: hidden;
    }

    .file-upload-area:hover {
        border-color: var(--jaf-primary);
        background-color: rgba(27, 67, 50, 0.05);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .file-upload-area.dragover {
        border-color: var(--jaf-success);
        background-color: rgba(64, 145, 108, 0.1);
        transform: scale(1.02);
    }

    .file-upload-area::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--jaf-primary), var(--jaf-success));
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .file-upload-area:hover::before {
        opacity: 1;
    }

    /* تحسين عرض معلومات الملفات */
    #fileInfo {
        animation: slideDown 0.3s ease;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .modern-remove-btn {
        background: #dc3545;
        color: white;
        border: none;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .modern-remove-btn:hover {
        background: #c82333;
        transform: scale(1.1);
    }

    /* تأثيرات بصرية للرفع */
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.02); }
        100% { transform: scale(1); }
    }

    .file-upload-area i {
        transition: all 0.3s ease;
    }

    .file-upload-area h5 {
        transition: all 0.3s ease;
        font-weight: 600;
    }

    .file-upload-area p {
        transition: all 0.3s ease;
    }
    
    .dress-code-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }
    
    .dress-code-option {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 0.6rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
        font-size: 0.8rem;
        font-weight: 600;
        min-height: 100px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
    
    .dress-code-option:hover {
        border-color: var(--jaf-primary);
        background-color: rgba(27, 67, 50, 0.05);
    }
    
    .dress-code-option.selected {
        border-color: var(--jaf-primary);
        background-color: rgba(27, 67, 50, 0.1);
        color: var(--jaf-primary);
        font-weight: 600;
    }
    
    .dress-code-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        display: block;
        color: #FFD700;
        text-shadow: 0 2px 8px rgba(255, 215, 0, 0.4);
        transition: all 0.3s ease;
    }

    .dress-code-option:hover .dress-code-icon {
        color: #FFA500;
        transform: scale(1.1);
    }

    .dress-code-option.selected .dress-code-icon {
        color: #FF8C00;
        transform: scale(1.05);
    }

    .dress-code-option.selected .dress-code-icon-img {
        transform: scale(1.05);
        filter: drop-shadow(0 4px 12px rgba(255, 140, 0, 0.7)) brightness(1.15) contrast(1.05);
        border: 2px solid #FF8C00;
    }

    .dress-code-icon-img {
        width: 48px;
        height: 48px;
        margin-bottom: 0.4rem;
        display: block;
        margin-left: auto;
        margin-right: auto;
        filter: drop-shadow(0 2px 6px rgba(255, 215, 0, 0.4));
        transition: all 0.3s ease;
        border-radius: 6px;
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
        image-rendering: pixelated;
        -ms-interpolation-mode: nearest-neighbor;
    }

    .dress-code-option:hover .dress-code-icon-img {
        transform: scale(1.1);
        filter: drop-shadow(0 4px 15px rgba(255, 165, 0, 0.8)) brightness(1.2) contrast(1.1);
    }

    .dress-code-option.selected .dress-code-icon-img {
        transform: scale(1.05);
        filter: drop-shadow(0 3px 12px rgba(255, 140, 0, 0.8)) brightness(1.2);
    }
    
    .time-input-group {
        display: flex;
        gap: 1rem;
        align-items: center;
    }
    
    .conflict-warning {
        background: rgba(247, 127, 0, 0.1);
        border: 1px solid var(--jaf-warning);
        border-radius: 10px;
        padding: 1rem;
        margin-top: 1rem;
        display: none;
    }
    
    .submit-section {
        background: linear-gradient(135deg, rgba(27, 67, 50, 0.05) 0%, rgba(45, 106, 79, 0.05) 100%);
        border-radius: 20px;
        padding: 3rem 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
        border: 2px solid rgba(27, 67, 50, 0.1);
    }

    .submit-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(27, 67, 50, 0.05), transparent);
        animation: shimmer-submit 4s infinite;
    }

    @keyframes shimmer-submit {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    .modern-submit-container {
        position: relative;
        z-index: 2;
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        max-width: 400px;
        margin: 0 auto;
    }

    .modern-submit-btn {
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        border: none;
        border-radius: 14px;
        padding: 0.8rem 1.8rem;
        color: #333;
        font-size: 1rem;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        box-shadow: 0 6px 25px rgba(255, 215, 0, 0.4);
        border: 2px solid transparent;
        text-transform: none;
        letter-spacing: 0.6px;
        min-height: 62px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
    }

    .modern-submit-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.6s ease;
    }

    .modern-submit-btn:hover::before {
        left: 100%;
    }

    .modern-submit-btn:hover {
        transform: translateY(-3px) scale(1.02);
        box-shadow: 0 8px 30px rgba(255, 215, 0, 0.5);
        border-color: rgba(255, 255, 255, 0.3);
        background: linear-gradient(135deg, #FFA500 0%, #FF8C00 100%);
        color: #fff;
    }

    .modern-submit-btn:active {
        transform: translateY(-2px) scale(1.01);
        box-shadow: 0 6px 20px rgba(255, 215, 0, 0.7);
    }

    .modern-reset-btn {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 14px;
        padding: 0.8rem 1.8rem;
        color: #6c757d;
        font-size: 1rem;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 25px rgba(0,0,0,0.08);
        min-height: 62px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        letter-spacing: 0.5px;
    }

    .modern-reset-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        transition: left 0.3s ease;
        z-index: 1;
    }

    .modern-reset-btn i,
    .modern-reset-btn span {
        position: relative;
        z-index: 2;
    }

    .modern-reset-btn:hover {
        color: white;
        border-color: #6c757d;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
    }

    .modern-reset-btn:hover::before {
        left: 0;
    }

    .btn-icon {
        margin-left: 0.75rem;
        font-size: 1.2rem;
        transition: transform 0.3s ease;
    }

    .modern-submit-btn:hover .btn-icon {
        transform: scale(1.2) rotate(5deg);
    }

    .modern-reset-btn:hover .btn-icon {
        transform: scale(1.1) rotate(-5deg);
    }

    /* أزرار التاريخ والوقت السريعة العصرية */
    .quick-date-buttons,
    .quick-time-buttons {
        display: flex;
        gap: 0.5rem;
        margin-top: 0.75rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .quick-date-btn,
    .quick-time-btn {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 2px solid #dee2e6;
        border-radius: 14px;
        padding: 0.75rem 1.25rem;
        font-size: 0.95rem;
        font-weight: 600;
        color: #495057;
        cursor: pointer;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0,0,0,0.08);
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .quick-date-btn::before,
    .quick-time-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #1B4332 0%, #2D5A3D 100%);
        transition: left 0.3s ease;
        z-index: 1;
    }

    .quick-date-btn span,
    .quick-time-btn span {
        position: relative;
        z-index: 2;
    }

    .quick-date-btn:hover,
    .quick-time-btn:hover {
        color: white;
        border-color: #1B4332;
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 6px 20px rgba(27, 67, 50, 0.3);
    }

    .quick-date-btn:hover::before,
    .quick-time-btn:hover::before {
        left: 0;
    }

    .quick-date-btn:active,
    .quick-time-btn:active {
        transform: translateY(-1px) scale(1.02);
        box-shadow: 0 3px 10px rgba(27, 67, 50, 0.4);
    }

    .quick-date-btn.active,
    .quick-time-btn.active {
        background: linear-gradient(135deg, #1B4332 0%, #2D5A3D 100%);
        color: white;
        border-color: #1B4332;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(27, 67, 50, 0.4);
    }

    .quick-date-btn.active::before,
    .quick-time-btn.active::before {
        left: 0;
        background: linear-gradient(135deg, #2D5A3D 0%, #40916C 100%);
    }

    /* حقول التاريخ والوقت المحسنة */
    .modern-date-input-container,
    .modern-time-input-container {
        position: relative;
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 16px;
        overflow: hidden;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        margin-bottom: 1rem;
    }

    .modern-date-input-container:hover,
    .modern-time-input-container:hover {
        border-color: #1B4332;
        box-shadow: 0 8px 25px rgba(27, 67, 50, 0.15);
        transform: translateY(-2px);
    }

    .modern-date-input-container:focus-within,
    .modern-time-input-container:focus-within {
        border-color: #1B4332;
        box-shadow: 0 0 0 4px rgba(27, 67, 50, 0.1);
        transform: translateY(-1px);
    }

    .modern-date-input,
    .modern-time-input {
        width: 100%;
        height: var(--input-height);
        padding: 1rem 5rem 1rem var(--input-padding);
        border: none;
        background: transparent;
        font-size: var(--input-font-size);
        font-weight: 600;
        color: #333;
        outline: none;
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        direction: ltr;
        text-align: left;
        line-height: 1.6;
        transition: all 0.3s ease;
    }

    .modern-date-input::-webkit-calendar-picker-indicator,
    .modern-time-input::-webkit-calendar-picker-indicator {
        opacity: 0;
        position: absolute;
        right: 0;
        width: 5rem;
        height: 100%;
        cursor: pointer;
        z-index: 20;
    }

    .modern-date-overlay,
    .modern-time-overlay {
        position: absolute;
        right: 0.5rem;
        top: 50%;
        transform: translateY(-50%);
        width: 56px;
        height: 56px;
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        border-radius: 18px;
        display: flex !important;
        align-items: center;
        justify-content: center;
        color: #333 !important;
        font-size: 1.6rem;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 8px 25px rgba(255, 215, 0, 0.5);
        z-index: 100 !important;
        border: 3px solid rgba(255, 255, 255, 0.4);
        font-weight: bold;
        opacity: 1 !important;
        visibility: visible !important;
    }

    .modern-date-overlay:hover,
    .modern-time-overlay:hover {
        transform: translateY(-50%) scale(1.2);
        box-shadow: 0 12px 35px rgba(255, 215, 0, 0.7);
        background: linear-gradient(135deg, #FFA500 0%, #FF8C00 100%);
        color: #fff !important;
        animation: golden-pulse 1s ease-in-out infinite;
    }

    .modern-date-overlay:active,
    .modern-time-overlay:active {
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 8px 25px rgba(255, 215, 0, 0.8);
    }

    .modern-date-input:focus + .modern-date-overlay,
    .modern-time-input:focus + .modern-time-overlay {
        background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
        transform: translateY(-50%) scale(1.12);
        animation: pulse-overlay 1.5s infinite;
        color: #fff;
        box-shadow: 0 8px 25px rgba(255, 107, 53, 0.5);
    }

    .modern-date-input:valid,
    .modern-time-input:valid {
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.03) 0%, rgba(40, 167, 69, 0.01) 100%);
        border-right: 4px solid #28a745;
    }

    .modern-date-input:valid + .modern-date-overlay,
    .modern-time-input:valid + .modern-time-overlay {
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        animation: success-bounce 1s ease-out;
        color: #333;
        box-shadow: 0 6px 20px rgba(255, 215, 0, 0.6);
    }

    @keyframes pulse-overlay {
        0%, 100% {
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            transform: translateY(-50%) scale(1.05);
        }
        50% {
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.6);
            transform: translateY(-50%) scale(1.1);
        }
    }

    @keyframes success-bounce {
        0% { transform: translateY(-50%) scale(1); }
        50% { transform: translateY(-50%) scale(1.2); }
        100% { transform: translateY(-50%) scale(1); }
    }

    /* تأثير نبض ذهبي مستمر للأيقونة */
    .modern-date-overlay i,
    .modern-time-overlay i {
        animation: golden-icon-pulse 2s ease-in-out infinite;
        text-shadow: 0 2px 6px rgba(0,0,0,0.4);
        color: #333 !important;
        font-weight: 900;
        display: inline-block;
    }

    @keyframes golden-icon-pulse {
        0%, 100% {
            transform: scale(1);
            opacity: 1;
            text-shadow: 0 2px 6px rgba(0,0,0,0.4);
        }
        50% {
            transform: scale(1.2);
            opacity: 0.9;
            text-shadow: 0 4px 12px rgba(255,215,0,0.6);
        }
    }

    /* تأثير نبض ذهبي للخلفية */
    @keyframes golden-pulse {
        0%, 100% {
            box-shadow: 0 12px 35px rgba(255, 215, 0, 0.7);
        }
        50% {
            box-shadow: 0 16px 45px rgba(255, 215, 0, 0.9);
        }
    }

    /* إجبار ظهور أيقونات التاريخ */
    .modern-date-overlay,
    .modern-time-overlay {
        pointer-events: auto !important;
    }

    .modern-date-overlay::before,
    .modern-time-overlay::before {
        content: '';
        position: absolute;
        top: -5px;
        left: -5px;
        right: -5px;
        bottom: -5px;
        background: linear-gradient(135deg, rgba(255, 215, 0, 0.3) 0%, rgba(255, 165, 0, 0.3) 100%);
        border-radius: 23px;
        z-index: -1;
        animation: golden-glow 3s ease-in-out infinite;
    }

    @keyframes golden-glow {
        0%, 100% {
            opacity: 0.5;
            transform: scale(1);
        }
        50% {
            opacity: 0.8;
            transform: scale(1.1);
        }
    }

    /* زر العودة العصري */
    .modern-back-btn {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        color: #6c757d;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        position: relative;
        overflow: hidden;
    }

    .modern-back-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        transition: left 0.3s ease;
        z-index: 1;
    }

    .modern-back-btn i,
    .modern-back-btn span {
        position: relative;
        z-index: 2;
    }

    .modern-back-btn:hover {
        color: white;
        border-color: #6c757d;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(108, 117, 125, 0.3);
        text-decoration: none;
    }

    .modern-back-btn:hover::before {
        left: 0;
    }

    .modern-back-btn:hover i {
        transform: translateX(-3px);
    }

    /* زر إزالة الملف العصري */
    .modern-remove-btn {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border: none;
        border-radius: 8px;
        padding: 0.4rem 0.8rem;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.85rem;
        box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
        position: relative;
        overflow: hidden;
    }

    .modern-remove-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
        transition: left 0.3s ease;
    }

    .modern-remove-btn:hover {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
    }

    .modern-remove-btn:hover::before {
        left: 0;
    }

    .modern-remove-btn i {
        position: relative;
        z-index: 2;
    }

    /* تحسينات للأجهزة المحمولة */
    @media (max-width: 768px) {
        /* تحسين الحقول للشاشات المتوسطة */
        .professional-input,
        .form-control,
        .form-select {
            height: 54px !important;
            font-size: 1rem !important;
            padding: 0 1.25rem !important;
            border-radius: 14px !important;
        }

        textarea.professional-input,
        textarea.form-control {
            height: 100px !important;
        }

        .submit-section {
            padding: 2rem 1rem;
            border-radius: 16px;
        }

        .modern-submit-container {
            max-width: 100%;
            gap: 1rem;
        }

        .modern-submit-btn,
        .modern-reset-btn {
            padding: 1rem 2rem;
            font-size: 1.05rem;
            border-radius: 16px;
            min-height: 56px;
        }

        .quick-date-buttons,
        .quick-time-buttons {
            gap: 0.375rem;
        }

        .quick-date-btn,
        .quick-time-btn {
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
            border-radius: 10px;
        }

        .modern-back-btn {
            padding: 0.625rem 1.25rem;
            font-size: 0.9rem;
            border-radius: 10px;
        }

        /* تحسين الأعمدة الثلاثة للشاشات المتوسطة */
        .col-lg-4 {
            margin-bottom: 1rem;
        }

        .file-upload-area {
            min-height: 100px;
            padding: 0.75rem;
        }

        .file-upload-area i {
            font-size: 1.25rem !important;
        }
    }

    @media (max-width: 576px) {
        /* تحسين الحقول للشاشات الصغيرة */
        .professional-input,
        .form-control,
        .form-select {
            height: 52px !important;
            font-size: 0.95rem !important;
            padding: 0 1rem !important;
            border-radius: 12px !important;
        }

        textarea.professional-input,
        textarea.form-control {
            height: 90px !important;
        }

        .professional-label {
            font-size: 1rem !important;
            margin-bottom: 0.5rem !important;
        }

        .submit-section {
            padding: 1.5rem 0.75rem;
        }

        .modern-submit-btn,
        .modern-reset-btn {
            padding: 0.9rem 1.5rem;
            font-size: 1rem;
            min-height: 52px;
            border-radius: 14px;
        }

        .quick-date-buttons,
        .quick-time-buttons {
            flex-direction: column;
            gap: 0.5rem;
        }

        .quick-date-btn,
        .quick-time-btn {
            width: 100%;
            text-align: center;
            padding: 0.5rem;
        }

        .modern-back-btn {
            width: 100%;
            justify-content: center;
            margin-bottom: 1rem;
        }

        .page-header {
            flex-direction: column;
            gap: 1rem;
            text-align: center;
        }

        .modern-date-input-container,
        .modern-time-input-container {
            border-radius: 12px;
        }

        .modern-date-input,
        .modern-time-input {
            height: 52px;
            padding: 0.875rem 4rem 0.875rem 1.25rem;
            font-size: 1rem;
        }

        .modern-date-overlay,
        .modern-time-overlay {
            width: 48px;
            height: 48px;
            font-size: 1.4rem;
            border-radius: 12px;
            right: 0.4rem;
            display: flex !important;
            opacity: 1 !important;
            visibility: visible !important;
            z-index: 100 !important;
        }
    }

    @media (max-width: 576px) {
        .modern-date-input-container,
        .modern-time-input-container {
            border-radius: 10px;
        }

        .modern-date-input,
        .modern-time-input {
            height: 48px;
            padding: 0.75rem 3.5rem 0.75rem 1rem;
            font-size: 0.95rem;
        }

        .modern-date-overlay,
        .modern-time-overlay {
            width: 38px;
            height: 38px;
            font-size: 1.1rem;
            border-radius: 8px;
        }

        .modern-notification {
            right: 10px !important;
            left: 10px !important;
            min-width: auto !important;
            transform: translateY(-100px) !important;
        }

        .modern-notification.show {
            transform: translateY(0) !important;
        }

        /* تحسين الأعمدة الثلاثة للشاشات الصغيرة */
        .col-lg-4 {
            margin-bottom: 1.5rem;
        }

        .file-upload-area {
            min-height: 80px;
            padding: 0.5rem;
        }

        .file-upload-area i {
            font-size: 1rem !important;
        }

        .file-upload-area h6 {
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }
    }

    /* مودال التضارب */
    .conflict-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .conflict-modal.show {
        opacity: 1;
    }

    .conflict-modal-content {
        background: white;
        border-radius: 15px;
        max-width: 600px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        transform: scale(0.9);
        transition: transform 0.3s ease;
    }

    .conflict-modal.show .conflict-modal-content {
        transform: scale(1);
    }

    .conflict-modal-header {
        padding: 1.5rem;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border-radius: 15px 15px 0 0;
    }

    .conflict-modal-header h4 {
        margin: 0;
        color: #856404;
    }

    .conflict-modal-body {
        padding: 1.5rem;
    }

    .conflict-modal-footer {
        padding: 1rem 1.5rem;
        border-top: 1px solid #dee2e6;
        display: flex;
        justify-content: flex-end;
        gap: 0.5rem;
        background: #f8f9fa;
        border-radius: 0 0 15px 15px;
    }

    .conflict-item {
        margin-bottom: 0.5rem;
    }

    .btn-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        color: #856404;
        cursor: pointer;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn-close:before {
        content: "×";
    }

    /* تنسيق رسائل SweetAlert للعربية */
    .arabic-popup {
        font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
        direction: rtl !important;
        text-align: right !important;
    }

    .arabic-popup .swal2-title {
        font-weight: 700 !important;
        font-size: 1.5rem !important;
    }

    .arabic-popup .swal2-content {
        font-size: 1.1rem !important;
        line-height: 1.6 !important;
    }

    .arabic-popup .swal2-confirm {
        font-weight: 600 !important;
        padding: 12px 24px !important;
        border-radius: 8px !important;
    }

    .arabic-popup .swal2-cancel {
        font-weight: 600 !important;
        padding: 12px 24px !important;
        border-radius: 8px !important;
    }

    /* تنسيق أخطاء التحقق */
    .is-invalid {
        border-color: #dc3545 !important;
        box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25) !important;
    }

    .invalid-feedback {
        display: block !important;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 0.875rem;
        color: #dc3545;
        font-weight: 500;
        background: rgba(220, 53, 69, 0.1);
        padding: 0.5rem;
        border-radius: 6px;
        border-left: 3px solid #dc3545;
    }

    /* تحسين رسائل المساعدة */
    .text-muted {
        font-size: 0.85rem;
        color: #6c757d !important;
    }

    .text-muted i {
        color: #17a2b8;
    }

    /* تنسيق نوع اللباس */
    .dress-code-option {
        cursor: pointer;
        transition: all 0.3s ease;
        border: 3px solid transparent;
        border-radius: 10px;
        padding: 5px;
    }

    .dress-code-option:hover {
        border-color: #007bff;
        transform: scale(1.05);
    }

    .dress-code-option.selected {
        border-color: #28a745;
        background-color: rgba(40, 167, 69, 0.1);
        transform: scale(1.05);
    }

    /* تحسين مظهر حقول التاريخ والوقت */
    .modern-date-input-container, .modern-time-input-container {
        position: relative;
        display: flex;
        align-items: center;
    }

    .modern-date-input, .modern-time-input {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 12px 45px 12px 15px;
        font-size: 16px;
        color: #495057;
        transition: all 0.3s ease;
        width: 100%;
        direction: ltr;
        text-align: left;
    }

    .modern-date-input:focus, .modern-time-input:focus {
        border-color: var(--jaf-primary);
        box-shadow: 0 0 0 3px rgba(27, 67, 50, 0.1);
        outline: none;
    }

    /* Placeholder مخصص للتاريخ والوقت */
    .date-placeholder, .time-placeholder {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        pointer-events: none;
        font-size: 16px;
        font-weight: 500;
        z-index: 1;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
        padding: 2px 6px;
        border-radius: 4px;
    }

    /* إخفاء الـ placeholder عند وجود قيمة أو التركيز */
    .modern-date-input:valid ~ .date-placeholder,
    .modern-date-input:focus ~ .date-placeholder {
        opacity: 0;
        transform: translateY(-50%) translateX(-10px);
    }

    .modern-time-input:valid ~ .time-placeholder,
    .modern-time-input:focus ~ .time-placeholder {
        opacity: 0;
        transform: translateY(-50%) translateX(-10px);
    }

    /* إخفاء النص الافتراضي للمتصفح */
    .modern-date-input::-webkit-datetime-edit-text,
    .modern-date-input::-webkit-datetime-edit-month-field,
    .modern-date-input::-webkit-datetime-edit-day-field,
    .modern-date-input::-webkit-datetime-edit-year-field {
        color: transparent;
    }

    .modern-date-input:focus::-webkit-datetime-edit-text,
    .modern-date-input:focus::-webkit-datetime-edit-month-field,
    .modern-date-input:focus::-webkit-datetime-edit-day-field,
    .modern-date-input:focus::-webkit-datetime-edit-year-field,
    .modern-date-input:valid::-webkit-datetime-edit-text,
    .modern-date-input:valid::-webkit-datetime-edit-month-field,
    .modern-date-input:valid::-webkit-datetime-edit-day-field,
    .modern-date-input:valid::-webkit-datetime-edit-year-field {
        color: #495057;
    }

    .modern-time-input::-webkit-datetime-edit-text,
    .modern-time-input::-webkit-datetime-edit-hour-field,
    .modern-time-input::-webkit-datetime-edit-minute-field,
    .modern-time-input::-webkit-datetime-edit-ampm-field {
        color: transparent;
    }

    .modern-time-input:focus::-webkit-datetime-edit-text,
    .modern-time-input:focus::-webkit-datetime-edit-hour-field,
    .modern-time-input:focus::-webkit-datetime-edit-minute-field,
    .modern-time-input:focus::-webkit-datetime-edit-ampm-field,
    .modern-time-input:valid::-webkit-datetime-edit-text,
    .modern-time-input:valid::-webkit-datetime-edit-hour-field,
    .modern-time-input:valid::-webkit-datetime-edit-minute-field,
    .modern-time-input:valid::-webkit-datetime-edit-ampm-field {
        color: #495057;
    }

    /* تحسين الـ overlay */
    .modern-date-overlay, .modern-time-overlay {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        background: var(--jaf-primary);
        color: white;
        width: 35px;
        height: 35px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        z-index: 2;
    }

    .modern-date-overlay:hover, .modern-time-overlay:hover {
        background: var(--jaf-secondary);
        transform: translateY(-50%) scale(1.1);
    }

    /* إصلاح أزرار نوع اللباس */
    .dress-code-option {
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        border: 2px solid transparent !important;
    }

    .dress-code-option:hover {
        transform: translateY(-3px) !important;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
        border-color: #007bff !important;
    }

    .dress-code-option.selected {
        border-color: #28a745 !important;
        background: rgba(40, 167, 69, 0.1) !important;
        transform: scale(1.05) !important;
    }

    /* تحسين أزرار النموذج */
    .modern-submit-btn,
    .modern-reset-btn {
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        border: none !important;
        outline: none !important;
    }

    .modern-submit-btn:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3) !important;
    }

    .modern-reset-btn:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3) !important;
    }

    /* تحسين قائمة جهات الدعوة */
    #inviting_party_dropdown {
        border: 1px solid #dee2e6 !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
        background: white !important;
        z-index: 1000 !important;
    }

    #inviting_party_dropdown .dropdown-item {
        padding: 10px 15px !important;
        transition: all 0.2s ease !important;
    }

    #inviting_party_dropdown .dropdown-item:hover {
        background: #f8f9fa !important;
        color: #007bff !important;
    }



    /* تنسيق التاريخ من اليمين إلى اليسار */
    .modern-date-input,
    input[type="date"] {
        direction: rtl !important;
        text-align: right !important;
    }

    /* إخفاء أيقونة التقويم الافتراضية في بعض المتصفحات */
    input[type="date"]::-webkit-calendar-picker-indicator {
        opacity: 0;
        position: absolute;
        right: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
    }

    /* تحسين مظهر حقول التاريخ */
    .modern-date-input-container {
        position: relative;
    }

    .modern-date-input-container input[type="date"] {
        padding-right: 45px !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-2">
    <!-- 🎯 Header محسن ومضغوط -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center bg-white rounded-3 p-3 shadow-sm border">
                <div>
                    <h4 class="fw-bold text-primary mb-1 d-flex align-items-center">
                        <i class="fas fa-plus-circle me-2 text-success"></i>
                        إضافة اجتماع جديد
                    </h4>
                    <small class="text-muted">املأ البيانات المطلوبة بدقة</small>
                </div>
                <a href="{{ url_for('index') }}" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-arrow-right me-1"></i>
                    العودة
                </a>
            </div>
        </div>
    </div>

    <!-- 📝 النموذج الرئيسي بتخطيط محسن -->
    <form id="meetingForm" method="POST" enctype="multipart/form-data" onsubmit="return confirmSave(event)">
        <div class="row g-3">
            <!-- 📋 العمود الأيسر - المعلومات الأساسية -->
            <div class="col-lg-8">
                <!-- قسم المعلومات الأساسية -->
                <div class="form-section mb-3">
                    <div class="section-header">
                        <i class="fas fa-info-circle me-2"></i>
                        المعلومات الأساسية
                    </div>
                    <div class="section-body">
                        <div class="row g-3">
                            <!-- موضوع الاجتماع -->
                            <div class="col-12">
                                <label class="professional-label">
                                    <i class="fas fa-heading me-2"></i>
                                    موضوع الاجتماع
                                </label>
                                <input type="text" class="form-control professional-input" id="subject" name="subject"
                                       placeholder="أدخل موضوع الاجتماع..." required>
                            </div>

                            <!-- نوع الفعالية -->
                            <div class="col-md-4">
                                <label class="professional-label">
                                    <i class="fas fa-list-alt me-2"></i>
                                    نوع الفعالية
                                </label>
                                <select class="form-select professional-input" id="meeting_type" name="meeting_type" required>
                                    <option value="">اختر نوع الفعالية</option>
                                    <option value="اجتماع">🤝 اجتماع</option>
                                    <option value="دعوة">📧 دعوة</option>
                                    <option value="زيارة">🚶 زيارة</option>
                                    <option value="إيجاز">📊 إيجاز</option>
                                    <option value="تمرين">🏃 تمرين</option>
                                    <option value="اتصال مرئي">📹 اتصال مرئي</option>
                                </select>
                            </div>

                            <!-- جهة الدعوة -->
                            <div class="col-md-5">
                                <label class="professional-label">
                                    <i class="fas fa-building me-2"></i>
                                    جهة الدعوة
                                </label>
                                <div class="position-relative">
                                    <input type="text" class="form-control professional-input" id="inviting_party" name="inviting_party"
                                           placeholder="ابحث أو اكتب جهة الدعوة..." autocomplete="off" required
                                           onclick="showInvitingPartiesList()" onfocus="showInvitingPartiesList()" oninput="filterInvitingPartiesList(this.value)">
                                    <div id="inviting_party_dropdown" class="dropdown-menu w-100" style="display: none; max-height: 200px; overflow-y: auto;">
                                        <!-- سيتم ملء القائمة بواسطة JavaScript -->
                                    </div>
                                </div>
                            </div>

                            <!-- الحضور قبل -->
                            <div class="col-md-3">
                                <label class="professional-label">
                                    <i class="fas fa-user-clock me-2"></i>
                                    الحضور قبل (دقائق)
                                </label>
                                <input type="number" class="form-control professional-input" id="arrival_time_before" name="arrival_time_before"
                                       min="5" max="120" value="15" required dir="ltr">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قسم التاريخ والوقت -->
                <div class="form-section mb-3">
                    <div class="section-header">
                        <i class="fas fa-calendar-check me-2"></i>
                        التاريخ والوقت
                    </div>
                    <div class="section-body">
                        <div class="row g-3">
                            <!-- التاريخ -->
                            <div class="col-md-6">
                                <label class="professional-label">
                                    <i class="fas fa-calendar-alt me-2"></i>
                                    تاريخ الاجتماع
                                </label>
                                <div class="modern-date-input-container">
                                    <input type="date" class="modern-date-input" id="meeting_date" name="meeting_date" required>
                                    <div class="modern-date-overlay" onclick="openDatePicker('meeting_date')">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                </div>

                            </div>

                            <!-- الوقت -->
                            <div class="col-md-6">
                                <label class="professional-label">
                                    <i class="fas fa-clock me-2"></i>
                                    وقت الاجتماع (النظام العسكري)
                                </label>
                                <div class="modern-time-input-container">
                                    <input type="time" class="modern-time-input" id="meeting_time" name="meeting_time" required>
                                    <div class="modern-time-overlay" onclick="openTimePicker('meeting_time')">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- مكان الاجتماع -->
                            <div class="col-12">
                                <label class="professional-label">
                                    <i class="fas fa-map-marker-alt me-2"></i>
                                    مكان الاجتماع
                                </label>
                                <input type="text" class="form-control professional-input" id="location" name="location"
                                       placeholder="أدخل مكان الاجتماع..." required>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قسم المرفقات والملاحظات -->
                <div class="form-section mb-3">
                    <div class="section-header">
                        <i class="fas fa-paperclip me-2"></i>
                        المرفقات والملاحظات
                    </div>
                    <div class="section-body">
                        <div class="row g-3">

                            <!-- المرفقات والملاحظات -->
                            <div class="col-md-6">
                                <h6 class="fw-bold mb-2 text-success">
                                    <i class="fas fa-paperclip me-2"></i>
                                    المرفقات
                                </h6>
                                <div class="file-upload-area" id="fileUploadArea">
                                    <i class="fas fa-cloud-upload-alt" style="font-size: 1.25rem; color: #6c757d; margin-bottom: 0.25rem;"></i>
                                    <h6 class="mb-1">اسحب الملفات أو انقر</h6>
                                    <small class="text-muted">PDF, Word, والصور</small>
                                    <input type="file" id="attachments" name="attachments" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" multiple style="display: none;">
                                </div>
                                <div id="fileInfo" class="mt-2" style="display: none;">
                                    <div class="alert alert-info p-2 mb-0">
                                        <i class="fas fa-file-pdf me-2"></i>
                                        <span id="fileName"></span>
                                        <button type="button" class="btn btn-sm btn-outline-danger float-end" id="removeFile">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h6 class="fw-bold mb-2 text-success">
                                    <i class="fas fa-sticky-note me-2"></i>
                                    ملاحظات إضافية
                                </h6>
                                <textarea class="form-control professional-input" id="notes" name="notes" rows="4"
                                          placeholder="أي ملاحظات أو تفاصيل إضافية..." style="resize: vertical;"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 📋 العمود الأيمن - المعلومات الإضافية -->
            <div class="col-lg-4">
                <!-- قسم نوع اللباس -->
                <div class="form-section mb-3">
                    <div class="section-header">
                        <i class="fas fa-user-tie me-2"></i>
                        نوع اللباس
                    </div>
                    <div class="section-body">
                        <div class="dress-code-grid">
                            <div class="dress-code-option" data-value="بدلة عمل" onclick="selectDressCode(this, 'بدلة عمل')">
                                <img src="{{ url_for('static', filename='images/بدلة عمل.png') }}" alt="بدلة عمل" class="dress-code-icon-img">
                                <div>بدلة عمل</div>
                            </div>
                            <div class="dress-code-option" data-value="موسمي" onclick="selectDressCode(this, 'موسمي')">
                                <img src="{{ url_for('static', filename='images/موسمي.png') }}" alt="موسمي" class="dress-code-icon-img">
                                <div>موسمي</div>
                            </div>
                            <div class="dress-code-option" data-value="بدلة عمل+فست+كاب" onclick="selectDressCode(this, 'بدلة عمل+فست+كاب')">
                                <img src="{{ url_for('static', filename='images/بدلة+فست+كاب.png') }}" alt="بدلة+فست+كاب" class="dress-code-icon-img">
                                <div>بدلة+فست+كاب</div>
                            </div>
                            <div class="dress-code-option" data-value="دركي" onclick="selectDressCode(this, 'دركي')">
                                <img src="{{ url_for('static', filename='images/دركي.png') }}" alt="دركي" class="dress-code-icon-img">
                                <div>دركي</div>
                            </div>
                            <div class="dress-code-option" data-value="دركي+شماغ" onclick="selectDressCode(this, 'دركي+شماغ')">
                                <img src="{{ url_for('static', filename='images/دركي+شماغ.png') }}" alt="دركي+شماغ" class="dress-code-icon-img">
                                <div>دركي+شماغ</div>
                            </div>
                            <div class="dress-code-option" data-value="بدلة رسمية" onclick="selectDressCode(this, 'بدلة رسمية')">
                                <img src="{{ url_for('static', filename='images/بدلة رسمة.png') }}" alt="بدلة رسمية" class="dress-code-icon-img">
                                <div>بدلة رسمية</div>
                            </div>
                        </div>
                        <input type="hidden" id="dress_code" name="dress_code" required>
                    </div>
                </div>

                <!-- قسم تفاصيل الدعوة -->
                <div class="form-section mb-3">
                    <div class="section-header">
                        <i class="fas fa-envelope me-2"></i>
                        تفاصيل الدعوة
                    </div>
                    <div class="section-body">
                        <!-- رقم وتاريخ الكتاب -->
                        <div class="row g-2">
                            <div class="col-6">
                                <label class="professional-label">
                                    <i class="fas fa-file-alt me-2"></i>
                                    رقم الكتاب
                                </label>
                                <input type="text" class="form-control professional-input" id="book_number" name="book_number"
                                       required dir="ltr" placeholder="أدخل رقم الكتاب أو 0 إذا لم يكن هناك رقم">


                            </div>
                            <div class="col-6">
                                <label class="professional-label">
                                    <i class="fas fa-calendar-check me-2"></i>
                                    تاريخ الكتاب
                                </label>
                                <div class="modern-date-input-container">
                                    <input type="date" class="modern-date-input" id="book_date" name="book_date" required>
                                    <div class="modern-date-overlay" onclick="openDatePicker('book_date')">
                                        <i class="fas fa-calendar-check"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قسم الأزرار -->
                <div class="form-section">
                    <div class="section-header">
                        <i class="fas fa-cogs me-2"></i>
                        إجراءات النموذج
                    </div>
                    <div class="section-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="modern-submit-btn">
                                <i class="fas fa-save"></i>
                                <span>حفظ الاجتماع</span>
                            </button>
                            <button type="button" class="modern-reset-btn" onclick="resetForm()">
                                <i class="fas fa-undo"></i>
                                <span>إعادة تعيين</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </form>
</div>
{% endblock %}

{% block extra_js %}
<!-- SweetAlert2 JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- إصلاح شامل لأزرار صفحة إدخال الاجتماع -->
<script src="{{ url_for('static', filename='js/add_meeting_buttons_fix.js') }}"></script>

<script src="{{ url_for('static', filename='js/add_meeting.js') }}"></script>
<script src="{{ url_for('static', filename='js/enhanced-date-picker.js') }}"></script>

<!-- تحويل بسيط للأرقام العربية -->
<script>
    // تنبيه للتأكد من أن JavaScript يعمل
    console.log('🚀 JavaScript يعمل بنجاح!');

    // دالة بسيطة لاختيار نوع اللباس - مباشرة من HTML
    function selectDressCode(element, value) {
        alert('تم النقر على نوع اللباس: ' + value);

        // إزالة selected من جميع الخيارات
        var allOptions = document.querySelectorAll('.dress-code-option');
        for (var i = 0; i < allOptions.length; i++) {
            allOptions[i].classList.remove('selected');
            allOptions[i].style.borderColor = '';
            allOptions[i].style.backgroundColor = '';
            allOptions[i].style.transform = '';
        }

        // تحديد الخيار المنقور
        element.classList.add('selected');
        element.style.borderColor = '#28a745';
        element.style.backgroundColor = 'rgba(40, 167, 69, 0.3)';
        element.style.transform = 'scale(1.05)';

        // حفظ القيمة في الحقل المخفي
        var hiddenInput = document.getElementById('dress_code');
        if (hiddenInput) {
            hiddenInput.value = value;
        }

        console.log('تم حفظ نوع اللباس:', value);
    }

    // قائمة جهات الدعوة
    var invitingPartiesList = [
        "عطوفة رئيس هيئة الاركان المشتركة",
        "المساعد للعمليات والتدريب",
        "المساعد للادارة والقوى البشرية",
        "مكتب المساعد للتخطيط والتنظيم والموارد الدفاعية",
        "وكيل قوة القوات المسلحة",
        "قيادة المنطقة العسكرية الوسطى",
        "قيادة المنطقة العسكرية الشمالية",
        "قيادة المنطقة العسكرية الشرقية",
        "قيادة المنطقة العسكرية الجنوبية",
        "قيادة قوات الملك عبدالله الثاني الخاصة الملكية",
        "قيادة القوات الجوية الملكية",
        "قيادة القوات البحرية الملكية",
        "مديرية الاستخبارات العامة",
        "مديرية الامن العام",
        "مديرية الدفاع المدني",
        "مديرية الدائرة المالية",
        "مديرية ديوان القيادة العامة",
        "مديرية الشؤون الادارية",
        "مديرية الشؤون القانونية",
        "مديرية التدريب والتطوير"
    ];

    // دالة عرض قائمة جهات الدعوة
    function showInvitingPartiesList() {
        alert('تم النقر على حقل جهة الدعوة - ستظهر القائمة');

        var dropdown = document.getElementById('inviting_party_dropdown');
        if (!dropdown) return;

        dropdown.innerHTML = '';

        // عرض أول 10 جهات
        for (var i = 0; i < Math.min(10, invitingPartiesList.length); i++) {
            var item = document.createElement('div');
            item.className = 'dropdown-item';
            item.style.cursor = 'pointer';
            item.textContent = invitingPartiesList[i];

            item.onclick = function() {
                document.getElementById('inviting_party').value = this.textContent;
                dropdown.style.display = 'none';
            };

            dropdown.appendChild(item);
        }

        // إضافة خيار "أخرى"
        var otherItem = document.createElement('div');
        otherItem.className = 'dropdown-item';
        otherItem.style.cursor = 'pointer';
        otherItem.style.borderTop = '2px solid #e9ecef';
        otherItem.style.fontWeight = 'bold';
        otherItem.style.color = '#007bff';
        otherItem.innerHTML = '<i class="fas fa-edit me-2"></i>أخرى (كتابة يدوية)';

        otherItem.onclick = function() {
            document.getElementById('inviting_party').value = '';
            document.getElementById('inviting_party').focus();
            dropdown.style.display = 'none';
        };

        dropdown.appendChild(otherItem);
        dropdown.style.display = 'block';
    }

    // دالة فلترة جهات الدعوة
    function filterInvitingPartiesList(searchText) {
        var dropdown = document.getElementById('inviting_party_dropdown');
        if (!dropdown) return;

        dropdown.innerHTML = '';

        var filtered = [];
        if (searchText && searchText.length > 0) {
            for (var i = 0; i < invitingPartiesList.length; i++) {
                if (invitingPartiesList[i].includes(searchText)) {
                    filtered.push(invitingPartiesList[i]);
                }
            }
        } else {
            filtered = invitingPartiesList.slice(0, 10);
        }

        // عرض النتائج المفلترة
        for (var i = 0; i < Math.min(10, filtered.length); i++) {
            var item = document.createElement('div');
            item.className = 'dropdown-item';
            item.style.cursor = 'pointer';
            item.textContent = filtered[i];

            item.onclick = function() {
                document.getElementById('inviting_party').value = this.textContent;
                dropdown.style.display = 'none';
            };

            dropdown.appendChild(item);
        }

        // إضافة خيار "أخرى"
        var otherItem = document.createElement('div');
        otherItem.className = 'dropdown-item';
        otherItem.style.cursor = 'pointer';
        otherItem.style.borderTop = '2px solid #e9ecef';
        otherItem.style.fontWeight = 'bold';
        otherItem.style.color = '#007bff';
        otherItem.innerHTML = '<i class="fas fa-edit me-2"></i>أخرى (كتابة يدوية)';

        otherItem.onclick = function() {
            document.getElementById('inviting_party').value = '';
            document.getElementById('inviting_party').focus();
            dropdown.style.display = 'none';
        };

        dropdown.appendChild(otherItem);
        dropdown.style.display = 'block';
    }

    // دالة فتح منتقي الوقت
    function openTimePicker(inputId) {
        var input = document.getElementById(inputId);
        if (input) {
            input.focus();
            input.click();
        }
    }

    // تحسين عرض التاريخ بصيغة 2025/7/19
    function setupDateFormatting() {
        console.log('📅 إعداد صيغة التاريخ...');

        const dateInputs = document.querySelectorAll('input[type="date"]');

        dateInputs.forEach(function(dateInput) {
            // إضافة مستمع للتغيير
            dateInput.addEventListener('change', function() {
                if (this.value) {
                    const date = new Date(this.value);
                    const year = date.getFullYear();
                    const month = date.getMonth() + 1;
                    const day = date.getDate();

                    // تغيير الصيغة إلى يوم/شهر/سنة
                    const formattedDate = `${day}/${month}/${year}`;
                    console.log('📅 التاريخ المحدد:', formattedDate);

                    // إضافة تلميح للمستخدم
                    this.title = `التاريخ: ${formattedDate}`;

                    // عرض تأكيد مؤقت
                    showDateNotification(formattedDate);
                }
            });

            // إضافة مستمع للتركيز
            dateInput.addEventListener('focus', function() {
                console.log('🔍 تم التركيز على حقل التاريخ');
            });
        });

        console.log('✅ تم إعداد صيغة التاريخ');
    }

    // عرض إشعار التاريخ
    function showDateNotification(formattedDate) {
        // إزالة أي إشعارات سابقة
        const existingNotifications = document.querySelectorAll('.date-notification');
        existingNotifications.forEach(n => n.remove());

        // إنشاء إشعار جديد
        const notification = document.createElement('div');
        notification.className = 'date-notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 12px 18px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 500;
            z-index: 9999;
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
            transform: translateX(100%);
            transition: all 0.4s ease;
        `;
        notification.innerHTML = `
            <i class="fas fa-calendar-check me-2"></i>
            تم تحديد التاريخ: <strong>${formattedDate}</strong>
        `;

        document.body.appendChild(notification);

        // إظهار الإشعار
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // إخفاء الإشعار بعد 3 ثوان
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 400);
        }, 3000);
    }

    // تحويل بسيط للأرقام في الحقول فقط
    document.addEventListener('input', function(e) {
        if (e.target.matches('input, textarea, select') && e.target.value) {
            const arabicDigits = '٠١٢٣٤٥٦٧٨٩';
            const englishDigits = '0123456789';
            let value = e.target.value;

            for (let i = 0; i < arabicDigits.length; i++) {
                value = value.replace(new RegExp(arabicDigits[i], 'g'), englishDigits[i]);
            }

            if (value !== e.target.value) {
                const start = e.target.selectionStart;
                e.target.value = value;
                e.target.setSelectionRange(start, start);
            }
        }
    });

    // تحسين Date Picker و Time Picker
    document.addEventListener('DOMContentLoaded', function() {
        // إعداد صيغة التاريخ أولاً
        setupDateFormatting();

        // إعداد التاريخ الافتراضي (اليوم)
        const today = new Date();
        const meetingDateInput = document.getElementById('meeting_date');
        const bookDateInput = document.getElementById('book_date');
        const meetingTimeInput = document.getElementById('meeting_time');

        if (meetingDateInput && !meetingDateInput.value) {
            meetingDateInput.value = today.toISOString().split('T')[0];
        }
        if (bookDateInput && !bookDateInput.value) {
            bookDateInput.value = today.toISOString().split('T')[0];
        }
        if (meetingTimeInput && !meetingTimeInput.value) {
            // تعيين وقت افتراضي (9:00 صباحاً)
            meetingTimeInput.value = '09:00';
        }

        // إضافة التحقق الفوري من تاريخ الكتاب
        function validateBookDateInstant() {
            if (!bookDateInput || !meetingDateInput) return;
            if (!bookDateInput.value || !meetingDateInput.value) return;

            const bookDate = new Date(bookDateInput.value);
            const meetingDate = new Date(meetingDateInput.value);

            if (bookDate > meetingDate) {
                bookDateInput.classList.add('is-invalid');
                showBookDateError();
            } else {
                bookDateInput.classList.remove('is-invalid');
                hideBookDateError();
            }
        }

        function showBookDateError() {
            hideBookDateError();
            const errorDiv = document.createElement('div');
            errorDiv.className = 'invalid-feedback';
            errorDiv.id = 'book-date-error';
            errorDiv.innerHTML = '<i class="fas fa-exclamation-triangle me-1"></i>تاريخ الكتاب يجب أن يكون مساوياً أو أقل من تاريخ الاجتماع';
            bookDateInput.parentNode.appendChild(errorDiv);
        }

        function hideBookDateError() {
            const existingError = document.getElementById('book-date-error');
            if (existingError) {
                existingError.remove();
            }
        }

        // إضافة مستمعي الأحداث للتحقق الفوري
        if (bookDateInput) {
            bookDateInput.addEventListener('change', validateBookDateInstant);
            bookDateInput.addEventListener('blur', validateBookDateInstant);
        }
        if (meetingDateInput) {
            meetingDateInput.addEventListener('change', validateBookDateInstant);
        }

        // تحسين تفاعل Date Picker و Time Picker
        const dateTimeInputs = document.querySelectorAll('input[type="date"], input[type="time"]');
        dateTimeInputs.forEach(input => {
            input.addEventListener('change', function() {
                // تأثير بصري عند اختيار التاريخ/الوقت
                this.style.borderColor = '#28a745';
                this.style.boxShadow = '0 0 0 3px rgba(40, 167, 69, 0.1)';

                // إزالة التأثير بعد ثانية
                setTimeout(() => {
                    this.style.borderColor = '#1B4332';
                    this.style.boxShadow = '0 0 0 3px rgba(27, 67, 50, 0.1)';
                }, 1000);

                // إظهار رسالة تأكيد
                showMeetingDateTimeSelectedMessage(this);
            });

            // فتح Picker عند التركيز
            input.addEventListener('focus', function() {
                if (this.showPicker) {
                    this.showPicker();
                }
            });
        });
    });

    // إظهار رسالة تأكيد اختيار التاريخ/الوقت - محسنة
    function showMeetingDateTimeSelectedMessage(input) {
        let message = '';


        if (input.type === 'date') {
            const selectedDate = new Date(input.value);
            // yyyy/m/d format, English digits only
            const y = selectedDate.getFullYear();
            const m = selectedDate.getMonth() + 1;
            const d = selectedDate.getDate();
            const formattedDate = `${y}/${m}/${d}`;
            message = `📅 تم اختيار التاريخ: ${formattedDate}`;
        } else if (input.type === 'time') {
            // Always show time in 24h, English digits only
            const timeValue = input.value;
            // timeValue is already in HH:mm (24h) from input[type="time"]
            // But may contain Arabic digits if user pasted, so force English
            const englishTime = timeValue.replace(/[٠-٩]/g, d => '0123456789'['٠١٢٣٤٥٦٧٨٩'.indexOf(d)]);
            message = `🕐 تم اختيار الوقت: ${englishTime}`;
        }

        // استخدام النظام الموحد للإشعارات
        showNotification(message, 'success');
    }

    // وظائف مساعدة للتواريخ السريعة المحسنة
    function setQuickDate(type) {
        console.log('تعيين تاريخ سريع:', type);
        alert('تم النقر على زر التاريخ: ' + type);

        const dateInput = document.getElementById('meeting_date');
        if (!dateInput) {
            console.error('لم يتم العثور على حقل التاريخ');
            return;
        }

        const today = new Date();
        let targetDate;

        switch(type) {
            case 'today':
                targetDate = today;
                break;
            case 'tomorrow':
                targetDate = new Date(today);
                targetDate.setDate(today.getDate() + 1);
                break;
            case 'next_week':
                targetDate = new Date(today);
                targetDate.setDate(today.getDate() + 7);
                break;
        }

        if (targetDate) {
            const dateString = targetDate.toISOString().split('T')[0];
            dateInput.value = dateString;

            // إطلاق أحداث التغيير
            dateInput.dispatchEvent(new Event('change', { bubbles: true }));
            dateInput.dispatchEvent(new Event('input', { bubbles: true }));

            // تحديث حالة الأزرار
            updateQuickDateButtons(type);

            // تأثير بصري
            animateInput(dateInput);

            // رسالة تأكيد
            showDateTimeConfirmation('date', dateString);

            console.log('تم تعيين التاريخ:', dateString);
        }
    }

    function setQuickTime(time) {
        console.log('تعيين وقت سريع:', time);
        alert('تم النقر على زر الوقت: ' + time);

        const timeInput = document.getElementById('meeting_time');
        if (!timeInput) {
            console.error('لم يتم العثور على حقل الوقت');
            return;
        }

        timeInput.value = time;

        // إطلاق أحداث التغيير
        timeInput.dispatchEvent(new Event('change', { bubbles: true }));
        timeInput.dispatchEvent(new Event('input', { bubbles: true }));

        // تحديث حالة الأزرار
        updateQuickTimeButtons(time);

        // تأثير بصري
        animateInput(timeInput);

        // رسالة تأكيد
        showDateTimeConfirmation('time', time);

        console.log('تم تعيين الوقت:', time);
    }

    // تحديث حالة أزرار التاريخ
    function updateQuickDateButtons(activeType) {
        const buttons = document.querySelectorAll('.quick-date-btn');
        buttons.forEach(btn => {
            btn.classList.remove('active');
            const btnType = btn.onclick.toString().match(/'([^']+)'/)[1];
            if (btnType === activeType) {
                btn.classList.add('active');
            }
        });
    }

    // تحديث حالة أزرار الوقت
    function updateQuickTimeButtons(activeTime) {
        const buttons = document.querySelectorAll('.quick-time-btn');
        buttons.forEach(btn => {
            btn.classList.remove('active');
            const btnTime = btn.onclick.toString().match(/'([^']+)'/)[1];
            if (btnTime === activeTime) {
                btn.classList.add('active');
            }
        });
    }

    // فتح منتقي التاريخ المحسن
    function openDatePicker(inputId) {
        console.log('فتح منتقي التاريخ:', inputId);

        const input = document.getElementById(inputId);
        if (!input) {
            console.error('لم يتم العثور على حقل التاريخ:', inputId);
            return;
        }

        // تأثير بصري فوري
        const overlay = input.nextElementSibling;
        if (overlay && overlay.classList.contains('modern-date-overlay')) {
            overlay.style.transform = 'translateY(-50%) scale(1.3)';
            overlay.style.background = 'linear-gradient(135deg, #17a2b8 0%, #138496 100%)';

            setTimeout(() => {
                overlay.style.transform = 'translateY(-50%) scale(1.1)';
            }, 200);
        }

        // محاولة فتح منتقي التاريخ
        try {
            input.focus();

            // للمتصفحات الحديثة
            if (input.showPicker && typeof input.showPicker === 'function') {
                input.showPicker();
                console.log('تم فتح منتقي التاريخ الحديث');
            } else {
                // للمتصفحات القديمة
                input.click();
                console.log('تم فتح منتقي التاريخ التقليدي');
            }

            // تأثير اهتزاز خفيف
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }

        } catch (error) {
            console.error('خطأ في فتح منتقي التاريخ:', error);

            // محاولة بديلة
            setTimeout(() => {
                input.click();
            }, 100);
        }
    }

    // فتح منتقي الوقت المحسن
    function openTimePicker(inputId) {
        console.log('فتح منتقي الوقت:', inputId);

        const input = document.getElementById(inputId);
        if (!input) {
            console.error('لم يتم العثور على حقل الوقت:', inputId);
            return;
        }

        // تأثير بصري فوري
        const overlay = input.nextElementSibling;
        if (overlay && overlay.classList.contains('modern-time-overlay')) {
            overlay.style.transform = 'translateY(-50%) scale(1.3)';
            overlay.style.background = 'linear-gradient(135deg, #17a2b8 0%, #138496 100%)';

            setTimeout(() => {
                overlay.style.transform = 'translateY(-50%) scale(1.1)';
            }, 200);
        }

        // محاولة فتح منتقي الوقت
        try {
            input.focus();

            // للمتصفحات الحديثة
            if (input.showPicker && typeof input.showPicker === 'function') {
                input.showPicker();
                console.log('تم فتح منتقي الوقت الحديث');
            } else {
                // للمتصفحات القديمة
                input.click();
                console.log('تم فتح منتقي الوقت التقليدي');
            }

            // تأثير اهتزاز خفيف
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }

        } catch (error) {
            console.error('خطأ في فتح منتقي الوقت:', error);

            // محاولة بديلة
            setTimeout(() => {
                input.click();
            }, 100);
        }
    }

    // تأثير بصري للحقول
    function animateInput(input) {
        const container = input.closest('.modern-date-input-container, .modern-time-input-container, .date-input-container, .time-input-container');
        if (container) {
            container.style.transform = 'scale(1.02)';
            container.style.boxShadow = '0 8px 25px rgba(27, 67, 50, 0.3)';

            setTimeout(() => {
                container.style.transform = 'scale(1)';
                container.style.boxShadow = '';
            }, 300);
        }
    }

    // تحسين تفاعل الأزرار عند التحميل
    document.addEventListener('DOMContentLoaded', function() {
        // تحسين أزرار التاريخ
        const dateButtons = document.querySelectorAll('.quick-date-btn');
        dateButtons.forEach((btn, index) => {
            btn.style.opacity = '0';
            btn.style.transform = 'translateY(20px)';

            setTimeout(() => {
                btn.style.transition = 'all 0.4s ease';
                btn.style.opacity = '1';
                btn.style.transform = 'translateY(0)';
            }, index * 100);
        });

        // تحسين أزرار الوقت
        const timeButtons = document.querySelectorAll('.quick-time-btn');
        timeButtons.forEach((btn, index) => {
            btn.style.opacity = '0';
            btn.style.transform = 'translateY(20px)';

            setTimeout(() => {
                btn.style.transition = 'all 0.4s ease';
                btn.style.opacity = '1';
                btn.style.transform = 'translateY(0)';
            }, (index + 3) * 100);
        });

        // تحسين أزرار الحفظ
        const submitBtn = document.querySelector('.modern-submit-btn');
        const resetBtn = document.querySelector('.modern-reset-btn');

        if (submitBtn) {
            submitBtn.style.opacity = '0';
            submitBtn.style.transform = 'translateY(30px)';

            setTimeout(() => {
                submitBtn.style.transition = 'all 0.6s ease';
                submitBtn.style.opacity = '1';
                submitBtn.style.transform = 'translateY(0)';
            }, 800);
        }

        if (resetBtn) {
            resetBtn.style.opacity = '0';
            resetBtn.style.transform = 'translateY(30px)';

            setTimeout(() => {
                resetBtn.style.transition = 'all 0.6s ease';
                resetBtn.style.opacity = '1';
                resetBtn.style.transform = 'translateY(0)';
            }, 1000);
        }
    });

    // وظيفة رسالة التأكيد للتاريخ والوقت - محسنة
    function showDateTimeConfirmation(type, value) {
        let message;

        if (type === 'date') {
            const date = new Date(value);
            // yyyy/m/d format, English digits only
            const y = date.getFullYear();
            const m = date.getMonth() + 1;
            const d = date.getDate();
            const formattedDate = `${y}/${m}/${d}`;
            message = `📅 تم اختيار التاريخ: ${formattedDate}`;
        } else if (type === 'time') {
            // تحويل الوقت إلى النظام العسكري
            let timeValue = value;
            // timeValue is already in HH:mm (24h) from input[type="time"]
            // But may contain Arabic digits if user pasted, so force English
            timeValue = timeValue.replace(/[٠-٩]/g, d => '0123456789'['٠١٢٣٤٥٦٧٨٩'.indexOf(d)]);

            // تحويل إلى النظام العسكري (إزالة النقطتين)
            const [hours, minutes] = timeValue.split(':');
            const militaryTime = `${hours.padStart(2, '0')}${minutes.padStart(2, '0')}`;

            message = `🕐 تم اختيار الوقت: ${militaryTime}`;
        }

        // استخدام النظام الموحد للإشعارات
        showNotification(message, 'success');
    }

    // ===== وظائف رفع الملفات =====

    // تم نقل كود رفع الملفات إلى ملف add_meeting_buttons_fix.js
    // لتجنب التضارب



    // ===== وظائف التأكيد والحفظ =====

    // وظيفة تأكيد الحفظ
    function confirmSave(event) {
        event.preventDefault(); // منع الإرسال المباشر

        // التحقق من تاريخ الكتاب أولاً
        if (typeof validateBookDateGlobal === 'function') {
            if (!validateBookDateGlobal()) {
                return false; // إيقاف الحفظ إذا كان تاريخ الكتاب غير صحيح
            }
        }

        // التحقق من تعبئة الحقول المطلوبة
        const requiredFields = [
            { id: 'subject', name: 'موضوع الاجتماع' },
            { id: 'meeting_type', name: 'نوع الفعالية' },
            { id: 'meeting_date', name: 'تاريخ الاجتماع' },
            { id: 'meeting_time', name: 'وقت الاجتماع' },
            { id: 'location', name: 'مكان الاجتماع' },
            { id: 'inviting_party', name: 'جهة الدعوة' },
            { id: 'book_number', name: 'رقم الكتاب' },
            { id: 'book_date', name: 'تاريخ الكتاب' }
        ];

        let missingFields = [];
        requiredFields.forEach(field => {
            const element = document.getElementById(field.id);
            if (!element || !element.value.trim()) {
                missingFields.push(field.name);
            }
        });

        if (missingFields.length > 0) {
            Swal.fire({
                title: '⚠️ حقول مطلوبة',
                html: `يرجى تعبئة الحقول التالية:<br><br>• ${missingFields.join('<br>• ')}`,
                icon: 'warning',
                confirmButtonText: 'موافق',
                confirmButtonColor: '#f39c12',
                background: '#fff',
                customClass: {
                    popup: 'arabic-popup'
                }
            });
            return false;
        }

        // التحقق من اختيار نوع اللباس
        const dressCode = document.getElementById('dress_code');
        if (!dressCode || !dressCode.value) {
            Swal.fire({
                title: '⚠️ نوع اللباس مطلوب',
                text: 'يرجى اختيار نوع اللباس المطلوب للاجتماع',
                icon: 'warning',
                confirmButtonText: 'موافق',
                confirmButtonColor: '#f39c12',
                background: '#fff',
                customClass: {
                    popup: 'arabic-popup'
                }
            });
            return false;
        }

        // رسالة التأكيد
        Swal.fire({
            title: '💾 تأكيد الحفظ',
            text: 'هل تريد حفظ هذا الاجتماع؟',
            icon: 'question',
            showCancelButton: true,
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#dc3545',
            confirmButtonText: '✅ نعم، احفظ',
            cancelButtonText: '❌ إلغاء',
            background: '#fff',
            customClass: {
                popup: 'arabic-popup'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                // إظهار رسالة التحميل
                Swal.fire({
                    title: '⏳ جاري الحفظ...',
                    text: 'يرجى الانتظار',
                    icon: 'info',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    background: '#fff',
                    customClass: {
                        popup: 'arabic-popup'
                    }
                });

                // إرسال النموذج
                submitFormWithSuccess();
            }
        });

        return false;
    }

    // وظيفة إرسال النموذج مع معالجة النجاح
    function submitFormWithSuccess() {
        const form = document.getElementById('meetingForm');
        const formData = new FormData(form);

        fetch(form.action || window.location.pathname, {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(data => {
            // التحقق من نجاح العملية
            if (data.includes('تم إضافة الاجتماع بنجاح') || data.includes('success')) {
                // رسالة النجاح
                Swal.fire({
                    title: '🎉 تم الحفظ بنجاح!',
                    text: 'تم إضافة الاجتماع بنجاح',
                    icon: 'success',
                    confirmButtonText: 'ممتاز',
                    confirmButtonColor: '#28a745',
                    background: '#fff',
                    customClass: {
                        popup: 'arabic-popup'
                    }
                }).then(() => {
                    // مسح جميع الحقول
                    clearAllFields();
                });
            } else {
                // رسالة خطأ
                Swal.fire({
                    title: '❌ خطأ في الحفظ',
                    text: 'حدث خطأ أثناء حفظ الاجتماع. يرجى المحاولة مرة أخرى.',
                    icon: 'error',
                    confirmButtonText: 'موافق',
                    confirmButtonColor: '#dc3545',
                    background: '#fff',
                    customClass: {
                        popup: 'arabic-popup'
                    }
                });
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            Swal.fire({
                title: '❌ خطأ في الاتصال',
                text: 'تعذر الاتصال بالخادم. يرجى المحاولة مرة أخرى.',
                icon: 'error',
                confirmButtonText: 'موافق',
                confirmButtonColor: '#dc3545',
                background: '#fff',
                customClass: {
                    popup: 'arabic-popup'
                }
            });
        });
    }

    // وظيفة مسح جميع الحقول
    function clearAllFields() {
        const form = document.getElementById('meetingForm');

        // مسح جميع input fields
        const inputs = form.querySelectorAll('input[type="text"], input[type="date"], input[type="time"], input[type="number"], textarea, select');
        inputs.forEach(input => {
            if (input.type === 'checkbox' || input.type === 'radio') {
                input.checked = false;
            } else {
                input.value = '';
            }
        });

        // إعادة تعيين الـ selects للخيار الأول
        const selects = form.querySelectorAll('select');
        selects.forEach(select => {
            select.selectedIndex = 0;
        });

        // تأثير بصري لإظهار المسح
        inputs.forEach((input, index) => {
            setTimeout(() => {
                input.style.transition = 'all 0.3s ease';
                input.style.transform = 'scale(0.95)';
                input.style.opacity = '0.7';

                setTimeout(() => {
                    input.style.transform = 'scale(1)';
                    input.style.opacity = '1';
                }, 150);
            }, index * 50);
        });

        // رسالة تأكيد المسح
        setTimeout(() => {
            showNotification('✨ تم مسح جميع الحقول', 'success');
        }, 500);
    }

    // نظام إشعارات بسيط ومضمون
    function showNotification(message, type) {
        try {
            console.log('عرض إشعار:', message, type);

            // إزالة الإشعارات السابقة
            const oldNotifications = document.querySelectorAll('.my-notification');
            oldNotifications.forEach(n => n.remove());

            // إنشاء الإشعار
            const div = document.createElement('div');
            div.className = 'my-notification';

            // تحديد اللون
            let color = '#FFD700'; // ذهبي
            if (type === 'error') color = '#dc3545'; // أحمر

            div.innerHTML = `
                <div style="display: flex; align-items: center; gap: 10px;">
                    <span style="font-size: 18px;">✅</span>
                    <span style="flex: 1; font-weight: bold; color: #000;">${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()"
                            style="background: none; border: none; color: #000; font-size: 20px; cursor: pointer;">×</button>
                </div>
            `;

            div.style.cssText = `
                position: fixed !important;
                top: 20px !important;
                left: 20px !important;
                background: ${color} !important;
                padding: 15px 20px !important;
                border-radius: 8px !important;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
                z-index: 99999 !important;
                min-width: 300px !important;
                font-size: 14px !important;
                direction: rtl !important;
                text-align: right !important;
            `;

            document.body.appendChild(div);

            // إزالة بعد 3 ثوان
            setTimeout(() => {
                if (div.parentNode) {
                    div.remove();
                }
            }, 3000);

        } catch (error) {
            console.error('خطأ في الإشعار:', error);
            alert(message); // fallback
        }
    }

    // وظيفة إنشاء إشعار ذهبي
    function createGoldenNotification(message) {
        console.log('إنشاء إشعار ذهبي:', message);
        showNotification(message, 'success');
    }

    // وظيفة إعادة تعيين النموذج مع تأكيد
    function resetForm() {
        alert('تم النقر على زر إعادة التعيين');
        Swal.fire({
            title: '🔄 إعادة تعيين النموذج',
            text: 'هل تريد مسح جميع البيانات المدخلة؟',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#f39c12',
            cancelButtonColor: '#6c757d',
            confirmButtonText: '✅ نعم، امسح الكل',
            cancelButtonText: '❌ إلغاء',
            background: '#fff',
            customClass: {
                popup: 'arabic-popup'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                clearAllFields();
                Swal.fire({
                    title: '✨ تم المسح',
                    text: 'تم مسح جميع البيانات بنجاح',
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false,
                    background: '#fff',
                    customClass: {
                        popup: 'arabic-popup'
                    }
                });
            }
        });
    }

    // قائمة جهات الدعوة من ملف ABCD.txt
    const invitingParties = [
        "عطوفة رئيس هيئة الاركان المشتركة",
        "المساعد للعمليات والتدريب",
        "المساعد للادارة والقوى البشرية",
        "مكتب المساعد للتخطيط والتنظيم والموارد الدفاعية",
        "وكيل قوة القوات المسلحة",
        "قيادة المنطقة العسكرية الوسطى",
        "قيادة المنطقة العسكرية الشمالية",
        "قيادة المنطقة العسكرية الشرقية",
        "قيادة المنطقة العسكرية الجنوبية",
        "قيادة قوات الملك عبدالله الثاني الخاصة الملكية",
        "قيادة القوات الجوية الملكية",
        "قيادة القوات البحرية الملكية",
        "مديرية الاستخبارات العامة",
        "مديرية الامن العام",
        "مديرية الدفاع المدني",
        "مديرية الدائرة المالية",
        "مديرية ديوان القيادة العامة",
        "مديرية الشؤون الادارية",
        "مديرية الشؤون القانونية",
        "مديرية التدريب والتطوير",
        "مديرية الاتصالات والمعلومات",
        "مديرية الهندسة العسكرية",
        "مديرية الطب العسكري",
        "مديرية التموين والنقل",
        "مديرية الاسلحة والذخيرة",
        "مديرية الشرطة العسكرية",
        "مديرية الامن والحماية",
        "مديرية الاعلام العسكري",
        "مديرية التاريخ العسكري",
        "مديرية الشؤون الدينية",
        "مديرية الرياضة العسكرية",
        "مديرية الموسيقى العسكرية",
        "كلية الامير الحسين بن عبدالله الثاني للقيادة والاركان",
        "كلية الدفاع الوطني الملكية",
        "كلية الامير فيصل للقيادة والاركان",
        "كلية الملك عبدالله الثاني للدفاع الجوي",
        "كلية الاميرة عائشة بنت الحسين العسكرية للنساء",
        "كلية الملك طلال العسكرية التقنية",
        "كلية الامير الحسن للعلوم العسكرية",
        "كلية الملك الحسين الجوية",
        "كلية الملك فيصل البحرية",
        "كلية الامير راشد للعلوم العسكرية التطبيقية",
        "مستشفى الملك الحسين الطبي",
        "مستشفى الاميرة راية العسكري",
        "مستشفى الملك طلال العسكري",
        "مستشفى الاميرة بسمة العسكري",
        "مركز الملك عبدالله الثاني للتدريب الخاص",
        "مركز الاميرة منى للتدريب الطبي العسكري",
        "مركز الملك عبدالله الثاني للتصميم والتطوير",
        "مركز الاتصالات والالكترونيات العسكرية",
        "مركز الحرب الالكترونية",
        "مركز الدفاع الجوي",
        "مركز العمليات المشتركة",
        "مركز التدريب على المحاكيات",
        "مركز اللغات العسكرية",
        "مركز الدراسات الاستراتيجية",
        "مركز التوثيق والمحفوظات العسكرية",
        "مركز الخدمات الطبية المتخصصة",
        "مركز الصيانة والاصلاح",
        "مركز النقل والمواصلات",
        "مركز الامداد والتموين",
        "مركز الاسلحة والمعدات",
        "مركز التكنولوجيا العسكرية",
        "مركز الامن السيبراني",
        "مركز المعلومات الجغرافية العسكرية",
        "مركز الطيران العسكري",
        "مركز القوات الخاصة",
        "مركز المظليين",
        "مركز القناصة",
        "مركز الحرب الحضرية"
    ];

    console.log(`📋 تم تحميل ${invitingParties.length} جهة دعوة من القائمة المحددة`);
    console.log('🔍 أول 3 جهات:', invitingParties.slice(0, 3));

    // وظيفة التعامل مع نوع اللباس - حل جذري
    function setupDressCodeSelection() {
        console.log('🚀 بدء الحل الجذري لنوع اللباس...');

        // انتظار تحميل الصفحة كاملة
        setTimeout(function() {
            const options = document.querySelectorAll('.dress-code-option');
            const hiddenInput = document.getElementById('dress_code');

            console.log('🔍 عدد خيارات نوع اللباس:', options.length);
            console.log('🔍 الحقل المخفي:', hiddenInput ? 'موجود' : 'غير موجود');

            if (options.length === 0) {
                console.log('❌ لا توجد خيارات نوع اللباس!');
                return;
            }

            // إضافة مستمع لكل خيار
            options.forEach(function(option, index) {
                const value = option.getAttribute('data-value');
                console.log(`📌 خيار ${index + 1}: ${value}`);

                // إزالة أي مستمعات سابقة
                option.onclick = null;

                // إضافة مستمع جديد
                option.onclick = function() {
                    console.log('🖱️ تم النقر على:', value);
                    alert('تم النقر على نوع اللباس: ' + value); // تنبيه للاختبار

                    // إزالة selected من جميع الخيارات
                    options.forEach(function(opt) {
                        opt.classList.remove('selected');
                        opt.style.borderColor = '';
                        opt.style.backgroundColor = '';
                    });

                    // إضافة selected للخيار المنقور
                    this.classList.add('selected');

                    // تحديث الحقل المخفي
                    if (hiddenInput) {
                        hiddenInput.value = value;
                        console.log('✅ تم حفظ القيمة:', value);
                    }

                    // تأكيد بصري قوي
                    this.style.borderColor = '#28a745 !important';
                    this.style.backgroundColor = 'rgba(40, 167, 69, 0.3) !important';
                    this.style.transform = 'scale(1.05)';
                };
            });

            console.log('✅ تم تفعيل جميع خيارات نوع اللباس بنجاح!');
        }, 1000); // انتظار ثانية واحدة
    }

    // وظيفة تصفية جهات الدعوة حسب النص المدخل
    function filterInvitingParties(searchText) {
        if (!searchText || searchText.length < 1) {
            return invitingParties.slice(0, 15); // عرض أول 15 جهة
        }

        const filtered = invitingParties.filter(party =>
            party.includes(searchText) || party.toLowerCase().includes(searchText.toLowerCase())
        );

        return filtered.slice(0, 15); // عرض أول 15 نتيجة
    }

    // وظيفة عرض قائمة جهات الدعوة
    function showInvitingPartiesDropdown(filteredParties, inputElement) {
        const dropdown = document.getElementById('inviting_party_dropdown');
        if (!dropdown) return;

        dropdown.innerHTML = '';

        if (filteredParties.length === 0) {
            const noResultsItem = document.createElement('div');
            noResultsItem.className = 'dropdown-item text-muted';
            noResultsItem.textContent = 'لا توجد نتائج';
            dropdown.appendChild(noResultsItem);
        } else {
            // إضافة جهات الدعوة المفلترة
            filteredParties.forEach(party => {
                const item = document.createElement('div');
                item.className = 'dropdown-item';
                item.style.cursor = 'pointer';
                item.textContent = party;

                item.addEventListener('click', function() {
                    inputElement.value = party;
                    dropdown.style.display = 'none';
                });

                dropdown.appendChild(item);
            });
        }

        // إضافة خيار "أخرى" دائماً في النهاية
        const otherItem = document.createElement('div');
        otherItem.className = 'dropdown-item';
        otherItem.style.cursor = 'pointer';
        otherItem.style.borderTop = '2px solid #e9ecef';
        otherItem.style.fontWeight = 'bold';
        otherItem.style.color = '#007bff';
        otherItem.innerHTML = '<i class="fas fa-edit me-2"></i>أخرى (كتابة يدوية)';

        otherItem.addEventListener('click', function() {
            inputElement.value = '';
            inputElement.focus();
            dropdown.style.display = 'none';
        });

        dropdown.appendChild(otherItem);
        dropdown.style.display = 'block';
    }

    // وظيفة إخفاء قائمة جهات الدعوة
    function hideInvitingPartiesDropdown() {
        const dropdown = document.getElementById('inviting_party_dropdown');
        if (dropdown) {
            setTimeout(() => {
                dropdown.style.display = 'none';
            }, 200); // تأخير قصير للسماح بالنقر على العناصر
        }
    }

    // تفعيل autocomplete لجهة الدعوة عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        const invitingPartyInput = document.getElementById('inviting_party');

        if (invitingPartyInput) {
            // جلب جهات الدعوة عند التركيز على الحقل
            invitingPartyInput.addEventListener('focus', function() {
                console.log('🔍 تم التركيز على حقل جهة الدعوة');
                alert('تم التركيز على حقل جهة الدعوة - ستظهر القائمة'); // تنبيه للاختبار
                const filteredParties = filterInvitingParties(this.value);
                showInvitingPartiesDropdown(filteredParties, this);
            });
        }

        // تفعيل اختيار نوع اللباس - الحل الجذري
        console.log('🚀 بدء الحل الجذري لنوع اللباس...');
        setupDressCodeSelection();

            // البحث أثناء الكتابة
            invitingPartyInput.addEventListener('input', function() {
                const filteredParties = filterInvitingParties(this.value);
                showInvitingPartiesDropdown(filteredParties, this);
            });

            // إخفاء القائمة عند فقدان التركيز
            invitingPartyInput.addEventListener('blur', function() {
                hideInvitingPartiesDropdown();
            });

            // إخفاء القائمة عند الضغط على Escape
            invitingPartyInput.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    hideInvitingPartiesDropdown();
                }
            });
        }
    });

    // استدعاء إضافي للتأكد من عمل نوع اللباس
    window.addEventListener('load', function() {
        console.log('🔄 استدعاء إضافي لنوع اللباس...');
        setupDressCodeSelection();
    });

    // استدعاء ثالث بعد 3 ثوان
    setTimeout(function() {
        console.log('🔄 استدعاء ثالث لنوع اللباس...');
        setupDressCodeSelection();
    }, 3000);

    // إضافة عرض الوقت العسكري داخل حقل الإدخال
    document.addEventListener('DOMContentLoaded', function() {
        const timeInput = document.getElementById('meeting_time');
        if (timeInput) {
            // دالة لتحديث عرض الوقت
            function updateTimeDisplay() {
                if (timeInput.value && timeInput.value.includes(':')) {
                    const [hours, minutes] = timeInput.value.split(':');
                    if (hours && minutes && hours.length === 2 && minutes.length === 2) {
                        const militaryTime = `${hours.padStart(2, '0')}${minutes.padStart(2, '0')}`;

                        // حفظ القيمة الأصلية
                        timeInput.setAttribute('data-original-value', timeInput.value);
                        timeInput.style.color = '#1B4332';
                        timeInput.style.fontWeight = 'bold';

                        // إعداد المستمعات إذا لم تكن موجودة
                        if (!timeInput.hasAttribute('data-military-mode')) {
                            timeInput.setAttribute('data-military-mode', 'true');

                            // عند التركيز: إظهار الوقت الأصلي
                            timeInput.addEventListener('focus', function() {
                                if (this.getAttribute('data-original-value')) {
                                    this.value = this.getAttribute('data-original-value');
                                    this.style.color = '';
                                    this.style.fontWeight = '';
                                }
                            });

                            // عند فقدان التركيز: إظهار الوقت العسكري
                            timeInput.addEventListener('blur', function() {
                                updateTimeDisplay();
                            });
                        }

                        // عرض الوقت العسكري عند عدم التركيز
                        if (document.activeElement !== timeInput) {
                            timeInput.value = militaryTime;
                        }
                    }
                }
            }

            // إضافة مستمعي الأحداث
            timeInput.addEventListener('change', updateTimeDisplay);
            timeInput.addEventListener('input', updateTimeDisplay);

            // عرض الوقت إذا كان موجوداً مسبقاً
            updateTimeDisplay();
        }
    });
</script>
{% endblock %}
