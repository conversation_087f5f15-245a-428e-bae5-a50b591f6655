#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
تطبيق اختبار بسيط للتحقق من البيانات
"""

from flask import Flask, jsonify
import sqlite3
import os
from datetime import datetime

app = Flask(__name__)

@app.route('/test-data')
def test_data():
    try:
        db_path = 'instance/jaf_meetings.db'
        
        if not os.path.exists(db_path):
            return jsonify({'error': 'قاعدة البيانات غير موجودة'})
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود الجدول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='meeting'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            return jsonify({'error': 'جدول الاجتماعات غير موجود'})
        
        # جلب جميع الاجتماعات
        cursor.execute("SELECT * FROM meeting")
        meetings = cursor.fetchall()
        
        # جلب أسماء الأعمدة
        cursor.execute("PRAGMA table_info(meeting)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # تحويل النتائج إلى قاموس
        meetings_data = []
        for meeting in meetings:
            meeting_dict = dict(zip(columns, meeting))
            meetings_data.append(meeting_dict)
        
        # إحصائيات حسب السنة
        cursor.execute("SELECT COUNT(*) FROM meeting WHERE meeting_date LIKE '2024%'")
        count_2024 = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM meeting WHERE meeting_date LIKE '2025%'")
        count_2025 = cursor.fetchone()[0]
        
        conn.close()
        
        return jsonify({
            'success': True,
            'total_meetings': len(meetings_data),
            'meetings_2024': count_2024,
            'meetings_2025': count_2025,
            'meetings': meetings_data
        })
        
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/create-data')
def create_data():
    try:
        # إنشاء قاعدة البيانات
        db_path = 'instance/jaf_meetings.db'
        os.makedirs('instance', exist_ok=True)

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # إنشاء جدول الاجتماعات
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS meeting (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title VARCHAR(200) NOT NULL,
            subject VARCHAR(200),
            meeting_type VARCHAR(100),
            meeting_date DATE NOT NULL,
            meeting_time TIME,
            location VARCHAR(200),
            inviting_party VARCHAR(200),
            book_number VARCHAR(100),
            book_date DATE,
            dress_code VARCHAR(100),
            notes TEXT,
            is_cancelled BOOLEAN DEFAULT 0,
            is_postponed BOOLEAN DEFAULT 0,
            creator_id INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # حذف البيانات الموجودة
        cursor.execute('DELETE FROM meeting')

        # إضافة اجتماع 2024
        cursor.execute('''
        INSERT INTO meeting (title, subject, meeting_type, meeting_date, meeting_time, location, inviting_party, book_number, book_date, dress_code, notes, is_cancelled, is_postponed, creator_id)
        VALUES ('اجتماع مجلس الإدارة - مارس 2024', 'اجتماع مجلس الإدارة الشهري', 'اجتماع إداري', '2024-03-15', '10:30', 'قاعة الاجتماعات الرئيسية', 'مجلس الإدارة', '001/2024/م.د', '2024-03-10', 'الزي الرسمي', 'اجتماع دوري لمناقشة الأمور الإدارية', 0, 0, 1)
        ''')

        # إضافة اجتماعات 2025
        meetings_2025 = [
            ('اجتماع يناير 2025', '2025-01-10', 0),
            ('اجتماع فبراير 2025', '2025-02-15', 0),
            ('اجتماع مارس 2025', '2025-03-20', 1),  # ملغي
            ('اجتماع أبريل 2025', '2025-04-12', 0),
            ('اجتماع مايو 2025', '2025-05-18', 0),
            ('اجتماع يونيو 2025', '2025-06-25', 0),
            ('اجتماع يوليو 2025', '2025-07-08', 1),   # ملغي
            ('اجتماع أغسطس 2025', '2025-08-14', 0),
            ('اجتماع سبتمبر 2025', '2025-09-22', 0),
            ('اجتماع أكتوبر 2025', '2025-10-30', 0)
        ]

        for i, (title, meeting_date, is_cancelled) in enumerate(meetings_2025, 1):
            cursor.execute('''
            INSERT INTO meeting (title, subject, meeting_type, meeting_date, meeting_time, location, inviting_party, book_number, book_date, dress_code, notes, is_cancelled, is_postponed, creator_id)
            VALUES (?, ?, 'اجتماع إداري', ?, '14:00', 'قاعة المؤتمرات', 'الإدارة العامة', ?, ?, 'الزي الرسمي', ?, ?, 0, 1)
            ''', (title, f'اجتماع شهري رقم {i}', meeting_date, f'{i:03d}/2025/م.د', meeting_date, f'اجتماع شهري لسنة 2025', is_cancelled))

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': 'تم إنشاء البيانات بنجاح',
            'data_created': '1 اجتماع في 2024 و 10 اجتماعات في 2025'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)})

if __name__ == '__main__':
    print("🧪 تشغيل تطبيق الاختبار...")
    print("📍 الرابط: http://127.0.0.1:5001")
    print("🔗 إنشاء البيانات: http://127.0.0.1:5001/create-data")
    print("🔍 اختبار البيانات: http://127.0.0.1:5001/test-data")
    app.run(host='127.0.0.1', port=5001, debug=True)
