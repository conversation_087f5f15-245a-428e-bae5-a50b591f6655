/* Enhanced Military Login Styles - Jordanian Armed Forces Theme */

:root {
    /* Military Theme Colors - Jordanian Armed Forces */
    --military-primary: #1B4332;
    --military-secondary: #2C5530;
    --military-accent: #40916C;
    --military-gold: #FFD700;
    --military-silver: #C0C0C0;
    --military-dark: #081C15;
    --military-darker: #040E0A;
    --military-light: #2D5016;
    --military-border: #52B788;
    --military-glow: 0 0 25px;
    --military-text: #F8F9FA;
    --military-text-dim: #95A5A6;
    --military-success: #74C69D;
    --military-warning: #FFB700;
    --military-danger: #E63946;
    
    /* Enhanced Gradients */
    --gradient-primary: linear-gradient(135deg, var(--military-primary), var(--military-accent));
    --gradient-secondary: linear-gradient(135deg, var(--military-secondary), var(--military-light));
    --gradient-gold: linear-gradient(135deg, var(--military-gold), #FFA500);
    --gradient-bg: radial-gradient(ellipse at center, var(--military-darker) 0%, var(--military-dark) 100%);
    --gradient-overlay: linear-gradient(45deg, transparent 30%, rgba(255, 215, 0, 0.1) 50%, transparent 70%);
}

/* Enhanced Animations */
@keyframes militaryPulse {
    0%, 100% { opacity: 0.8; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.05); }
}

@keyframes militaryGlow {
    0%, 100% { box-shadow: var(--military-glow) var(--military-gold); }
    50% { box-shadow: var(--military-glow) var(--military-accent); }
}

@keyframes militaryFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
}

@keyframes militaryRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes militaryShimmer {
    0% { background-position: -200% center; }
    100% { background-position: 200% center; }
}

/* Enhanced Background Effects */
.military-bg-enhanced {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 30%, rgba(27, 67, 50, 0.4) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(64, 145, 108, 0.3) 0%, transparent 60%),
        radial-gradient(circle at 50% 20%, rgba(255, 215, 0, 0.15) 0%, transparent 40%),
        radial-gradient(circle at 30% 80%, rgba(44, 85, 48, 0.35) 0%, transparent 55%),
        linear-gradient(135deg, var(--military-darker) 0%, var(--military-dark) 50%, var(--military-primary) 100%);
    animation: militaryBgShift 20s ease-in-out infinite alternate;
}

/* Enhanced Login Box */
.login-box-enhanced {
    background: rgba(8, 28, 21, 0.98);
    border: 4px solid var(--military-border);
    border-radius: 25px;
    padding: 60px;
    width: 100%;
    max-width: 550px;
    backdrop-filter: blur(20px);
    box-shadow:
        var(--military-glow) var(--military-accent),
        inset 0 0 40px rgba(64, 145, 108, 0.15),
        0 25px 50px rgba(0, 0, 0, 0.4);
    position: relative;
    overflow: hidden;
    animation: militaryFloat 8s ease-in-out infinite;
}

.login-box-enhanced::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    background: var(--gradient-primary);
    border-radius: 25px;
    z-index: -1;
    animation: militaryGlow 5s ease-in-out infinite alternate;
}

.login-box-enhanced::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-overlay);
    animation: militaryShimmer 3s linear infinite;
    border-radius: 25px;
}

/* Enhanced Logo */
.military-logo-enhanced {
    width: 120px;
    height: 120px;
    margin: 0 auto 30px;
    background: var(--gradient-gold);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--military-dark);
    box-shadow: 
        var(--military-glow) var(--military-gold),
        inset 0 0 30px rgba(255, 215, 0, 0.4);
    animation: militaryRotate 12s linear infinite;
    position: relative;
    border: 6px solid var(--military-accent);
}

.military-logo-enhanced::before {
    content: '';
    position: absolute;
    top: -12px;
    left: -12px;
    right: -12px;
    bottom: -12px;
    border: 3px solid var(--military-gold);
    border-radius: 50%;
    animation: militaryRotate 15s linear infinite reverse;
}

.military-logo-enhanced::after {
    content: '';
    position: absolute;
    top: -18px;
    left: -18px;
    right: -18px;
    bottom: -18px;
    border: 1px solid var(--military-accent);
    border-radius: 50%;
    animation: militaryPulse 3s ease-in-out infinite;
}

/* Enhanced Form Elements */
.form-input-enhanced {
    width: 100%;
    padding: 22px 30px;
    background: rgba(8, 28, 21, 0.9);
    border: 3px solid var(--military-border);
    border-radius: 15px;
    color: var(--military-text);
    font-size: 1.2rem;
    transition: all 0.5s ease;
    font-family: 'Cairo', sans-serif;
    position: relative;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.3);
}

.form-input-enhanced:focus {
    outline: none;
    border-color: var(--military-gold);
    box-shadow: 
        var(--military-glow) var(--military-gold),
        inset 0 0 20px rgba(255, 215, 0, 0.15);
    background: rgba(27, 67, 50, 0.4);
    transform: translateY(-3px) scale(1.02);
}

/* Enhanced Button */
.login-btn-enhanced {
    width: 100%;
    padding: 25px;
    background: var(--gradient-gold);
    border: 4px solid var(--military-gold);
    border-radius: 20px;
    color: var(--military-dark);
    font-size: 1.3rem;
    font-weight: 800;
    font-family: 'Cairo', sans-serif;
    text-transform: uppercase;
    letter-spacing: 3px;
    cursor: pointer;
    transition: all 0.5s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 
        0 10px 25px rgba(255, 215, 0, 0.4),
        inset 0 0 25px rgba(255, 215, 0, 0.2);
    animation: militaryPulse 4s ease-in-out infinite;
}

.login-btn-enhanced:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 
        var(--military-glow) var(--military-gold),
        0 15px 35px rgba(255, 215, 0, 0.5);
    background: linear-gradient(135deg, var(--military-gold), #FFA500, var(--military-gold));
}

.login-btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
    transition: left 0.8s;
}

.login-btn-enhanced:hover::before {
    left: 100%;
}

/* Enhanced Security Panel */
.security-info-enhanced {
    margin-top: 40px;
    padding: 30px;
    background: rgba(8, 28, 21, 0.8);
    border: 3px solid var(--military-accent);
    border-radius: 20px;
    border-left: 8px solid var(--military-gold);
    position: relative;
    overflow: hidden;
    box-shadow: inset 0 0 20px rgba(64, 145, 108, 0.1);
}

.security-info-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-gold);
    animation: securityScan 3s linear infinite;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .login-box-enhanced {
        padding: 40px 30px;
        margin: 20px;
        max-width: 95%;
    }

    .military-logo-enhanced {
        width: 100px;
        height: 100px;
        font-size: 2.5rem;
    }

    .form-input-enhanced {
        padding: 20px 25px;
        font-size: 1.1rem;
    }

    .login-btn-enhanced {
        padding: 22px;
        font-size: 1.2rem;
        letter-spacing: 2px;
    }
}

@media (max-width: 480px) {
    .login-box-enhanced {
        padding: 35px 25px;
        margin: 15px;
    }

    .military-logo-enhanced {
        width: 90px;
        height: 90px;
        font-size: 2.2rem;
    }
}
