// ملف JavaScript مخصص لطباعة التقارير
console.log('🚀 تم تحميل ملف طباعة التقارير الجديد');

// دالة لطباعة بطاقة إحصائية
function printCard(cardType) {
    console.log('🖨️ طباعة البطاقة:', cardType);
    
    try {
        // الحصول على البيانات من الصفحة
        let title = '';
        let value = '';
        let description = '';
        let color = '#007bff';
        
        // تحديد البيانات حسب نوع البطاقة
        switch(cardType) {
            case 'total-meetings':
                const totalElement = document.getElementById('totalMeetings');
                title = 'إجمالي الاجتماعات';
                value = totalElement ? totalElement.textContent.trim() : '0';
                description = 'العدد الكلي للاجتماعات المسجلة في النظام';
                color = '#007bff';
                break;
                
            case 'upcoming-meetings':
                const upcomingElement = document.getElementById('upcomingMeetings');
                title = 'الاجتماعات القادمة';
                value = upcomingElement ? upcomingElement.textContent.trim() : '0';
                description = 'الاجتماعات المجدولة في المستقبل';
                color = '#28a745';
                break;
                
            case 'completed-meetings':
                const completedElement = document.getElementById('completedMeetings');
                title = 'الاجتماعات المكتملة';
                value = completedElement ? completedElement.textContent.trim() : '0';
                description = 'الاجتماعات التي تم عقدها بنجاح';
                color = '#17a2b8';
                break;
                
            case 'cancelled-meetings':
                const cancelledElement = document.getElementById('cancelledMeetings');
                title = 'الاجتماعات الملغية';
                value = cancelledElement ? cancelledElement.textContent.trim() : '0';
                description = 'الاجتماعات التي تم إلغاؤها';
                color = '#dc3545';
                break;
                
            case 'monthly-average':
                const avgElement = document.getElementById('monthlyAverage');
                title = 'المتوسط الشهري';
                value = avgElement ? avgElement.textContent.trim() : '0';
                description = 'معدل الاجتماعات شهرياً';
                color = '#6f42c1';
                break;
                
            case 'most-active-month':
                const activeElement = document.getElementById('mostActiveMonth');
                title = 'أكثر الشهور نشاطاً';
                value = activeElement ? activeElement.textContent.trim() : 'غير محدد';
                description = 'الشهر الذي سجل أعلى عدد من الاجتماعات';
                color = '#fd7e14';
                break;
                
            default:
                title = 'بيانات غير محددة';
                value = '0';
                description = 'لا توجد بيانات متاحة';
        }
        
        console.log('📊 البيانات:', { title, value, description, color });
        
        // إنشاء محتوى الطباعة
        const printContent = createPrintHTML(title, value, description, color);
        
        // فتح نافذة جديدة للطباعة
        const printWindow = window.open('', '_blank');
        printWindow.document.write(printContent);
        printWindow.document.close();
        
        // انتظار قصير ثم طباعة
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 500);
        
    } catch (error) {
        console.error('❌ خطأ في الطباعة:', error);
        alert('حدث خطأ أثناء الطباعة. يرجى المحاولة مرة أخرى.');
    }
}

// دالة لإنشاء HTML للطباعة
function createPrintHTML(title, value, description, color) {
    const currentDate = new Date();
    const arabicDate = currentDate.toLocaleDateString('ar-SA');
    const arabicTime = currentDate.toLocaleTimeString('ar-SA');
    
    return `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>طباعة تقرير - ${title}</title>
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }
                
                body {
                    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                    direction: rtl;
                    text-align: right;
                    padding: 40px;
                    background: white;
                    color: #333;
                }
                
                .header {
                    text-align: center;
                    margin-bottom: 40px;
                    border-bottom: 3px solid ${color};
                    padding-bottom: 20px;
                }
                
                .main-title {
                    font-size: 28px;
                    font-weight: bold;
                    color: ${color};
                    margin-bottom: 10px;
                }
                
                .subtitle {
                    font-size: 16px;
                    color: #666;
                }
                
                .card {
                    background: #f8f9fa;
                    border: 2px solid ${color};
                    border-radius: 12px;
                    padding: 40px;
                    margin: 30px 0;
                    text-align: center;
                }
                
                .value {
                    font-size: 48px;
                    font-weight: bold;
                    color: ${color};
                    margin-bottom: 20px;
                }
                
                .card-title {
                    font-size: 24px;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 15px;
                }
                
                .card-description {
                    font-size: 16px;
                    color: #666;
                    line-height: 1.6;
                }
                
                .footer {
                    margin-top: 40px;
                    text-align: center;
                    font-size: 14px;
                    color: #999;
                    border-top: 1px solid #dee2e6;
                    padding-top: 20px;
                }
                
                @media print {
                    body { padding: 20px; }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <div class="main-title">🏛️ نظام إدارة الاجتماعات</div>
                <div class="subtitle">📊 تقرير إحصائي - ${title}</div>
            </div>
            
            <div class="card">
                <div class="value">${value}</div>
                <div class="card-title">${title}</div>
                <div class="card-description">${description}</div>
            </div>
            
            <div class="footer">
                <p><strong>📅 التاريخ:</strong> ${arabicDate}</p>
                <p><strong>🕐 الوقت:</strong> ${arabicTime}</p>
                <p><strong>🏛️ المؤسسة:</strong> القوات المسلحة الأردنية</p>
            </div>
        </body>
        </html>
    `;
}

// ربط الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('📋 ربط أحداث الطباعة...');

    // البحث عن جميع أزرار الطباعة بطرق مختلفة
    const printButtons1 = document.querySelectorAll('[onclick*="printStatCard"]');
    const printButtons2 = document.querySelectorAll('.print-btn, .btn-print, [title*="طباعة"]');
    const printButtons3 = document.querySelectorAll('button[onclick], a[onclick]');

    // دمج جميع الأزرار
    const allButtons = new Set([...printButtons1, ...printButtons2, ...printButtons3]);
    const printButtons = Array.from(allButtons).filter(btn => {
        const onclick = btn.getAttribute('onclick') || '';
        const title = btn.getAttribute('title') || '';
        const text = btn.textContent || '';
        return onclick.includes('printStatCard') || title.includes('طباعة') || text.includes('طباعة');
    });

    console.log('🔍 تم العثور على', printButtons.length, 'زر طباعة');

    printButtons.forEach((button, index) => {
        console.log(`🔘 زر ${index + 1}:`, button.getAttribute('onclick'), button.textContent.trim());

        // حفظ الحدث الأصلي للمرجع
        const originalOnclick = button.getAttribute('onclick');

        // إزالة الحدث القديم
        button.removeAttribute('onclick');
        
        // إضافة حدث جديد
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // محاولة استخراج نوع البطاقة من الحدث الأصلي أولاً
            let cardType = 'total-meetings'; // افتراضي

            if (originalOnclick) {
                const match = originalOnclick.match(/printStatCard\(['"]([^'"]+)['"]\)/);
                if (match) {
                    cardType = match[1];
                    console.log('✅ تم استخراج نوع البطاقة من الحدث الأصلي:', cardType);
                }
            }

            // إذا لم نجد النوع، نبحث في العنصر
            if (cardType === 'total-meetings' && originalOnclick && !originalOnclick.includes('total-meetings')) {
                const cardElement = button.closest('.stat-card, .card, .dashboard-card');
                console.log('🔍 البحث عن نوع البطاقة في العنصر:', cardElement);

            if (cardElement) {
                // البحث في العنصر الحالي وجميع العناصر الفرعية
                if (cardElement.querySelector('#totalMeetings') || cardElement.innerHTML.includes('totalMeetings')) {
                    cardType = 'total-meetings';
                } else if (cardElement.querySelector('#upcomingMeetings') || cardElement.innerHTML.includes('upcomingMeetings')) {
                    cardType = 'upcoming-meetings';
                } else if (cardElement.querySelector('#completedMeetings') || cardElement.innerHTML.includes('completedMeetings')) {
                    cardType = 'completed-meetings';
                } else if (cardElement.querySelector('#cancelledMeetings') || cardElement.innerHTML.includes('cancelledMeetings')) {
                    cardType = 'cancelled-meetings';
                } else if (cardElement.querySelector('#monthlyAverage') || cardElement.innerHTML.includes('monthlyAverage')) {
                    cardType = 'monthly-average';
                } else if (cardElement.querySelector('#mostActiveMonth') || cardElement.innerHTML.includes('mostActiveMonth')) {
                    cardType = 'most-active-month';
                } else {
                    // البحث بناءً على النص في البطاقة
                    const cardText = cardElement.textContent || '';
                    if (cardText.includes('إجمالي الاجتماعات') || cardText.includes('إجمالي')) {
                        cardType = 'total-meetings';
                    } else if (cardText.includes('الاجتماعات القادمة') || cardText.includes('قادمة')) {
                        cardType = 'upcoming-meetings';
                    } else if (cardText.includes('الاجتماعات المكتملة') || cardText.includes('مكتملة')) {
                        cardType = 'completed-meetings';
                    } else if (cardText.includes('الاجتماعات الملغية') || cardText.includes('ملغية')) {
                        cardType = 'cancelled-meetings';
                    } else if (cardText.includes('متوسط شهري') || cardText.includes('المتوسط')) {
                        cardType = 'monthly-average';
                    } else if (cardText.includes('أكثر الشهور نشاطاً') || cardText.includes('الأكثر نشاطاً')) {
                        cardType = 'most-active-month';
                    }
                }
            }

            console.log('🎯 نوع البطاقة المحدد:', cardType);
            console.log('📄 محتوى البطاقة:', cardElement ? cardElement.textContent.substring(0, 100) : 'لا يوجد');
            printCard(cardType);
        });
    });
});

// دالة بديلة لربط الأحداث مباشرة بالأزرار
function setupDirectPrintButtons() {
    console.log('🔧 إعداد أزرار الطباعة المباشرة...');

    // قائمة بجميع أنواع البطاقات والعناصر المرتبطة بها
    const cardMappings = [
        { type: 'total-meetings', elementId: 'totalMeetings', keywords: ['إجمالي', 'total'] },
        { type: 'upcoming-meetings', elementId: 'upcomingMeetings', keywords: ['قادمة', 'upcoming'] },
        { type: 'completed-meetings', elementId: 'completedMeetings', keywords: ['مكتملة', 'completed'] },
        { type: 'cancelled-meetings', elementId: 'cancelledMeetings', keywords: ['ملغية', 'cancelled'] },
        { type: 'monthly-average', elementId: 'monthlyAverage', keywords: ['متوسط', 'average'] },
        { type: 'most-active-month', elementId: 'mostActiveMonth', keywords: ['نشاطاً', 'active'] }
    ];

    cardMappings.forEach(mapping => {
        const element = document.getElementById(mapping.elementId);
        if (element) {
            // البحث عن زر الطباعة في نفس البطاقة
            const card = element.closest('.stat-card, .card, .dashboard-card');
            if (card) {
                const printBtn = card.querySelector('button, a, .btn');
                if (printBtn) {
                    console.log(`🎯 ربط زر الطباعة للبطاقة: ${mapping.type}`);

                    // إزالة الأحداث القديمة
                    printBtn.removeAttribute('onclick');

                    // إضافة حدث جديد
                    printBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log(`🖨️ طباعة البطاقة المباشرة: ${mapping.type}`);
                        printCard(mapping.type);
                    });
                }
            }
        }
    });
}

// تشغيل الإعداد المباشر أيضاً
setTimeout(setupDirectPrintButtons, 1000);

console.log('✅ تم تحميل ملف طباعة التقارير بنجاح');
