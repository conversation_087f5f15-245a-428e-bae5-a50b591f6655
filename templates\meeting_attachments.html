<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مرفقات الاجتماع - {{ meeting.subject }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/arabic-support.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .attachments-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .meeting-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .meeting-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .meeting-info {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .attachments-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .attachment-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid #e0e0e0;
        }
        
        .attachment-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .attachment-icon {
            font-size: 48px;
            color: #667eea;
            text-align: center;
            margin-bottom: 15px;
        }
        
        .attachment-name {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
            word-break: break-word;
        }
        
        .attachment-size {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }
        
        .attachment-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a67d8, #6b46c1);
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }
        
        .btn-secondary:hover {
            background: linear-gradient(135deg, #38a169, #2f855a);
            transform: translateY(-2px);
        }
        
        .no-attachments {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .no-attachments i {
            font-size: 64px;
            color: #ddd;
            margin-bottom: 20px;
        }
        
        .back-button {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        
        .back-button:hover {
            background: linear-gradient(135deg, #3182ce, #2c5282);
            transform: translateY(-2px);
        }
        
        @media (max-width: 768px) {
            .attachments-grid {
                grid-template-columns: 1fr;
            }
            
            .meeting-title {
                font-size: 24px;
            }
            
            .attachment-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="attachments-container">
        <a href="{{ url_for('meetings_simple') }}" class="back-button">
            <i class="fas fa-arrow-right"></i>
            العودة للاجتماعات
        </a>
        
        <div class="meeting-header">
            <div class="meeting-title">مرفقات الاجتماع</div>
            <div class="meeting-info">
                <strong>{{ meeting.subject }}</strong><br>
                📅 {{ meeting.meeting_date.strftime('%d/%m/%Y') if meeting.meeting_date else 'غير محدد' }} - 
                🕐 {{ meeting.meeting_time | arabic_time if meeting.meeting_time else 'غير محدد' }}
            </div>
        </div>
        
        {% if attachments %}
            <div class="attachments-grid">
                {% for attachment in attachments %}
                <div class="attachment-card">
                    <div class="attachment-icon">
                        {% if attachment.filename.lower().endswith(('.pdf',)) %}
                            <i class="fas fa-file-pdf" style="color: #e53e3e;"></i>
                        {% elif attachment.filename.lower().endswith(('.doc', '.docx')) %}
                            <i class="fas fa-file-word" style="color: #2b6cb0;"></i>
                        {% elif attachment.filename.lower().endswith(('.xls', '.xlsx')) %}
                            <i class="fas fa-file-excel" style="color: #38a169;"></i>
                        {% elif attachment.filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')) %}
                            <i class="fas fa-file-image" style="color: #d69e2e;"></i>
                        {% else %}
                            <i class="fas fa-file" style="color: #667eea;"></i>
                        {% endif %}
                    </div>
                    
                    <div class="attachment-name">{{ attachment.filename }}</div>
                    <div class="attachment-size">
                        {% if attachment.file_size %}
                            {{ "%.1f"|format(attachment.file_size / 1024 / 1024) }} MB
                        {% else %}
                            حجم غير معروف
                        {% endif %}
                    </div>
                    
                    <div class="attachment-actions">
                        <a href="{{ url_for('view_attachment', attachment_id=attachment.id) }}" 
                           class="btn btn-primary" target="_blank">
                            <i class="fas fa-eye"></i>
                            عرض
                        </a>
                        <a href="{{ url_for('download_attachment', attachment_id=attachment.id) }}" 
                           class="btn btn-secondary">
                            <i class="fas fa-download"></i>
                            تحميل
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="no-attachments">
                <i class="fas fa-folder-open"></i>
                <h3>لا توجد مرفقات</h3>
                <p>لم يتم إرفاق أي ملفات مع هذا الاجتماع</p>
            </div>
        {% endif %}
    </div>
</body>
</html>
