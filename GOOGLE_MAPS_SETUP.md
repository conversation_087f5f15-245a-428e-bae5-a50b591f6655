# إعداد خرائط جوجل في نظام إدارة الاجتماعات

## 🗺️ المميزات الجديدة المضافة

### 1. الشعار والعنوان المحدث
- ✅ إضافة شعار "LOGO" في أعلى التقارير
- ✅ تحديث العنوان إلى "مكتب مدير الدائرة المالية"
- ✅ تطبيق التحديث على جميع صفحات الطباعة

### 2. نظام الموقع الجغرافي
- ✅ إضافة حقول الموقع الجغرافي في قاعدة البيانات:
  - `latitude` (خط العرض)
  - `longitude` (خط الطول)  
  - `address` (العنوان التفصيلي)

### 3. واجهة اختيار الموقع
- ✅ زر "اختر من الخريطة" في صفحة إضافة الاجتماع
- ✅ نافذة منبثقة تحتوي على خريطة تفاعلية
- ✅ إمكانية النقر على الخريطة لتحديد الموقع
- ✅ عرض العنوان التفصيلي تلقائياً
- ✅ حفظ الإحداثيات مع الاجتماع

### 4. عرض الموقع في التقارير
- ✅ عرض العنوان التفصيلي تحت اسم المكان
- ✅ رابط "عرض على الخريطة" يفتح Google Maps
- ✅ إزالة تكرار المكان في التقارير

## 🔧 إعداد مفتاح Google Maps API

### الخطوة 1: الحصول على مفتاح API
1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com/)
2. أنشئ مشروع جديد أو اختر مشروع موجود
3. فعّل Google Maps JavaScript API
4. أنشئ مفتاح API جديد
5. قيّد المفتاح للأمان (اختياري)

### الخطوة 2: إضافة المفتاح للنظام
في ملف `templates/add_meeting.html`، ابحث عن هذا السطر:
```javascript
script.src = 'https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&libraries=geometry&callback=initMap';
```

واستبدل `YOUR_API_KEY` بمفتاحك الفعلي:
```javascript
script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyBOti4mM-6x9WDnZIjIeyb7N2BvhCcHBr4&libraries=geometry&callback=initMap';
```

### الخطوة 3: اختبار النظام
1. شغّل التطبيق: `python run_app.py`
2. اذهب إلى صفحة إضافة اجتماع جديد
3. اضغط على زر "اختر من الخريطة"
4. انقر على موقع في الخريطة
5. اضغط "تأكيد الموقع"

## 📋 الاستخدام

### إضافة اجتماع مع موقع جغرافي:
1. املأ تفاصيل الاجتماع العادية
2. في حقل "مكان الاجتماع"، أدخل اسم المكان
3. اضغط "اختر من الخريطة" لتحديد الموقع الدقيق
4. انقر على الموقع في الخريطة
5. اضغط "تأكيد الموقع"
6. احفظ الاجتماع

### عرض الموقع في التقارير:
- سيظهر اسم المكان كما هو معتاد
- سيظهر العنوان التفصيلي تحته (إن وجد)
- سيظهر رابط "عرض على الخريطة" للمواقع المحددة جغرافياً

## 🚀 تشغيل النظام

```bash
python run_app.py
```

ثم اذهب إلى: http://127.0.0.1:5000

## 📱 إشعارات WhatsApp

النظام يدعم إرسال إشعارات WhatsApp تلقائياً عند:
- إضافة اجتماع جديد
- تعديل اجتماع
- تأجيل اجتماع  
- إلغاء اجتماع

الرقم المفعل حالياً: +9620782146467

## 🎨 التحديثات المرئية

- شعار جديد في أعلى التقارير
- عنوان محدث: "مكتب مدير الدائرة المالية"
- تحسين عرض الموقع في التقارير
- واجهة خرائط تفاعلية

## ⚠️ ملاحظات مهمة

1. **مفتاح Google Maps**: يجب الحصول على مفتاح API صالح
2. **الأمان**: قيّد استخدام المفتاح للنطاقات المسموحة
3. **التكلفة**: تحقق من أسعار Google Maps API
4. **البديل**: يمكن استخدام النظام بدون خرائط (إدخال يدوي فقط)

## 🔄 النسخ الاحتياطي

تأكد من عمل نسخة احتياطية من قاعدة البيانات قبل التحديث:
```bash
cp meetings.db meetings_backup.db
```
