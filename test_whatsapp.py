#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
اختبار مبسط لنظام إرسال WhatsApp
"""

import urllib.parse
import webbrowser
from datetime import datetime

def test_whatsapp_send():
    """اختبار إرسال رسالة WhatsApp"""
    
    print("🧪 اختبار إرسال WhatsApp...")
    
    # الرقم المستهدف
    phone = "0782146467"
    
    # تنظيف الرقم
    clean_phone = phone.replace('+', '').replace('-', '').replace(' ', '')
    
    # إضافة رمز الدولة
    if not clean_phone.startswith('962'):
        if clean_phone.startswith('0'):
            clean_phone = '962' + clean_phone[1:]
        else:
            clean_phone = '962' + clean_phone
    
    print(f"📞 الرقم النهائي: +{clean_phone}")
    
    # الرسالة
    message = f"""🧪 *رسالة اختبار مباشرة*

📅 التاريخ: {datetime.now().strftime('%Y/%m/%d')}
⏰ الوقت: {datetime.now().strftime('%H:%M:%S')}

✅ إذا وصلتك هذه الرسالة، فإن نظام الإشعارات يعمل بنجاح!

🔧 هذا اختبار مباشر من Python

---
نظام إدارة الاجتماعات
القوات المسلحة الأردنية"""
    
    print(f"💬 الرسالة: {message[:100]}...")
    
    try:
        # إنشاء رابط WhatsApp
        encoded_message = urllib.parse.quote(message)
        whatsapp_url = f"https://wa.me/{clean_phone}?text={encoded_message}"
        
        print(f"🌐 رابط WhatsApp: {whatsapp_url}")
        
        # فتح الرابط في المتصفح
        webbrowser.open(whatsapp_url)
        print("✅ تم فتح WhatsApp Web في المتصفح")
        print("📱 تحقق من المتصفح واضغط إرسال في WhatsApp")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الإرسال: {str(e)}")
        return False

if __name__ == '__main__':
    print("🚀 بدء اختبار WhatsApp المباشر...")
    print("=" * 50)
    
    success = test_whatsapp_send()
    
    print("=" * 50)
    if success:
        print("✅ تم الاختبار بنجاح!")
        print("📱 تحقق من WhatsApp الآن")
    else:
        print("❌ فشل الاختبار")
    
    input("\nاضغط Enter للخروج...")
