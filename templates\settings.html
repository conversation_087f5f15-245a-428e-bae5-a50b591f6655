{% extends "base.html" %}

{% block title %}الإعدادات - نظام اجتماعات المدير{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header fade-in-up">
        <div>
            <h2 class="page-title">
                <i class="fas fa-cog me-2"></i>
                الإعدادات
            </h2>
            <p class="text-muted mb-0">إعدادات النظام والتفضيلات الشخصية</p>
        </div>
        <div>
            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للرئيسية
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Settings Navigation -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="settings-nav">
                        <div class="nav-item active" onclick="showSettingsTab('general')">
                            <i class="fas fa-cog me-2"></i>
                            الإعدادات العامة
                        </div>
                        <div class="nav-item" onclick="showSettingsTab('notifications')">
                            <i class="fas fa-bell me-2"></i>
                            الإشعارات
                        </div>
                        <div class="nav-item" onclick="showSettingsTab('appearance')">
                            <i class="fas fa-palette me-2"></i>
                            المظهر
                        </div>
                        <div class="nav-item" onclick="showSettingsTab('security')">
                            <i class="fas fa-shield-alt me-2"></i>
                            الأمان
                        </div>
                        <div class="nav-item" onclick="showSettingsTab('backup')">
                            <i class="fas fa-database me-2"></i>
                            النسخ الاحتياطي
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Content -->
        <div class="col-md-9">
            <!-- General Settings -->
            <div id="general-settings" class="settings-tab active">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cog me-2"></i>الإعدادات العامة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="language">
                                        <option value="ar" selected>العربية</option>
                                        <option value="en">English</option>
                                    </select>
                                    <label for="language">اللغة</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="timezone">
                                        <option value="Asia/Amman" selected>توقيت عمان</option>
                                        <option value="UTC">التوقيت العالمي</option>
                                    </select>
                                    <label for="timezone">المنطقة الزمنية</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="dateFormat">
                                        <option value="dd/mm/yyyy" selected>يوم/شهر/سنة</option>
                                        <option value="mm/dd/yyyy">شهر/يوم/سنة</option>
                                        <option value="yyyy-mm-dd">سنة-شهر-يوم</option>
                                    </select>
                                    <label for="dateFormat">تنسيق التاريخ</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="timeFormat">
                                        <option value="24" selected>24 ساعة</option>
                                        <option value="12">12 ساعة</option>
                                    </select>
                                    <label for="timeFormat">تنسيق الوقت</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="autoSave" checked>
                            <label class="form-check-label" for="autoSave">
                                الحفظ التلقائي للنماذج
                            </label>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="confirmDelete" checked>
                            <label class="form-check-label" for="confirmDelete">
                                تأكيد عمليات الحذف
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notifications Settings -->
            <div id="notifications-settings" class="settings-tab">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-bell me-2"></i>إعدادات الإشعارات</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="emailNotifications" checked>
                            <label class="form-check-label" for="emailNotifications">
                                إشعارات البريد الإلكتروني
                            </label>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="browserNotifications" checked>
                            <label class="form-check-label" for="browserNotifications">
                                إشعارات المتصفح
                            </label>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="meetingReminders" checked>
                            <label class="form-check-label" for="meetingReminders">
                                تذكير بالاجتماعات
                            </label>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <select class="form-select" id="reminderTime">
                                <option value="15">15 دقيقة</option>
                                <option value="30" selected>30 دقيقة</option>
                                <option value="60">ساعة واحدة</option>
                                <option value="120">ساعتان</option>
                            </select>
                            <label for="reminderTime">وقت التذكير قبل الاجتماع</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Appearance Settings -->
            <div id="appearance-settings" class="settings-tab">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-palette me-2"></i>إعدادات المظهر</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-floating mb-3">
                            <select class="form-select" id="theme">
                                <option value="light" selected>فاتح</option>
                                <option value="dark">داكن</option>
                                <option value="auto">تلقائي</option>
                            </select>
                            <label for="theme">المظهر</label>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <select class="form-select" id="fontSize">
                                <option value="small">صغير</option>
                                <option value="medium" selected>متوسط</option>
                                <option value="large">كبير</option>
                            </select>
                            <label for="fontSize">حجم الخط</label>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="animations" checked>
                            <label class="form-check-label" for="animations">
                                تفعيل الحركات والتأثيرات
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Settings -->
            <div id="security-settings" class="settings-tab">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-shield-alt me-2"></i>إعدادات الأمان</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-floating mb-3">
                            <select class="form-select" id="sessionTimeout">
                                <option value="30">30 دقيقة</option>
                                <option value="60" selected>ساعة واحدة</option>
                                <option value="120">ساعتان</option>
                                <option value="480">8 ساعات</option>
                            </select>
                            <label for="sessionTimeout">انتهاء الجلسة</label>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="twoFactor">
                            <label class="form-check-label" for="twoFactor">
                                المصادقة الثنائية
                            </label>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="loginAlerts" checked>
                            <label class="form-check-label" for="loginAlerts">
                                تنبيهات تسجيل الدخول
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Backup Settings -->
            <div id="backup-settings" class="settings-tab">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-database me-2"></i>النسخ الاحتياطي</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="autoBackup" checked>
                            <label class="form-check-label" for="autoBackup">
                                النسخ الاحتياطي التلقائي
                            </label>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <select class="form-select" id="backupFrequency">
                                <option value="daily" selected>يومي</option>
                                <option value="weekly">أسبوعي</option>
                                <option value="monthly">شهري</option>
                            </select>
                            <label for="backupFrequency">تكرار النسخ الاحتياطي</label>
                        </div>
                        
                        <div class="text-center">
                            <button type="button" class="btn btn-jaf-primary me-2">
                                <i class="fas fa-download me-1"></i>
                                إنشاء نسخة احتياطية الآن
                            </button>
                            <button type="button" class="btn btn-outline-secondary">
                                <i class="fas fa-upload me-1"></i>
                                استعادة من نسخة احتياطية
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Save Button -->
            <div class="text-end mt-4">
                <button type="button" class="btn btn-outline-secondary me-2">إعادة تعيين</button>
                <button type="button" class="btn btn-jaf-primary">
                    <i class="fas fa-save me-1"></i>
                    حفظ جميع الإعدادات
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.settings-nav .nav-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.settings-nav .nav-item:hover {
    background-color: rgba(27, 67, 50, 0.1);
}

.settings-nav .nav-item.active {
    background: var(--jaf-gradient);
    color: white;
}

.settings-tab {
    display: none;
}

.settings-tab.active {
    display: block;
}

.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 15px;
}

.card-header {
    background: var(--jaf-gradient);
    color: white;
    border-radius: 15px 15px 0 0 !important;
}
</style>

<script>
function showSettingsTab(tabName) {
    // إخفاء جميع التبويبات
    document.querySelectorAll('.settings-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // إزالة التفعيل من جميع عناصر التنقل
    document.querySelectorAll('.settings-nav .nav-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // إظهار التبويب المحدد
    document.getElementById(tabName + '-settings').classList.add('active');
    
    // تفعيل عنصر التنقل المحدد
    event.target.classList.add('active');
}
</script>
{% endblock %}
