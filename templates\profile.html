{% extends "base.html" %}

{% block title %}الملف الشخصي - نظام اجتماعات المدير{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header fade-in-up">
        <div>
            <h2 class="page-title">
                <i class="fas fa-user me-2"></i>
                الملف الشخصي
            </h2>
            <p class="text-muted mb-0">عرض وتعديل معلومات الملف الشخصي</p>
        </div>
        <div>
            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة للرئيسية
            </a>
        </div>
    </div>

    <!-- Profile Section -->
    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <div class="profile-avatar mb-3">
                        <i class="fas fa-user-circle" style="font-size: 5rem; color: var(--jaf-primary);"></i>
                    </div>
                    <h4>{{ current_user.full_name or current_user.username }}</h4>
                    <p class="text-muted">{{ current_user.username }}</p>
                    <div class="profile-stats">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="stat-number">{{ current_user.created_meetings or 0 }}</div>
                                <div class="stat-label">اجتماعات منشأة</div>
                            </div>
                            <div class="col-4">
                                <div class="stat-number">{{ current_user.last_login_days or 0 }}</div>
                                <div class="stat-label">أيام منذ آخر دخول</div>
                            </div>
                            <div class="col-4">
                                <div class="stat-number">{{ current_user.total_sessions or 1 }}</div>
                                <div class="stat-label">جلسات العمل</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-edit me-2"></i>معلومات الحساب</h5>
                </div>
                <div class="card-body">
                    <form>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="username" value="{{ current_user.username }}" readonly>
                                    <label for="username">اسم المستخدم</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="fullName" value="{{ current_user.full_name or '' }}" placeholder="الاسم الكامل">
                                    <label for="fullName">الاسم الكامل</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="email" class="form-control" id="email" value="{{ current_user.email or '' }}" placeholder="البريد الإلكتروني">
                                    <label for="email">البريد الإلكتروني</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="tel" class="form-control" id="phone" value="{{ current_user.phone or '' }}" placeholder="رقم الهاتف">
                                    <label for="phone">رقم الهاتف</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <select class="form-select" id="department">
                                <option value="">اختر القسم</option>
                                <option value="financial" {{ 'selected' if current_user.department == 'financial' else '' }}>الدائرة المالية</option>
                                <option value="admin" {{ 'selected' if current_user.department == 'admin' else '' }}>الإدارة</option>
                                <option value="hr" {{ 'selected' if current_user.department == 'hr' else '' }}>الموارد البشرية</option>
                                <option value="it" {{ 'selected' if current_user.department == 'it' else '' }}>تقنية المعلومات</option>
                            </select>
                            <label for="department">القسم</label>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <textarea class="form-control" id="bio" style="height: 100px" placeholder="نبذة شخصية">{{ current_user.bio or '' }}</textarea>
                            <label for="bio">نبذة شخصية</label>
                        </div>
                        
                        <div class="text-end">
                            <button type="button" class="btn btn-outline-secondary me-2">إلغاء</button>
                            <button type="submit" class="btn btn-jaf-primary">
                                <i class="fas fa-save me-1"></i>
                                حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Change Password Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-lock me-2"></i>تغيير كلمة المرور</h5>
                </div>
                <div class="card-body">
                    <form>
                        <div class="form-floating mb-3">
                            <input type="password" class="form-control" id="currentPassword" placeholder="كلمة المرور الحالية">
                            <label for="currentPassword">كلمة المرور الحالية</label>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="password" class="form-control" id="newPassword" placeholder="كلمة المرور الجديدة">
                                    <label for="newPassword">كلمة المرور الجديدة</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input type="password" class="form-control" id="confirmPassword" placeholder="تأكيد كلمة المرور">
                                    <label for="confirmPassword">تأكيد كلمة المرور</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-end">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-key me-1"></i>
                                تغيير كلمة المرور
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.profile-avatar {
    margin-bottom: 1rem;
}

.profile-stats .stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--jaf-primary);
}

.profile-stats .stat-label {
    font-size: 0.875rem;
    color: #6c757d;
}

.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 15px;
}

.card-header {
    background: var(--jaf-gradient);
    color: white;
    border-radius: 15px 15px 0 0 !important;
}
</style>
{% endblock %}
