#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إضافة بيانات حقيقية لاختبار التقارير
"""

import sys
import os
sys.path.append('.')

from app import app, db, Meeting, User
from datetime import date, time
from werkzeug.security import generate_password_hash

def add_real_data():
    with app.app_context():
        try:
            # إنشاء الجداول
            db.create_all()
            
            # إنشاء مستخدم admin إذا لم يكن موجوداً
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    full_name='مدير النظام',
                    is_admin=True
                )
                admin_user.set_password('admin')
                db.session.add(admin_user)
                db.session.commit()
                print("✅ تم إنشاء المستخدم الافتراضي")
            
            # حذف الاجتماعات الموجودة
            Meeting.query.delete()
            db.session.commit()
            
            # إضافة اجتماعات 2024
            meetings_2024 = [
                Meeting(
                    subject='اجتماع مجلس الإدارة - يناير 2024',
                    meeting_type='اجتماع إداري',
                    meeting_date=date(2024, 1, 15),
                    meeting_time=time(10, 0),
                    location='قاعة الاجتماعات الرئيسية',
                    inviting_party='مجلس الإدارة',
                    book_number='001/2024/م.د',
                    book_date=date(2024, 1, 10),
                    dress_code='الزي الرسمي',
                    notes='اجتماع دوري شهري',
                    is_cancelled=False,
                    creator_id=admin_user.id
                ),
                Meeting(
                    subject='اجتماع اللجنة المالية - مارس 2024',
                    meeting_type='اجتماع مالي',
                    meeting_date=date(2024, 3, 20),
                    meeting_time=time(14, 0),
                    location='مكتب المدير المالي',
                    inviting_party='اللجنة المالية',
                    book_number='002/2024/م.د',
                    book_date=date(2024, 3, 15),
                    dress_code='الزي الرسمي',
                    notes='مراجعة الميزانية الربعية',
                    is_cancelled=False,
                    creator_id=admin_user.id
                ),
                Meeting(
                    subject='اجتماع طارئ - أبريل 2024',
                    meeting_type='اجتماع طارئ',
                    meeting_date=date(2024, 4, 10),
                    meeting_time=time(9, 0),
                    location='قاعة المؤتمرات',
                    inviting_party='الإدارة العليا',
                    book_number='003/2024/م.د',
                    book_date=date(2024, 4, 8),
                    dress_code='الزي الرسمي',
                    notes='اجتماع طارئ لمناقشة أمور عاجلة',
                    is_cancelled=True,  # ملغي
                    creator_id=admin_user.id
                )
            ]
            
            # إضافة اجتماعات 2025
            meetings_2025 = [
                Meeting(
                    subject='اجتماع التخطيط السنوي - يناير 2025',
                    meeting_type='اجتماع تخطيط',
                    meeting_date=date(2025, 1, 10),
                    meeting_time=time(10, 0),
                    location='قاعة الاجتماعات الرئيسية',
                    inviting_party='إدارة التخطيط',
                    book_number='001/2025/م.د',
                    book_date=date(2025, 1, 5),
                    dress_code='الزي الرسمي',
                    notes='وضع الخطة السنوية للعام الجديد',
                    is_cancelled=False,
                    creator_id=admin_user.id
                ),
                Meeting(
                    subject='اجتماع المراجعة الشهرية - فبراير 2025',
                    meeting_type='اجتماع مراجعة',
                    meeting_date=date(2025, 2, 15),
                    meeting_time=time(14, 0),
                    location='قاعة المؤتمرات',
                    inviting_party='إدارة الجودة',
                    book_number='002/2025/م.د',
                    book_date=date(2025, 2, 10),
                    dress_code='الزي الرسمي',
                    notes='مراجعة الأداء الشهري',
                    is_cancelled=False,
                    creator_id=admin_user.id
                ),
                Meeting(
                    subject='اجتماع اللجنة التقنية - مارس 2025',
                    meeting_type='اجتماع تقني',
                    meeting_date=date(2025, 3, 20),
                    meeting_time=time(11, 0),
                    location='قاعة التقنية',
                    inviting_party='اللجنة التقنية',
                    book_number='003/2025/م.د',
                    book_date=date(2025, 3, 15),
                    dress_code='الزي الرسمي',
                    notes='مناقشة التطوير التقني',
                    is_cancelled=True,  # ملغي
                    creator_id=admin_user.id
                ),
                Meeting(
                    subject='اجتماع الموارد البشرية - أبريل 2025',
                    meeting_type='اجتماع إداري',
                    meeting_date=date(2025, 4, 12),
                    meeting_time=time(13, 0),
                    location='مكتب الموارد البشرية',
                    inviting_party='إدارة الموارد البشرية',
                    book_number='004/2025/م.د',
                    book_date=date(2025, 4, 8),
                    dress_code='الزي الرسمي',
                    notes='مناقشة شؤون الموظفين',
                    is_cancelled=False,
                    creator_id=admin_user.id
                ),
                Meeting(
                    subject='اجتماع المتابعة الربعية - يونيو 2025',
                    meeting_type='اجتماع متابعة',
                    meeting_date=date(2025, 6, 25),
                    meeting_time=time(10, 30),
                    location='قاعة الاجتماعات الرئيسية',
                    inviting_party='الإدارة العليا',
                    book_number='005/2025/م.د',
                    book_date=date(2025, 6, 20),
                    dress_code='الزي الرسمي',
                    notes='متابعة تنفيذ الخطط الربعية',
                    is_cancelled=False,
                    creator_id=admin_user.id
                )
            ]
            
            # إضافة جميع الاجتماعات
            all_meetings = meetings_2024 + meetings_2025
            for meeting in all_meetings:
                db.session.add(meeting)
            
            db.session.commit()
            
            # طباعة الإحصائيات
            total_meetings = Meeting.query.count()
            meetings_2024_count = Meeting.query.filter(Meeting.meeting_date.like('2024%')).count()
            meetings_2025_count = Meeting.query.filter(Meeting.meeting_date.like('2025%')).count()
            cancelled_meetings = Meeting.query.filter_by(is_cancelled=True).count()
            
            print(f"✅ تم إضافة البيانات الحقيقية بنجاح!")
            print(f"📊 إجمالي الاجتماعات: {total_meetings}")
            print(f"📅 اجتماعات 2024: {meetings_2024_count}")
            print(f"📅 اجتماعات 2025: {meetings_2025_count}")
            print(f"❌ اجتماعات ملغاة: {cancelled_meetings}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إضافة البيانات: {str(e)}")
            db.session.rollback()
            return False

if __name__ == '__main__':
    success = add_real_data()
    if success:
        print("\n🎉 تم إعداد البيانات بنجاح! يمكنك الآن اختبار التقارير.")
    else:
        print("\n💥 فشل في إعداد البيانات.")
