#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
اختبار مباشر لإرسال إشعارات WhatsApp
"""

import urllib.parse
import webbrowser
from datetime import datetime
import time

def send_whatsapp_notification(message_type):
    """إرسال إشعار WhatsApp مباشر"""
    
    print(f"🚀 إرسال إشعار {message_type}...")
    
    # الرقم المستهدف
    phone = "0782146467"
    
    # تنظيف الرقم وإضافة رمز الدولة
    clean_phone = phone.replace('+', '').replace('-', '').replace(' ', '')
    if not clean_phone.startswith('962'):
        if clean_phone.startswith('0'):
            clean_phone = '962' + clean_phone[1:]
        else:
            clean_phone = '962' + clean_phone
    
    print(f"📞 الرقم: +{clean_phone}")
    
    # بيانات الاجتماع الوهمي
    meeting_data = {
        'subject': 'اجتماع مجلس الإدارة الشهري',
        'location': 'قاعة الاجتماعات الرئيسية',
        'date': datetime.now().strftime('%Y/%m/%d'),
        'time': datetime.now().strftime('%H:%M'),
        'organizer': 'مديرية الدائرة المالية'
    }
    
    # صيغ الرسائل
    messages = {
        'new': f"""🆕 *اجتماع جديد*

📋 *الموضوع:* {meeting_data['subject']}
📍 *المكان:* {meeting_data['location']}
📅 *التاريخ:* {meeting_data['date']}
⏰ *الوقت:* {meeting_data['time']}
👤 *المنظم:* {meeting_data['organizer']}

---
نظام إدارة الاجتماعات
القوات المسلحة الأردنية""",

        'postponed': f"""⏰ *تم تأجيل الاجتماع*

📋 *الموضوع:* {meeting_data['subject']}
📍 *المكان:* {meeting_data['location']}
📅 *التاريخ الجديد:* {meeting_data['date']}
⏰ *الوقت الجديد:* {meeting_data['time']}

🔄 *تم تأجيل هذا الاجتماع إلى موعد جديد*

---
نظام إدارة الاجتماعات
القوات المسلحة الأردنية""",

        'cancelled': f"""❌ *تم إلغاء الاجتماع*

📋 *الموضوع:* {meeting_data['subject']}
📍 *المكان:* {meeting_data['location']}
📅 *التاريخ:* {meeting_data['date']}
⏰ *الوقت:* {meeting_data['time']}

⚠️ *تم إلغاء هذا الاجتماع*

---
نظام إدارة الاجتماعات
القوات المسلحة الأردنية""",

        'updated': f"""✏️ *تم تعديل الاجتماع*

📋 *الموضوع:* {meeting_data['subject']}
📍 *المكان:* {meeting_data['location']}
📅 *التاريخ:* {meeting_data['date']}
⏰ *الوقت:* {meeting_data['time']}
👤 *المنظم:* {meeting_data['organizer']}

🔄 *تم تحديث تفاصيل هذا الاجتماع*

---
نظام إدارة الاجتماعات
القوات المسلحة الأردنية"""
    }
    
    message = messages.get(message_type, messages['new'])
    
    print(f"💬 نوع الرسالة: {message_type}")
    print(f"📝 محتوى الرسالة: {message[:100]}...")
    
    try:
        # إنشاء رابط WhatsApp
        encoded_message = urllib.parse.quote(message)
        whatsapp_url = f"https://wa.me/{clean_phone}?text={encoded_message}"
        
        print(f"🌐 رابط WhatsApp: {whatsapp_url[:100]}...")
        
        # فتح الرابط في المتصفح
        webbrowser.open(whatsapp_url)
        print("✅ تم فتح WhatsApp Web في المتصفح")
        print("📱 اضغط 'إرسال' في WhatsApp لإكمال الإرسال")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الإرسال: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚀 نظام اختبار إشعارات WhatsApp المباشر")
    print("=" * 60)
    print("📱 الرقم المستهدف: 0782146467")
    print("=" * 60)
    
    while True:
        print("\nاختر نوع الإشعار:")
        print("1. 🆕 اجتماع جديد")
        print("2. ⏰ تأجيل اجتماع")
        print("3. ❌ إلغاء اجتماع")
        print("4. ✏️ تعديل اجتماع")
        print("5. 🚪 خروج")
        
        choice = input("\nأدخل اختيارك (1-5): ").strip()
        
        if choice == '1':
            send_whatsapp_notification('new')
        elif choice == '2':
            send_whatsapp_notification('postponed')
        elif choice == '3':
            send_whatsapp_notification('cancelled')
        elif choice == '4':
            send_whatsapp_notification('updated')
        elif choice == '5':
            print("👋 وداعاً!")
            break
        else:
            print("❌ اختيار غير صحيح!")
        
        print("\n" + "=" * 60)

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في البرنامج: {str(e)}")
        input("اضغط Enter للخروج...")
