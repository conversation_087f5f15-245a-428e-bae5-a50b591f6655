<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الطباعة النهائي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', 'Tahoma', 'Arial', sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .success-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        .test-section {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-button {
            margin: 10px;
            padding: 12px 20px;
            border-radius: 8px;
            border: none;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .btn-all { background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); }
        .btn-active { background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%); }
        .btn-postponed { background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%); color: #000; }
        .btn-cancelled { background: linear-gradient(135deg, #6c757d 0%, #545b62 100%); }
        .btn-finished { background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); }
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-item {
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            color: white;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">🎯 اختبار الطباعة النهائي</h1>
        
        <div class="success-badge">
            <h5>✅ جميع الإصلاحات مطبقة بنجاح!</h5>
            <p class="mb-0">البيانات الحقيقية + التاريخ YYYY/MM/DD + الطباعة الفردية</p>
        </div>

        <div class="test-section">
            <h4>📊 اختبار طباعة البيانات الحقيقية</h4>
            <p>هذه الأزرار تجلب البيانات الحقيقية من قاعدة البيانات:</p>
            
            <div class="status-grid">
                <div class="status-item btn-all">
                    <h6>جميع الاجتماعات</h6>
                    <button class="test-button btn-all" onclick="testRealData('all')">
                        <i class="fas fa-print me-1"></i>
                        طباعة الكل
                    </button>
                </div>
                
                <div class="status-item btn-active">
                    <h6>الاجتماعات النشطة</h6>
                    <button class="test-button btn-active" onclick="testRealData('active')">
                        <i class="fas fa-print me-1"></i>
                        طباعة الكل
                    </button>
                </div>
                
                <div class="status-item btn-postponed">
                    <h6>الاجتماعات المؤجلة</h6>
                    <button class="test-button btn-postponed" onclick="testRealData('postponed')">
                        <i class="fas fa-print me-1"></i>
                        طباعة الكل
                    </button>
                </div>
                
                <div class="status-item btn-cancelled">
                    <h6>الاجتماعات الملغية</h6>
                    <button class="test-button btn-cancelled" onclick="testRealData('cancelled')">
                        <i class="fas fa-print me-1"></i>
                        طباعة الكل
                    </button>
                </div>
                
                <div class="status-item btn-finished">
                    <h6>الاجتماعات المنتهية</h6>
                    <button class="test-button btn-finished" onclick="testRealData('finished')">
                        <i class="fas fa-print me-1"></i>
                        طباعة الكل
                    </button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h4>📅 اختبار صيغة التاريخ الجديدة</h4>
            <p>التاريخ الآن يظهر بصيغة: <strong id="currentDate"></strong></p>
            <p>بدلاً من الصيغة الهجرية القديمة</p>
        </div>

        <div class="test-section">
            <h4>🖨️ اختبار الطباعة الفردية</h4>
            <p>اختبار طباعة اجتماع واحد:</p>
            <button class="test-button btn-active" onclick="testSingleMeeting()">
                <i class="fas fa-print me-1"></i>
                طباعة اجتماع تجريبي
            </button>
        </div>

        <div class="console-output" id="consoleOutput">
            <div>🖥️ Console Output:</div>
            <div>انتظار اختبار الطباعة...</div>
        </div>

        <div class="alert alert-info">
            <h5>📋 ملاحظات الاختبار:</h5>
            <ul>
                <li><strong>البيانات الحقيقية:</strong> الأزرار أعلاه تجلب البيانات من قاعدة البيانات الفعلية</li>
                <li><strong>التاريخ YYYY/MM/DD:</strong> يظهر بصيغة 2025/07/28 بدلاً من الهجري</li>
                <li><strong>الطباعة الفردية:</strong> تعمل الآن بشكل صحيح</li>
                <li><strong>معالجة الأخطاء:</strong> في حالة عدم وجود بيانات، تظهر رسالة واضحة</li>
            </ul>
        </div>
    </div>

    <script>
        // تسجيل رسائل Console في الصفحة
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('consoleOutput');
        
        function addToConsole(message, type = 'log') {
            const div = document.createElement('div');
            div.style.color = type === 'error' ? '#ff6b6b' : '#00ff00';
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };

        // عرض التاريخ الحالي بصيغة YYYY/MM/DD
        document.getElementById('currentDate').textContent = new Date().toISOString().split('T')[0].replace(/-/g, '/');

        // اختبار البيانات الحقيقية
        function testRealData(filterType) {
            console.log('🔍 اختبار البيانات الحقيقية لنوع:', filterType);
            
            // محاكاة استدعاء API
            fetch('/api/meetings/print-data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    filter_type: filterType
                })
            })
            .then(response => {
                console.log('📡 استجابة الخادم:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log(`✅ تم جلب ${data.meetings.length} اجتماع من قاعدة البيانات`);
                
                if (data.meetings.length === 0) {
                    console.log('⚠️ لا توجد اجتماعات من هذا النوع');
                    alert(`لا توجد اجتماعات من نوع "${filterType}" في قاعدة البيانات`);
                    return;
                }
                
                // طباعة التقرير
                printRealDataReport(data.meetings, data.report_title);
            })
            .catch(error => {
                console.error('❌ خطأ في جلب البيانات:', error);
                alert('خطأ في الاتصال بقاعدة البيانات. تحقق من تسجيل الدخول.');
            });
        }

        // اختبار الطباعة الفردية
        function testSingleMeeting() {
            console.log('🖨️ اختبار الطباعة الفردية');
            
            const sampleMeeting = {
                subject: 'اجتماع تجريبي للاختبار',
                status: 'نشط',
                meeting_date: '2025-08-15',
                meeting_time: '10:30',
                location: 'قاعة الاختبار',
                inviting_party: 'إدارة الاختبار'
            };
            
            printRealDataReport([sampleMeeting], 'تقرير اجتماع تجريبي');
        }

        // دالة طباعة البيانات الحقيقية مع التاريخ YYYY/MM/DD
        function printRealDataReport(meetings, reportTitle) {
            console.log('🖨️ إنشاء تقرير بالبيانات الحقيقية:', reportTitle);

            if (!meetings || meetings.length === 0) {
                console.error('❌ لا توجد اجتماعات للطباعة');
                return;
            }

            // إنشاء HTML للطباعة مع التاريخ بصيغة YYYY/MM/DD
            let printHTML = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>${reportTitle}</title>
                    <style>
                        body { font-family: 'Cairo', 'Tahoma', 'Arial', sans-serif; direction: rtl; padding: 20px; line-height: 1.6; margin: 0; }
                        .header { text-align: center; margin-bottom: 30px; padding: 25px; border-bottom: 3px solid #1B4332; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 10px; }
                        .header h1 { color: #1B4332; margin-bottom: 15px; font-size: 28px; font-weight: bold; }
                        .header-info { display: flex; justify-content: center; gap: 30px; flex-wrap: wrap; margin-top: 15px; }
                        .header-item { background: white; padding: 10px 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                        .meetings-table { width: 100%; border-collapse: collapse; margin: 20px 0; background: white; box-shadow: 0 4px 8px rgba(0,0,0,0.1); border-radius: 10px; overflow: hidden; }
                        .meetings-table th { background: linear-gradient(135deg, #1B4332 0%, #2d5a3d 100%); color: white; padding: 15px 12px; text-align: center; font-weight: bold; font-size: 14px; border-bottom: 2px solid #0f2419; }
                        .meetings-table td { padding: 12px; text-align: center; border-bottom: 1px solid #dee2e6; vertical-align: middle; }
                        .meetings-table tr:nth-child(even) { background-color: #f8f9fa; }
                        .meetings-table tr:hover { background-color: #e9ecef; }
                        .meeting-subject { font-weight: bold; color: #1B4332; text-align: right; max-width: 300px; }
                        .row-number { background: #e9ecef; font-weight: bold; color: #495057; width: 50px; }
                        @media print { body { margin: 0; padding: 10px; } .meetings-table { page-break-inside: auto; } .meetings-table tr { page-break-inside: avoid; } .header { page-break-after: avoid; } }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>${reportTitle}</h1>
                        <div class="header-info">
                            <div class="header-item">
                                <strong>عدد الاجتماعات:</strong> ${meetings.length}
                            </div>
                            <div class="header-item">
                                <strong>تاريخ التقرير:</strong> ${new Date().toISOString().split('T')[0].replace(/-/g, '/')}
                            </div>
                        </div>
                    </div>
                    <table class="meetings-table">
                        <thead>
                            <tr>
                                <th style="width: 50px;">#</th>
                                <th style="width: 300px;">موضوع الاجتماع</th>
                                <th style="width: 120px;">التاريخ</th>
                                <th style="width: 100px;">الوقت</th>
                                <th style="width: 200px;">المكان</th>
                                <th style="width: 200px;">جهة الدعوة</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            meetings.forEach((meeting, index) => {
                printHTML += `
                    <tr>
                        <td class="row-number">${index + 1}</td>
                        <td class="meeting-subject">${meeting.subject}</td>
                        <td>${meeting.meeting_date}</td>
                        <td>${meeting.meeting_time}</td>
                        <td>${meeting.location}</td>
                        <td>${meeting.inviting_party}</td>
                    </tr>
                `;
            });

            printHTML += `
                        </tbody>
                    </table>
                </body>
                </html>
            `;

            // فتح نافذة طباعة جديدة
            const printWindow = window.open('', '_blank', 'width=1000,height=700');
            if (!printWindow) {
                console.error('❌ تعذر فتح نافذة الطباعة');
                alert('تعذر فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.');
                return;
            }

            console.log('✅ تم فتح نافذة الطباعة بنجاح');
            printWindow.document.write(printHTML);
            printWindow.document.close();

            // انتظار تحميل المحتوى ثم الطباعة
            printWindow.onload = function() {
                setTimeout(() => {
                    printWindow.focus();
                    printWindow.print();
                    console.log('✅ تم تشغيل الطباعة بنجاح');
                }, 500);
            };

            console.log(`✅ تم إنشاء تقرير بالبيانات الحقيقية لـ ${meetings.length} اجتماع`);
        }

        // رسالة ترحيب
        console.log('🚀 تم تحميل صفحة الاختبار النهائي');
        console.log('📅 التاريخ الحالي (YYYY/MM/DD):', new Date().toISOString().split('T')[0].replace(/-/g, '/'));
    </script>
</body>
</html>
