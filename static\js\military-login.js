// Enhanced Military Login Effects - Jordanian Armed Forces System
// نظام إدارة الاجتماعات - مديرية الدائرة المالية

class MilitaryLoginEffects {
    constructor() {
        this.matrixContainer = null;
        this.isInitialized = false;
        this.securityLevel = 'HIGH';
        this.init();
    }

    // تهيئة النظام
    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.initializeEffects();
            this.setupSecurityFeatures();
            this.setupFormHandlers();
            this.setupKeyboardShortcuts();
            this.displaySecurityWarnings();
            this.isInitialized = true;
            console.log('🛡️ نظام الحماية العسكري مفعل');
        });
    }

    // تهيئة التأثيرات البصرية
    initializeEffects() {
        this.createEnhancedMatrixRain();
        this.addMilitaryGlowEffects();
        this.setupInputAnimations();
        this.createRadarSweep();
        this.setupConnectionStatus();
    }

    // إنشاء تأثير المطر الرقمي المحسن
    createEnhancedMatrixRain() {
        this.matrixContainer = document.getElementById('matrixRain');
        if (!this.matrixContainer) return;

        const militaryChars = '01010110100101001011010010110100101101001011010010110100101101001011';
        const arabicChars = 'أبتثجحخدذرزسشصضطظعغفقكلمنهوي';
        const symbols = '⚡⚔️🛡️⭐🎖️';
        const characters = militaryChars + arabicChars + symbols;

        // مسح الأعمدة الموجودة
        this.matrixContainer.innerHTML = '';

        // إنشاء 80 عمود للتأثير المحسن
        for (let i = 0; i < 80; i++) {
            const column = document.createElement('div');
            column.className = 'matrix-column';
            column.style.left = Math.random() * 100 + '%';
            column.style.animationDuration = (Math.random() * 5 + 4) + 's';
            column.style.animationDelay = Math.random() * 4 + 's';
            column.style.fontSize = (Math.random() * 8 + 10) + 'px';
            column.style.opacity = Math.random() * 0.7 + 0.3;

            let text = '';
            const columnHeight = Math.floor(Math.random() * 20 + 15);
            for (let j = 0; j < columnHeight; j++) {
                const char = characters.charAt(Math.floor(Math.random() * characters.length));
                text += `<span style="color: ${this.getRandomMilitaryColor()}">${char}</span><br>`;
            }
            column.innerHTML = text;

            this.matrixContainer.appendChild(column);
        }

        // تحديث دوري للمطر الرقمي
        setInterval(() => {
            this.updateMatrixRain();
        }, 10000);
    }

    // الحصول على لون عسكري عشوائي
    getRandomMilitaryColor() {
        const colors = [
            'var(--military-accent)',
            'var(--military-gold)',
            'var(--military-success)',
            '#52B788',
            '#74C69D'
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    // تحديث المطر الرقمي
    updateMatrixRain() {
        const columns = this.matrixContainer.querySelectorAll('.matrix-column');
        columns.forEach(column => {
            if (Math.random() < 0.3) {
                column.style.animationDuration = (Math.random() * 5 + 4) + 's';
                column.style.opacity = Math.random() * 0.7 + 0.3;
            }
        });
    }

    // إضافة تأثيرات الإضاءة العسكرية
    addMilitaryGlowEffects() {
        const loginBox = document.querySelector('.login-box');
        if (!loginBox) return;

        // تغيير ألوان الحدود بشكل دوري
        setInterval(() => {
            const militaryColors = [
                'var(--military-gold)', 
                'var(--military-accent)', 
                'var(--military-primary)',
                'var(--military-success)'
            ];
            const randomColor = militaryColors[Math.floor(Math.random() * militaryColors.length)];
            loginBox.style.boxShadow = `var(--military-glow) ${randomColor}, inset 0 0 30px ${randomColor}33`;
        }, 7000);

        // تأثير النبض للشعار
        const logo = document.querySelector('.military-logo');
        if (logo) {
            setInterval(() => {
                logo.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    logo.style.transform = 'scale(1)';
                }, 200);
            }, 5000);
        }
    }

    // إعداد تحريك حقول الإدخال
    setupInputAnimations() {
        const inputs = document.querySelectorAll('.form-input');
        inputs.forEach(input => {
            // تأثير التركيز
            input.addEventListener('focus', (e) => {
                e.target.style.transform = 'translateY(-3px) scale(1.02)';
                e.target.style.boxShadow = 'var(--military-glow) var(--military-gold)';
                
                const icon = e.target.parentElement.querySelector('.input-icon');
                if (icon) {
                    icon.style.color = 'var(--military-gold)';
                    icon.style.transform = 'translateY(-50%) scale(1.2)';
                }
            });
            
            // تأثير فقدان التركيز
            input.addEventListener('blur', (e) => {
                e.target.style.transform = 'translateY(0) scale(1)';
                
                const icon = e.target.parentElement.querySelector('.input-icon');
                if (icon) {
                    icon.style.transform = 'translateY(-50%) scale(1)';
                }
            });
            
            // تأثير الكتابة
            input.addEventListener('input', (e) => {
                const icon = e.target.parentElement.querySelector('.input-icon');
                if (icon) {
                    icon.style.color = e.target.value ? 'var(--military-gold)' : 'var(--military-text-dim)';
                }
                
                // تأثير بصري للكتابة
                e.target.style.borderColor = 'var(--military-gold)';
                setTimeout(() => {
                    e.target.style.borderColor = 'var(--military-border)';
                }, 150);
            });
        });
    }

    // إنشاء تأثير الرادار
    createRadarSweep() {
        const radarSweep = document.querySelector('.radar-sweep');
        if (!radarSweep) return;

        setInterval(() => {
            const colors = ['var(--military-gold)', 'var(--military-accent)', 'var(--military-success)'];
            const randomColor = colors[Math.floor(Math.random() * colors.length)];
            radarSweep.style.borderColor = randomColor;
        }, 2500);
    }

    // إعداد حالة الاتصال
    setupConnectionStatus() {
        const connectionStatus = document.querySelector('.connection-status');
        if (!connectionStatus) return;

        // محاكاة حالة الاتصال
        setInterval(() => {
            const statuses = [
                { text: 'متصل بشكل آمن', color: 'var(--military-success)' },
                { text: 'فحص الأمان...', color: 'var(--military-warning)' },
                { text: 'تشفير البيانات...', color: 'var(--military-accent)' }
            ];
            
            const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
            const statusText = connectionStatus.querySelector('span');
            const statusDot = connectionStatus.querySelector('.connection-dot');
            
            if (statusText && statusDot) {
                statusText.textContent = randomStatus.text;
                statusDot.style.background = randomStatus.color;
            }
        }, 8000);
    }

    // إعداد معالجات النموذج
    setupFormHandlers() {
        const loginForm = document.getElementById('loginForm');
        const loginBtn = document.querySelector('.login-btn');
        
        if (!loginForm || !loginBtn) return;

        loginForm.addEventListener('submit', (e) => {
            this.handleFormSubmission(e, loginBtn);
        });
    }

    // معالجة إرسال النموذج
    handleFormSubmission(e, loginBtn) {
        const loadingSpinner = document.getElementById('loadingSpinner');
        const originalText = loginBtn.innerHTML;

        // إظهار حالة التحميل
        if (loadingSpinner) {
            loadingSpinner.style.display = 'block';
        }
        
        loginBtn.disabled = true;
        loginBtn.style.background = 'var(--military-border)';
        
        // نصوص التحميل المتحركة
        const loadingTexts = [
            '<i class="fas fa-shield-alt fa-spin me-2"></i>جاري التحقق من الهوية العسكرية...',
            '<i class="fas fa-lock fa-spin me-2"></i>فحص كلمة المرور المشفرة...',
            '<i class="fas fa-key fa-spin me-2"></i>تشفير البيانات الحساسة...',
            '<i class="fas fa-check-circle fa-spin me-2"></i>تأكيد الصلاحيات الأمنية...',
            '<i class="fas fa-user-shield fa-spin me-2"></i>تفعيل البروتوكولات الأمنية...'
        ];
        
        let textIndex = 0;
        const textInterval = setInterval(() => {
            loginBtn.innerHTML = loadingTexts[textIndex];
            textIndex = (textIndex + 1) % loadingTexts.length;
        }, 900);

        // محاكاة وقت المعالجة
        setTimeout(() => {
            clearInterval(textInterval);
            // سيتم إرسال النموذج بشكل طبيعي
        }, 3000);
    }

    // إعداد اختصارات لوحة المفاتيح
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl + Enter للإرسال
            if (e.ctrlKey && e.key === 'Enter') {
                const loginForm = document.getElementById('loginForm');
                if (loginForm) {
                    loginForm.submit();
                }
            }
            
            // ESC لمسح النموذج
            if (e.key === 'Escape') {
                const loginForm = document.getElementById('loginForm');
                if (loginForm) {
                    loginForm.reset();
                    const usernameInput = document.querySelector('input[name="username"]');
                    if (usernameInput) {
                        usernameInput.focus();
                    }
                }
            }

            // منع اختصارات أدوات المطور
            if (e.key === 'F12' || 
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.shiftKey && e.key === 'C') ||
                (e.ctrlKey && e.key === 'U')) {
                e.preventDefault();
                this.logSecurityAttempt('محاولة وصول غير مصرح بها للأدوات');
            }
        });
    }

    // إعداد ميزات الأمان
    setupSecurityFeatures() {
        // منع النقر بالزر الأيمن
        document.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.logSecurityAttempt('محاولة وصول غير مصرح بها');
        });

        // منع السحب والإفلات
        document.addEventListener('dragstart', (e) => {
            e.preventDefault();
        });

        // منع التحديد
        document.addEventListener('selectstart', (e) => {
            if (e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                e.preventDefault();
            }
        });
    }

    // تسجيل محاولات الأمان
    logSecurityAttempt(message) {
        console.log(`%c🚨 ${message}`, 'color: #E63946; font-size: 18px; font-weight: bold;');
        
        // يمكن إضافة إرسال تقرير للخادم هنا
        // fetch('/api/security-log', { method: 'POST', body: JSON.stringify({message}) });
    }

    // عرض تحذيرات الأمان
    displaySecurityWarnings() {
        console.log('%c🛡️ نظام الحماية العسكري مفعل', 'color: #FFD700; font-size: 24px; font-weight: bold; text-shadow: 2px 2px 4px #000;');
        console.log('%c🇯🇴 القوات المسلحة الأردنية - مديرية الدائرة المالية', 'color: #40916C; font-size: 16px; font-weight: bold;');
        console.log('%c⚠️ نظام محمي بتشفير عسكري متقدم AES-256', 'color: #FFB700; font-size: 14px;');
        console.log('%c🚨 أي محاولة للتلاعب أو الاختراق ستؤدي إلى عواقب قانونية خطيرة', 'color: #E63946; font-size: 14px; font-weight: bold;');
    }
}

// تهيئة النظام
const militaryLogin = new MilitaryLoginEffects();
