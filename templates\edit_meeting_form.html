{% extends "base.html" %}

{% block title %}تعديل الاجتماع - نظام إدارة الاجتماعات{% endblock %}

{% block extra_css %}
<style>
    .edit-form-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin: 2rem 0;
    }
    
    .form-header {
        background: var(--jaf-gradient);
        color: white;
        padding: 1.5rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: var(--jaf-primary);
        margin-bottom: 0.5rem;
        display: block;
    }
    
    .form-control {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 0.75rem;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        border-color: var(--jaf-primary);
        box-shadow: 0 0 0 3px rgba(27, 67, 50, 0.1);
    }
    
    .btn-save {
        background: var(--jaf-gradient);
        border: none;
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(27, 67, 50, 0.3);
    }
    
    .btn-cancel {
        background: #6c757d;
        border: none;
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 600;
        margin-left: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="edit-form-container">
        <div class="form-header">
            <h2><i class="fas fa-edit me-2"></i>تعديل الاجتماع</h2>
            <p class="mb-0">تحديث بيانات الاجتماع</p>
        </div>
        
        <form method="POST">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">موضوع الاجتماع</label>
                        <input type="text" name="subject" class="form-control" 
                               value="{{ meeting.subject }}" required>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">نوع الاجتماع</label>
                        <select name="meeting_type" class="form-control" required>
                            <option value="اجتماع إداري" {% if meeting.meeting_type == 'اجتماع إداري' %}selected{% endif %}>اجتماع إداري</option>
                            <option value="اجتماع مالي" {% if meeting.meeting_type == 'اجتماع مالي' %}selected{% endif %}>اجتماع مالي</option>
                            <option value="اجتماع تقني" {% if meeting.meeting_type == 'اجتماع تقني' %}selected{% endif %}>اجتماع تقني</option>
                            <option value="اجتماع طارئ" {% if meeting.meeting_type == 'اجتماع طارئ' %}selected{% endif %}>اجتماع طارئ</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">تاريخ الاجتماع</label>
                        <input type="date" name="meeting_date" class="form-control" 
                               value="{{ meeting.meeting_date.strftime('%Y-%m-%d') if meeting.meeting_date }}" required>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">وقت الاجتماع</label>
                        <input type="time" name="meeting_time" class="form-control" 
                               value="{{ meeting.meeting_time.strftime('%H:%M') if meeting.meeting_time }}" required dir="ltr" style="font-variant-numeric: lining-nums; -webkit-font-variant-numeric: lining-nums; font-feature-settings: 'lnum';">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">مكان الاجتماع</label>
                        <input type="text" name="location" class="form-control" 
                               value="{{ meeting.location }}" required>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label">جهة الدعوة</label>
                        <input type="text" name="inviting_party" class="form-control" 
                               value="{{ meeting.inviting_party }}" required>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="form-label">رقم الكتاب</label>
                        <input type="text" name="book_number" class="form-control" 
                               value="{{ meeting.book_number }}" required>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="form-label">تاريخ الكتاب</label>
                        <input type="date" name="book_date" class="form-control" 
                               value="{{ meeting.book_date.strftime('%Y-%m-%d') if meeting.book_date }}" required>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="form-label">اللباس المطلوب</label>
                        <select name="dress_code" class="form-control" required>
                            <option value="الزي الرسمي" {% if meeting.dress_code == 'الزي الرسمي' %}selected{% endif %}>الزي الرسمي</option>
                            <option value="الزي العسكري" {% if meeting.dress_code == 'الزي العسكري' %}selected{% endif %}>الزي العسكري</option>
                            <option value="الزي المدني" {% if meeting.dress_code == 'الزي المدني' %}selected{% endif %}>الزي المدني</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">ملاحظات إضافية</label>
                <textarea name="notes" class="form-control" rows="3">{{ meeting.notes }}</textarea>
            </div>
            
            <div class="text-center mt-4">
                <button type="submit" class="btn btn-save">
                    <i class="fas fa-save me-2"></i>حفظ التغييرات
                </button>
                <a href="{{ url_for('edit_meetings') }}" class="btn btn-cancel">
                    <i class="fas fa-times me-2"></i>إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
