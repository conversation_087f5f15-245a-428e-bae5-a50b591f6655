#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
تطبيق التقارير الذي يعمل بشكل مضمون
"""

from flask import Flask, render_template, jsonify
import sqlite3
import os
from datetime import datetime

app = Flask(__name__)

def ensure_database():
    """التأكد من وجود قاعدة البيانات والبيانات"""
    db_path = 'instance/jaf_meetings.db'
    os.makedirs('instance', exist_ok=True)

    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # إنشاء الجدول
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS meeting (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        subject VARCHAR(200) NOT NULL,
        meeting_type VARCHAR(100) NOT NULL,
        meeting_date DATE NOT NULL,
        meeting_time TIME NOT NULL,
        location VARCHAR(200) NOT NULL,
        inviting_party VARCHAR(200) NOT NULL,
        arrival_time_before INTEGER DEFAULT 15,
        book_number VARCHAR(100) NOT NULL,
        book_date DATE NOT NULL,
        dress_code VARCHAR(100) NOT NULL,
        notes TEXT,
        is_cancelled BOOLEAN DEFAULT 0,
        is_postponed BOOLEAN DEFAULT 0,
        status VARCHAR(50) DEFAULT 'scheduled',
        creator_id INTEGER NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # التحقق من وجود البيانات
    cursor.execute("SELECT COUNT(*) FROM meeting")
    count = cursor.fetchone()[0]
    
    if count == 0:
        print("🔄 إضافة البيانات التجريبية...")
        
        # حذف البيانات الموجودة
        cursor.execute('DELETE FROM meeting')

        # إضافة اجتماع 2024
        cursor.execute('''
        INSERT INTO meeting (subject, meeting_type, meeting_date, meeting_time, location, inviting_party, book_number, book_date, dress_code, notes, is_cancelled, is_postponed, creator_id)
        VALUES ('اجتماع مجلس الإدارة - مارس 2024', 'اجتماع إداري', '2024-03-15', '10:30', 'قاعة الاجتماعات الرئيسية', 'مجلس الإدارة', '001/2024/م.د', '2024-03-10', 'الزي الرسمي', 'اجتماع دوري لمناقشة الأمور الإدارية', 0, 0, 1)
        ''')

        # إضافة اجتماعات 2025
        meetings_2025 = [
            ('اجتماع يناير 2025', '2025-01-10', 0),
            ('اجتماع فبراير 2025', '2025-02-15', 0),
            ('اجتماع مارس 2025', '2025-03-20', 1),  # ملغي
            ('اجتماع أبريل 2025', '2025-04-12', 0),
            ('اجتماع مايو 2025', '2025-05-18', 0),
            ('اجتماع يونيو 2025', '2025-06-25', 0),
            ('اجتماع يوليو 2025', '2025-07-08', 1),   # ملغي
            ('اجتماع أغسطس 2025', '2025-08-14', 0),
            ('اجتماع سبتمبر 2025', '2025-09-22', 0),
            ('اجتماع أكتوبر 2025', '2025-10-30', 0)
        ]

        for i, (title, meeting_date, is_cancelled) in enumerate(meetings_2025, 1):
            cursor.execute('''
            INSERT INTO meeting (subject, meeting_type, meeting_date, meeting_time, location, inviting_party, book_number, book_date, dress_code, notes, is_cancelled, is_postponed, creator_id)
            VALUES (?, 'اجتماع إداري', ?, '14:00', 'قاعة المؤتمرات', 'الإدارة العامة', ?, ?, 'الزي الرسمي', ?, ?, 0, 1)
            ''', (title, meeting_date, f'{i:03d}/2025/م.د', meeting_date, f'اجتماع شهري لسنة 2025', is_cancelled))

        conn.commit()
        print("✅ تم إضافة البيانات التجريبية!")
    else:
        print(f"📊 توجد {count} اجتماع في قاعدة البيانات")
    
    conn.close()

@app.route('/reports')
def reports():
    return render_template('reports.html')

@app.route('/api/reports/monthly-data/<int:year>')
def get_monthly_data(year):
    print(f"🔍 API: طلب بيانات سنة {year}")
    
    try:
        db_path = 'instance/jaf_meetings.db'
        
        if not os.path.exists(db_path):
            print("❌ قاعدة البيانات غير موجودة")
            return jsonify({'success': False, 'error': 'قاعدة البيانات غير موجودة'})
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # جلب اجتماعات السنة
        cursor.execute("SELECT * FROM meeting WHERE meeting_date LIKE ?", (f'{year}%',))
        year_meetings = cursor.fetchall()
        print(f"📅 اجتماعات سنة {year}: {len(year_meetings)}")
        
        if len(year_meetings) > 0:
            print("📋 تفاصيل الاجتماعات:")
            for meeting in year_meetings:
                print(f"  - {meeting[1]} ({meeting[3]}) - ملغي: {meeting[12]}")
        
        # أسماء الأشهر العربية
        arabic_months = {
            1: 'كانون الثاني', 2: 'شباط', 3: 'آذار', 4: 'نيسان',
            5: 'أيار', 6: 'حزيران', 7: 'تموز', 8: 'آب',
            9: 'أيلول', 10: 'تشرين الأول', 11: 'تشرين الثاني', 12: 'كانون الأول'
        }

        monthly_stats = []
        current_date = datetime.now()
        
        year_completed = 0
        year_upcoming = 0
        year_cancelled = 0

        for month_num in range(1, 13):
            # جلب اجتماعات الشهر
            cursor.execute("""
                SELECT * FROM meeting 
                WHERE meeting_date LIKE ?
            """, (f'{year}-{month_num:02d}%',))
            
            month_meetings = cursor.fetchall()
            
            completed = 0
            upcoming = 0
            cancelled = 0

            for meeting in month_meetings:
                is_cancelled = meeting[12]  # is_cancelled column
                
                if is_cancelled:
                    cancelled += 1
                else:
                    # تحويل التاريخ للمقارنة
                    meeting_date_str = meeting[3]  # meeting_date column
                    meeting_date = datetime.strptime(meeting_date_str, '%Y-%m-%d').date()
                    if meeting_date < current_date.date():
                        completed += 1
                    else:
                        upcoming += 1

            # تحديد ما إذا كان الشهر قد انتهى
            month_ended = (year < current_date.year) or (year == current_date.year and month_num <= current_date.month)

            monthly_stats.append({
                'month': arabic_months[month_num],
                'month_number': month_num,
                'completed': completed,
                'upcoming': upcoming,
                'cancelled': cancelled,
                'total': completed + upcoming + cancelled,
                'ended': month_ended
            })
            
            # إضافة للإجماليات
            year_completed += completed
            year_upcoming += upcoming
            year_cancelled += cancelled

            # طباعة تفاصيل الشهر إذا كان يحتوي على بيانات
            if completed + upcoming + cancelled > 0:
                print(f"📅 {arabic_months[month_num]} {year}: مكتملة={completed}, قادمة={upcoming}, ملغاة={cancelled}")
        
        conn.close()
        
        year_total = year_completed + year_upcoming + year_cancelled
        print(f"📊 إحصائيات سنة {year}: مكتملة={year_completed}, قادمة={year_upcoming}, ملغاة={year_cancelled}, المجموع={year_total}")

        result = {
            'success': True,
            'year': year,
            'months': monthly_stats,
            'year_totals': {
                'completed': year_completed,
                'upcoming': year_upcoming,
                'cancelled': year_cancelled,
                'total': year_total
            }
        }
        
        print(f"✅ إرجاع النتيجة: {len(monthly_stats)} شهر")
        return jsonify(result)

    except Exception as e:
        print(f"❌ خطأ في API: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/reports/stats')
def get_reports_stats():
    print("🔍 API: طلب الإحصائيات العامة")
    
    try:
        db_path = 'instance/jaf_meetings.db'
        
        if not os.path.exists(db_path):
            return jsonify({'success': False, 'error': 'قاعدة البيانات غير موجودة'})
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # إحصائيات عامة
        cursor.execute("SELECT COUNT(*) FROM meeting")
        total_meetings = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM meeting WHERE is_cancelled = 0")
        active_meetings = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM meeting WHERE is_cancelled = 1")
        cancelled_meetings = cursor.fetchone()[0]
        
        # حساب المكتملة والقادمة
        current_date = datetime.now().date()
        cursor.execute("SELECT COUNT(*) FROM meeting WHERE is_cancelled = 0 AND meeting_date < ?", (current_date,))
        completed_meetings = cursor.fetchone()[0]
        
        upcoming_meetings = active_meetings - completed_meetings
        
        conn.close()
        
        result = {
            'success': True,
            'total_meetings': total_meetings,
            'completed_meetings': completed_meetings,
            'upcoming_meetings': upcoming_meetings,
            'cancelled_meetings': cancelled_meetings,
            'avg_per_month': total_meetings // 12 if total_meetings > 0 else 0,
            'most_active_month': 'مارس'
        }
        
        print(f"📊 الإحصائيات العامة: المجموع={total_meetings}, مكتملة={completed_meetings}, قادمة={upcoming_meetings}, ملغاة={cancelled_meetings}")
        return jsonify(result)
        
    except Exception as e:
        print(f"❌ خطأ في الإحصائيات: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    print("🚀 تشغيل تطبيق التقارير المضمون...")
    ensure_database()
    print("📍 الرابط: http://127.0.0.1:5000")
    print("🔗 التقارير: http://127.0.0.1:5000/reports")
    app.run(host='127.0.0.1', port=5000, debug=True)
