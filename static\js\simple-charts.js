/**
 * مكتبة الرسوم البيانية البسيطة
 * Simple Charts Library for JAF Meeting System
 */

// متغيرات عامة
let currentChart = null;
let currentChartType = 'monthly';

// بيانات وهمية للاختبار
const sampleData = {
    monthly: {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
        datasets: [{
            label: 'عدد الاجتماعات',
            data: [12, 19, 8, 15, 22, 18],
            borderColor: '#1B4332',
            backgroundColor: 'rgba(27, 67, 50, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    },
    types: {
        labels: ['اجتماع', 'ورشة عمل', 'مؤتمر', 'ندوة', 'لقاء'],
        datasets: [{
            label: 'أنواع الفعاليات',
            data: [30, 15, 10, 20, 25],
            backgroundColor: [
                '#1B4332',
                '#2D5A3D',
                '#40916C',
                '#52B788',
                '#74C69D'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    status: {
        labels: ['نشط', 'ملغي', 'مؤجل', 'منتهي'],
        datasets: [{
            label: 'حالة الاجتماعات',
            data: [45, 5, 8, 42],
            backgroundColor: [
                '#28a745',
                '#dc3545',
                '#ffc107',
                '#6c757d'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    departments: {
        labels: ['الدائرة المالية', 'الموارد البشرية', 'العمليات', 'التخطيط', 'الأمن'],
        datasets: [{
            label: 'الجهات الداعية',
            data: [25, 18, 22, 15, 20],
            backgroundColor: '#1B4332',
            borderColor: '#2D5A3D',
            borderWidth: 2
        }]
    }
};

// تهيئة الرسوم البيانية
function initializeCharts() {
    console.log('🎨 بدء تهيئة الرسوم البيانية...');
    
    // التحقق من وجود Chart.js
    if (typeof Chart === 'undefined') {
        console.error('❌ Chart.js غير محمل');
        showChartError('مكتبة الرسوم البيانية غير محملة');
        return;
    }
    
    // التحقق من وجود العنصر
    const canvas = document.getElementById('mainChart');
    if (!canvas) {
        console.error('❌ عنصر mainChart غير موجود');
        showChartError('عنصر الرسم البياني غير موجود');
        return;
    }
    
    console.log('✅ Chart.js محمل بنجاح');
    console.log('✅ عنصر mainChart موجود');
    
    // إنشاء الرسم البياني الافتراضي
    createChart('monthly');
}

// إنشاء رسم بياني
function createChart(type) {
    console.log(`🎨 إنشاء رسم بياني من نوع: ${type}`);
    
    const canvas = document.getElementById('mainChart');
    const ctx = canvas.getContext('2d');
    
    // تدمير الرسم البياني السابق إن وجد
    if (currentChart) {
        currentChart.destroy();
    }
    
    // إعداد البيانات
    const data = sampleData[type] || sampleData.monthly;
    
    // إعداد الخيارات
    const options = getChartOptions(type);
    
    // إنشاء الرسم البياني
    try {
        currentChart = new Chart(ctx, {
            type: getChartType(type),
            data: data,
            options: options
        });
        
        currentChartType = type;
        console.log(`✅ تم إنشاء الرسم البياني بنجاح: ${type}`);
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء الرسم البياني:', error);
        showChartError('فشل في إنشاء الرسم البياني');
    }
}

// الحصول على نوع الرسم البياني
function getChartType(type) {
    switch (type) {
        case 'monthly':
            return 'line';
        case 'types':
        case 'status':
            return 'doughnut';
        case 'departments':
            return 'bar';
        default:
            return 'line';
    }
}

// الحصول على خيارات الرسم البياني
function getChartOptions(type) {
    const baseOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
                labels: {
                    font: {
                        family: 'Segoe UI, Tahoma, Arial, sans-serif',
                        size: 12
                    },
                    usePointStyle: true,
                    padding: 20
                }
            },
            tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#fff',
                bodyColor: '#fff',
                borderColor: '#1B4332',
                borderWidth: 1,
                cornerRadius: 8,
                displayColors: true
            }
        }
    };
    
    // خيارات خاصة حسب النوع
    switch (type) {
        case 'monthly':
            return {
                ...baseOptions,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            font: {
                                family: 'Segoe UI, Tahoma, Arial, sans-serif'
                            }
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            font: {
                                family: 'Segoe UI, Tahoma, Arial, sans-serif'
                            }
                        }
                    }
                }
            };
            
        case 'departments':
            return {
                ...baseOptions,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            };
            
        case 'types':
        case 'status':
            return {
                ...baseOptions,
                cutout: '60%',
                plugins: {
                    ...baseOptions.plugins,
                    legend: {
                        ...baseOptions.plugins.legend,
                        position: 'right'
                    }
                }
            };
            
        default:
            return baseOptions;
    }
}

// تبديل الرسم البياني
function switchChart(type) {
    console.log(`🔄 تبديل الرسم البياني إلى: ${type}`);
    
    // تحديث الأزرار
    document.querySelectorAll('.chart-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    event.target.closest('.chart-tab').classList.add('active');
    
    // إظهار مؤشر التحميل
    showLoadingIndicator();
    
    // إنشاء الرسم البياني الجديد
    setTimeout(() => {
        createChart(type);
        hideLoadingIndicator();
    }, 500);
}

// إظهار مؤشر التحميل
function showLoadingIndicator() {
    const indicator = document.getElementById('chartLoadingIndicator');
    if (indicator) {
        indicator.style.display = 'block';
    }
}

// إخفاء مؤشر التحميل
function hideLoadingIndicator() {
    const indicator = document.getElementById('chartLoadingIndicator');
    if (indicator) {
        indicator.style.display = 'none';
    }
}

// إظهار رسالة خطأ
function showChartError(message) {
    const canvas = document.getElementById('mainChart');
    if (canvas) {
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // رسم رسالة الخطأ
        ctx.fillStyle = '#dc3545';
        ctx.font = '16px Segoe UI, Tahoma, Arial, sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText(message, canvas.width / 2, canvas.height / 2);
        
        ctx.fillStyle = '#6c757d';
        ctx.font = '14px Segoe UI, Tahoma, Arial, sans-serif';
        ctx.fillText('يرجى المحاولة مرة أخرى', canvas.width / 2, canvas.height / 2 + 30);
    }
}

// اختبار الرسم البياني
function testChart() {
    console.log('🧪 اختبار الرسم البياني...');
    
    if (currentChart) {
        console.log('✅ الرسم البياني يعمل بشكل صحيح');
        alert('✅ الرسم البياني يعمل بشكل صحيح!');
    } else {
        console.log('❌ الرسم البياني غير موجود');
        alert('❌ الرسم البياني غير موجود!');
    }
}

// فحص الحالة
function checkStatus() {
    console.log('🔍 فحص حالة النظام...');
    
    const status = {
        chartJs: typeof Chart !== 'undefined',
        canvas: !!document.getElementById('mainChart'),
        currentChart: !!currentChart,
        chartType: currentChartType
    };
    
    console.log('📊 حالة النظام:', status);
    
    let message = 'حالة النظام:\n';
    message += `Chart.js: ${status.chartJs ? '✅' : '❌'}\n`;
    message += `Canvas: ${status.canvas ? '✅' : '❌'}\n`;
    message += `الرسم الحالي: ${status.currentChart ? '✅' : '❌'}\n`;
    message += `النوع: ${status.chartType}`;
    
    alert(message);
}

// طباعة الرسم البياني
function printChart() {
    if (currentChart) {
        window.print();
    } else {
        alert('لا يوجد رسم بياني للطباعة');
    }
}

// تحميل الرسم البياني
function downloadChart() {
    if (currentChart) {
        const canvas = document.getElementById('mainChart');
        const url = canvas.toDataURL('image/png');
        
        const link = document.createElement('a');
        link.download = `chart_${currentChartType}_${new Date().getTime()}.png`;
        link.href = url;
        link.click();
    } else {
        alert('لا يوجد رسم بياني للتحميل');
    }
}

// تهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 تم تحميل الصفحة، بدء تهيئة الرسوم البيانية...');
    
    // انتظار قليل للتأكد من تحميل جميع المكتبات
    setTimeout(initializeCharts, 100);
});

console.log('📊 تم تحميل مكتبة الرسوم البيانية البسيطة');
