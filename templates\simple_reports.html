<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقارير الاجتماعات - بسيط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { background: white; border-radius: 20px; padding: 30px; margin: 20px auto; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
        .stats-card { 
            background: linear-gradient(135deg, #f8f9fa, #e9ecef); 
            border-radius: 15px; 
            padding: 25px; 
            margin: 15px 0; 
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #007bff;
            transition: transform 0.3s ease;
        }
        .stats-card:hover { transform: translateY(-5px); }
        .stat-number { 
            font-size: 3.5rem; 
            font-weight: bold; 
            color: #2c3e50; 
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .stat-label { color: #6c757d; font-size: 1.2rem; font-weight: 600; }
        .year-select { 
            background: linear-gradient(135deg, #b8860b, #daa520); 
            color: white; 
            border: none; 
            padding: 12px 25px; 
            border-radius: 12px; 
            font-weight: bold; 
            font-size: 1.1rem;
            box-shadow: 0 4px 15px rgba(184, 134, 11, 0.3);
            transition: all 0.3s ease;
        }
        .year-select:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(184, 134, 11, 0.4); }
        .btn-print { 
            background: linear-gradient(135deg, #28a745, #20c997); 
            color: white; 
            border: none; 
            padding: 12px 25px; 
            border-radius: 10px; 
            margin: 8px; 
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-print:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3); }
        .btn-pdf { 
            background: linear-gradient(135deg, #dc3545, #c82333); 
            color: white; 
            border: none; 
            padding: 12px 25px; 
            border-radius: 10px; 
            margin: 8px; 
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-pdf:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3); }
        .monthly-table { 
            width: 100%; 
            margin: 20px 0; 
            border-radius: 10px; 
            overflow: hidden; 
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .monthly-table th, .monthly-table td { 
            padding: 15px; 
            text-align: center; 
            border: 1px solid #dee2e6; 
        }
        .monthly-table th { 
            background: linear-gradient(135deg, #2c3e50, #34495e); 
            color: white; 
            font-weight: bold; 
            font-size: 1.1rem;
        }
        .monthly-table tr:nth-child(even) { background: #f8f9fa; }
        .monthly-table tr:hover { background: #e3f2fd; }
        .loading { text-align: center; padding: 20px; color: #6c757d; }
        .alert { border-radius: 12px; font-weight: 600; }
        .page-title { 
            background: linear-gradient(135deg, #2c3e50, #34495e); 
            color: white; 
            padding: 20px; 
            border-radius: 15px; 
            margin-bottom: 30px; 
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-title">
            <h1><i class="fas fa-chart-bar me-3"></i>تقارير الاجتماعات</h1>
            <h4>القوات المسلحة الأردنية - مديرية الدائرة المالية</h4>
        </div>
        
        <div class="text-center mb-4">
            <label for="yearSelect" class="me-3" style="font-size: 1.2rem; font-weight: bold;">
                <i class="fas fa-calendar-year me-2"></i>اختر السنة:
            </label>
            <select id="yearSelect" class="year-select" onchange="loadData()">
                <option value="2024">📅 2024</option>
                <option value="2025">📅 2025</option>
                <option value="2026">📅 2026</option>
                <option value="2027">📅 2027</option>
                <option value="2028">📅 2028</option>
                <option value="2029">📅 2029</option>
                <option value="2030">📅 2030</option>
            </select>
            <button class="btn-print" onclick="printReport()">
                <i class="fas fa-print me-2"></i>طباعة
            </button>
            <button class="btn-pdf" onclick="exportPDF()">
                <i class="fas fa-file-pdf me-2"></i>تصدير PDF
            </button>
        </div>

        <!-- البطاقات الإحصائية -->
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stat-number" id="totalMeetings">0</div>
                    <div class="stat-label"><i class="fas fa-clipboard-list me-2"></i>إجمالي الاجتماعات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stat-number" id="completedMeetings" style="color: #28a745;">0</div>
                    <div class="stat-label"><i class="fas fa-check-circle me-2"></i>اجتماعات مكتملة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stat-number" id="upcomingMeetings" style="color: #007bff;">0</div>
                    <div class="stat-label"><i class="fas fa-clock me-2"></i>اجتماعات قادمة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stat-number" id="cancelledMeetings" style="color: #dc3545;">0</div>
                    <div class="stat-label"><i class="fas fa-times-circle me-2"></i>اجتماعات ملغاة</div>
                </div>
            </div>
        </div>

        <!-- الجدول الشهري -->
        <div class="stats-card mt-4">
            <h4><i class="fas fa-calendar-alt me-2"></i>التفصيل الشهري لسنة <span id="currentYear">2024</span></h4>
            <table class="monthly-table table">
                <thead>
                    <tr>
                        <th><i class="fas fa-calendar me-2"></i>الشهر</th>
                        <th><i class="fas fa-check me-2"></i>مكتملة</th>
                        <th><i class="fas fa-clock me-2"></i>قادمة</th>
                        <th><i class="fas fa-times me-2"></i>ملغاة</th>
                        <th><i class="fas fa-sum me-2"></i>المجموع</th>
                    </tr>
                </thead>
                <tbody id="monthlyTableBody">
                    <tr><td colspan="5" class="loading"><i class="fas fa-spinner fa-spin me-2"></i>جاري التحميل...</td></tr>
                </tbody>
            </table>
        </div>

        <!-- رسالة الحالة -->
        <div id="statusMessage" class="alert alert-info mt-3" style="display: none;"></div>
    </div>

    <script>
        // تحميل البيانات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 بدء تحميل التقارير البسيطة...');
            loadData();
        });

        // دالة تحميل البيانات
        async function loadData() {
            const year = document.getElementById('yearSelect').value;
            document.getElementById('currentYear').textContent = year;
            showStatus('جاري تحميل البيانات...', 'info');
            
            try {
                console.log(`📊 جلب بيانات سنة ${year}...`);
                
                // جلب الإحصائيات العامة
                const statsResponse = await fetch('/api/simple-stats');
                const statsData = await statsResponse.json();
                
                console.log('📈 بيانات الإحصائيات:', statsData);
                
                if (statsData.success) {
                    document.getElementById('totalMeetings').textContent = statsData.total_meetings;
                    document.getElementById('completedMeetings').textContent = statsData.completed_meetings;
                    document.getElementById('upcomingMeetings').textContent = statsData.upcoming_meetings;
                    document.getElementById('cancelledMeetings').textContent = statsData.cancelled_meetings;
                }

                // جلب البيانات الشهرية
                const monthlyResponse = await fetch(`/api/simple-monthly/${year}`);
                const monthlyData = await monthlyResponse.json();
                
                console.log('📅 البيانات الشهرية:', monthlyData);
                
                if (monthlyData.success) {
                    updateMonthlyTable(monthlyData.months);
                    showStatus(`✅ تم تحميل بيانات سنة ${year} بنجاح!`, 'success');
                } else {
                    showStatus('❌ فشل في تحميل البيانات الشهرية', 'danger');
                }
                
            } catch (error) {
                console.error('❌ خطأ:', error);
                showStatus('❌ حدث خطأ في تحميل البيانات', 'danger');
            }
        }

        // تحديث الجدول الشهري
        function updateMonthlyTable(months) {
            const tbody = document.getElementById('monthlyTableBody');
            let html = '';
            
            months.forEach(month => {
                const rowClass = month.total > 0 ? '' : 'text-muted';
                html += `
                    <tr class="${rowClass}">
                        <td><strong>${month.month}</strong></td>
                        <td><span class="badge bg-success">${month.completed}</span></td>
                        <td><span class="badge bg-primary">${month.upcoming}</span></td>
                        <td><span class="badge bg-danger">${month.cancelled}</span></td>
                        <td><strong>${month.total}</strong></td>
                    </tr>
                `;
            });
            
            if (html === '') {
                html = '<tr><td colspan="5" class="text-center text-muted">لا توجد بيانات لهذه السنة</td></tr>';
            }
            
            tbody.innerHTML = html;
        }

        // إظهار رسالة الحالة
        function showStatus(message, type) {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.className = `alert alert-${type} mt-3`;
            statusDiv.innerHTML = `<i class="fas fa-info-circle me-2"></i>${message}`;
            statusDiv.style.display = 'block';
            
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 4000);
        }

        // طباعة التقرير
        function printReport() {
            showStatus('🖨️ جاري إعداد التقرير للطباعة...', 'info');
            setTimeout(() => {
                window.print();
            }, 1000);
        }

        // تصدير PDF
        function exportPDF() {
            showStatus('📄 لحفظ كـ PDF: اضغط Ctrl+P ← اختر "حفظ كـ PDF" ← احفظ', 'warning');
            setTimeout(() => {
                window.print();
            }, 2000);
        }
    </script>
</body>
</html>
