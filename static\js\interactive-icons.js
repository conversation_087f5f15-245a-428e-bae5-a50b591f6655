/**
 * تأثيرات الأيقونات التفاعلية
 * Interactive Icons Effects for JAF Meeting System
 */

document.addEventListener('DOMContentLoaded', function() {
    // تأثيرات الأيقونات في البطاقات
    const actionCards = document.querySelectorAll('.action-card');
    
    actionCards.forEach(function(card) {
        const icon = card.querySelector('.action-icon i');
        
        if (icon) {
            // تأثير عند التمرير
            card.addEventListener('mouseenter', function() {
                icon.style.transform = 'scale(1.2) rotate(5deg)';
                icon.style.transition = 'all 0.3s ease';
            });
            
            card.addEventListener('mouseleave', function() {
                icon.style.transform = 'scale(1) rotate(0deg)';
            });
            
            // تأثير عند النقر
            card.addEventListener('click', function() {
                icon.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    icon.style.transform = 'scale(1.1)';
                    setTimeout(() => {
                        icon.style.transform = 'scale(1)';
                    }, 150);
                }, 100);
            });
        }
    });
    
    // تأثيرات الأزرار
    const buttons = document.querySelectorAll('.btn');
    
    buttons.forEach(function(button) {
        const icon = button.querySelector('i');
        
        if (icon) {
            button.addEventListener('mouseenter', function() {
                icon.style.transform = 'scale(1.1)';
                icon.style.transition = 'all 0.2s ease';
            });
            
            button.addEventListener('mouseleave', function() {
                icon.style.transform = 'scale(1)';
            });
        }
    });
    
    // تأثيرات الأيقونات في الجدول
    const tableIcons = document.querySelectorAll('table i');
    
    tableIcons.forEach(function(icon) {
        icon.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.2)';
            this.style.transition = 'all 0.2s ease';
        });
        
        icon.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
    
    // تأثير النبض للإشعارات
    const notifications = document.querySelectorAll('.alert');
    
    notifications.forEach(function(notification) {
        const icon = notification.querySelector('i');
        
        if (icon) {
            // تأثير النبض
            icon.style.animation = 'pulse 2s infinite';
        }
    });
});

// إضافة CSS للتأثيرات
const style = document.createElement('style');
style.textContent = `
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
    
    .action-card {
        transition: all 0.3s ease;
    }
    
    .action-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .btn {
        transition: all 0.2s ease;
    }
    
    .btn:hover {
        transform: translateY(-2px);
    }
`;

document.head.appendChild(style);
