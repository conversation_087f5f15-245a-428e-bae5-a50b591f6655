<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار قالب الطباعة الموحد</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/unified-print-styles.css') }}">
    <style>
        body {
            font-family: 'Cairo', 'Tahoma', 'Arial', sans-serif;
            background: #f8f9fa;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .test-button {
            background: linear-gradient(135deg, #1B4332 0%, #2D5A3D 100%);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            font-size: 14px;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
        }
        .test-title {
            color: #1B4332;
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 18px;
        }
        .test-description {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="text-center mb-4">
            <h1 class="text-primary">
                <i class="fas fa-print me-2"></i>
                اختبار قالب الطباعة الموحد
            </h1>
            <p class="text-muted">اختبر جميع أنواع التقارير بالتصميم الموحد الجديد</p>
        </div>

        <!-- اختبار طباعة اجتماع -->
        <div class="test-section">
            <h3 class="test-title">
                <i class="fas fa-calendar-alt me-2"></i>
                طباعة تفاصيل اجتماع
            </h3>
            <p class="test-description">
                اختبار طباعة تفاصيل اجتماع كاملة بالتصميم الموحد الجديد مع جميع المعلومات والأيقونات الملونة.
            </p>
            <button class="test-button" onclick="testMeetingPrint()">
                <i class="fas fa-print me-2"></i>
                طباعة اجتماع تجريبي
            </button>
        </div>

        <!-- اختبار طباعة إحصائية -->
        <div class="test-section">
            <h3 class="test-title">
                <i class="fas fa-chart-bar me-2"></i>
                طباعة بطاقة إحصائية
            </h3>
            <p class="test-description">
                اختبار طباعة بطاقة إحصائية مع القيم والأوصاف بالتصميم الموحد.
            </p>
            <button class="test-button" onclick="testStatPrint()">
                <i class="fas fa-print me-2"></i>
                طباعة إحصائية تجريبية
            </button>
        </div>

        <!-- اختبار تقرير مخصص -->
        <div class="test-section">
            <h3 class="test-title">
                <i class="fas fa-file-alt me-2"></i>
                طباعة تقرير مخصص
            </h3>
            <p class="test-description">
                اختبار طباعة تقرير مخصص مع محتوى متنوع وأيقونات ملونة.
            </p>
            <button class="test-button" onclick="testCustomReport()">
                <i class="fas fa-print me-2"></i>
                طباعة تقرير مخصص
            </button>
        </div>

        <!-- اختبار تقرير شامل -->
        <div class="test-section">
            <h3 class="test-title">
                <i class="fas fa-clipboard-list me-2"></i>
                طباعة تقرير شامل
            </h3>
            <p class="test-description">
                اختبار طباعة تقرير شامل يحتوي على جميع أنواع البيانات والعناصر.
            </p>
            <button class="test-button" onclick="testComprehensiveReport()">
                <i class="fas fa-print me-2"></i>
                طباعة تقرير شامل
            </button>
        </div>

        <!-- اختبار شاشة البحث -->
        <div class="test-section">
            <h3 class="test-title">
                <i class="fas fa-search me-2"></i>
                طباعة من شاشة البحث
            </h3>
            <p class="test-description">
                اختبار طباعة النتائج من شاشة البحث - جميع الاجتماعات في جدول واحد في صفحة واحدة.
            </p>
            <button class="test-button" onclick="testSearchPrint()">
                <i class="fas fa-print me-2"></i>
                طباعة نتائج بحث تجريبية
            </button>
        </div>

        <!-- معاينة التصميم -->
        <div class="test-section">
            <h3 class="test-title">
                <i class="fas fa-eye me-2"></i>
                معاينة التصميم
            </h3>
            <p class="test-description">
                معاينة التصميم الموحد بدون طباعة فعلية.
            </p>
            <button class="test-button" onclick="previewDesign()">
                <i class="fas fa-eye me-2"></i>
                معاينة التصميم
            </button>
        </div>

        <!-- معلومات إضافية -->
        <div class="alert alert-info mt-4">
            <h5 class="alert-heading">
                <i class="fas fa-info-circle me-2"></i>
                التحديثات الجديدة
            </h5>
            <ul class="mb-0">
                <li>✅ تم حذف الشعار من الأعلى</li>
                <li>✅ تم نقل التاريخ والوقت إلى الهامش السفلي</li>
                <li>✅ تم إضافة "مديرية الدائرة المالية" في الوسط السفلي</li>
                <li>✅ تصميم موحد لجميع التقارير</li>
                <li>✅ طباعة شاشة البحث: جميع الاجتماعات في جدول واحد</li>
                <li>✅ تصميم متجاوب ويعمل على جميع الأحجام</li>
            </ul>
        </div>
    </div>

    <!-- تحميل القالب الموحد -->
    <script src="{{ url_for('static', filename='js/unified-print-template.js') }}"></script>

    <script>
        // اختبار طباعة اجتماع
        function testMeetingPrint() {
            const meeting = {
                subject: 'اجتماع مجلس الإدارة الشهري',
                meeting_type: 'اجتماع إداري',
                meeting_date: '2025/01/22',
                meeting_time: '10:30',
                location: 'قاعة الاجتماعات الرئيسية',
                inviting_party: 'مديرية الدائرة المالية',
                arrival_time_before: '15',
                book_number: '456/2024',
                book_date: '2025/01/15',
                dress_code: 'زي رسمي',
                notes: 'يرجى إحضار الأوراق المطلوبة لمناقشة الميزانية الجديدة'
            };

            printMeetingReport(meeting);
        }

        // اختبار طباعة إحصائية
        function testStatPrint() {
            const data = {
                title: 'إجمالي الاجتماعات',
                subtitle: 'إحصائية شهرية - نظام إدارة المواعيد',
                content: [
                    {
                        icon: 'fas fa-chart-bar',
                        label: 'القيمة',
                        value: '150 اجتماع'
                    },
                    {
                        icon: 'fas fa-info-circle',
                        label: 'الوصف',
                        value: 'إجمالي عدد الاجتماعات المجدولة هذا الشهر'
                    }
                ],
                showBorder: true,
                customStyles: `
                    .detail-row:first-child .detail-value {
                        font-size: 24px;
                        font-weight: bold;
                        color: #1B4332;
                    }
                `
            };

            printUnifiedReport(data);
        }

        // اختبار تقرير مخصص
        function testCustomReport() {
            const data = {
                title: 'تقرير الأداء الأسبوعي',
                subtitle: 'نظام إدارة المواعيد - الأسبوع الثالث من يناير',
                content: [
                    {
                        icon: 'fas fa-calendar-check',
                        label: 'الاجتماعات المكتملة',
                        value: '28 اجتماع'
                    },
                    {
                        icon: 'fas fa-clock',
                        label: 'متوسط مدة الاجتماع',
                        value: '45 دقيقة'
                    },
                    {
                        icon: 'fas fa-users',
                        label: 'عدد المشاركين',
                        value: '156 مشارك'
                    },
                    {
                        icon: 'fas fa-star',
                        label: 'تقييم الرضا',
                        value: '4.8/5.0'
                    }
                ],
                showBorder: true
            };

            printUnifiedReport(data);
        }

        // اختبار تقرير شامل
        function testComprehensiveReport() {
            const data = {
                title: 'التقرير الشهري الشامل',
                subtitle: 'نظام إدارة المواعيد - يناير 2025',
                content: [
                    {
                        icon: 'fas fa-file-alt',
                        label: 'الموضوع',
                        value: 'تقرير شامل عن أداء النظام'
                    },
                    {
                        icon: 'fas fa-calendar',
                        label: 'فترة التقرير',
                        value: '01/01/2025 - 31/01/2025'
                    },
                    {
                        icon: 'fas fa-chart-line',
                        label: 'إجمالي الاجتماعات',
                        value: '245 اجتماع'
                    },
                    {
                        icon: 'fas fa-check-circle',
                        label: 'الاجتماعات المكتملة',
                        value: '198 اجتماع (80.8%)'
                    },
                    {
                        icon: 'fas fa-times-circle',
                        label: 'الاجتماعات الملغاة',
                        value: '12 اجتماع (4.9%)'
                    },
                    {
                        icon: 'fas fa-clock',
                        label: 'الاجتماعات المؤجلة',
                        value: '35 اجتماع (14.3%)'
                    },
                    {
                        icon: 'fas fa-building',
                        label: 'الأقسام المشاركة',
                        value: '15 قسم'
                    },
                    {
                        icon: 'fas fa-user-tie',
                        label: 'إجمالي المشاركين',
                        value: '1,247 مشارك'
                    }
                ],
                showBorder: true
            };

            printUnifiedReport(data);
        }

        // اختبار طباعة شاشة البحث - جميع الاجتماعات في جدول واحد
        function testSearchPrint() {
            // محاكاة بيانات نتائج البحث
            const searchResults = [
                {
                    subject: 'العلاوات بشط اجتماع', // سيتم تنظيفه إلى "العلاوات بشط"
                    meeting_type: 'اجتماع إداري',
                    meeting_date: '2025/01/23',
                    meeting_time: '09:00',
                    location: 'قاعة الاجتماعات الكبرى',
                    inviting_party: 'مديرية الدائرة المالية',
                    arrival_time_before: '15',
                    book_number: '123/2025',
                    book_date: '2025/01/20',
                    dress_code: 'رسمي',
                    notes: 'يرجى إحضار التقارير المالية',
                    index: 1
                },
                {
                    subject: 'اجتماع قرارات بشط عادي', // سيتم تنظيفه إلى "قرارات بشط"
                    meeting_type: 'اجتماع لجنة',
                    meeting_date: '2025/01/24',
                    meeting_time: '11:30',
                    location: 'قاعة الاجتماعات الصغرى',
                    inviting_party: 'لجنة المراجعة الداخلية',
                    arrival_time_before: '10',
                    book_number: '124/2025',
                    book_date: '2025/01/21',
                    dress_code: 'رسمي',
                    notes: 'مراجعة الحسابات الربعية',
                    index: 2
                },
                {
                    subject: 'اجتماع بلوتو بشط عادي', // سيتم تنظيفه إلى "بلوتو بشط"
                    meeting_type: 'اجتماع تخطيط',
                    meeting_date: '2025/01/25',
                    meeting_time: '14:00',
                    location: 'قاعة المؤتمرات',
                    inviting_party: 'إدارة التخطيط والتطوير',
                    arrival_time_before: '20',
                    book_number: '125/2025',
                    book_date: '2025/01/22',
                    dress_code: 'رسمي',
                    notes: 'مناقشة الخطة الخمسية الجديدة',
                    index: 3
                },
                {
                    subject: 'متابعة المشاريع اجتماع طارئ', // سيتم تنظيفه إلى "متابعة المشاريع"
                    meeting_type: 'اجتماع متابعة',
                    meeting_date: '2025/01/26',
                    meeting_time: '10:15',
                    location: 'قاعة الاجتماعات الفرعية',
                    inviting_party: 'إدارة المشاريع',
                    arrival_time_before: '15',
                    book_number: '126/2025',
                    book_date: '2025/01/23',
                    dress_code: 'رسمي',
                    notes: 'متابعة تقدم المشاريع الجارية',
                    index: 4
                },
                {
                    subject: 'الموارد البشرية - اجتماع دوري', // سيتم تنظيفه إلى "الموارد البشرية"
                    meeting_type: 'اجتماع إداري',
                    meeting_date: '2025/01/27',
                    meeting_time: '13:30',
                    location: 'مكتب الموارد البشرية',
                    inviting_party: 'إدارة الموارد البشرية',
                    arrival_time_before: '10',
                    book_number: '127/2025',
                    book_date: '2025/01/24',
                    dress_code: 'رسمي',
                    notes: '',
                    index: 5
                }
            ];

            // تنظيف مواضيع الاجتماعات قبل الطباعة
            const cleanedResults = searchResults.map(meeting => ({
                ...meeting,
                subject: cleanMeetingSubjectTest(meeting.subject)
            }));

            // استخدام وظيفة طباعة الجدول الجديدة
            if (typeof printAllMeetingsInTable === 'function') {
                printAllMeetingsInTable(cleanedResults);
            } else {
                // طريقة احتياطية - إنشاء جدول يدوياً
                createTableReport(cleanedResults);
            }
        }

        // وظيفة احتياطية لإنشاء تقرير جدول
        function createTableReport(meetings) {
            let tableHTML = `
                <table style="width: 100%; border-collapse: collapse; font-size: 11px;">
                    <thead>
                        <tr style="background: #1B4332; color: white;">
                            <th style="border: 1px solid #1B4332; padding: 8px; text-align: center;">#</th>
                            <th style="border: 1px solid #1B4332; padding: 8px;">الموضوع</th>
                            <th style="border: 1px solid #1B4332; padding: 8px;">النوع</th>
                            <th style="border: 1px solid #1B4332; padding: 8px;">التاريخ</th>
                            <th style="border: 1px solid #1B4332; padding: 8px;">الوقت</th>
                            <th style="border: 1px solid #1B4332; padding: 8px;">المكان</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            meetings.forEach((meeting, index) => {
                const bgColor = index % 2 === 0 ? 'white' : '#f8f9fa';
                tableHTML += `
                    <tr style="background: ${bgColor};">
                        <td style="border: 1px solid #ddd; padding: 6px; text-align: center; font-weight: bold;">${index + 1}</td>
                        <td style="border: 1px solid #ddd; padding: 6px;"><strong>${meeting.subject}</strong></td>
                        <td style="border: 1px solid #ddd; padding: 6px;">${meeting.meeting_type}</td>
                        <td style="border: 1px solid #ddd; padding: 6px;">${meeting.meeting_date}</td>
                        <td style="border: 1px solid #ddd; padding: 6px;">${meeting.meeting_time}</td>
                        <td style="border: 1px solid #ddd; padding: 6px;">${meeting.location}</td>
                    </tr>
                `;
            });

            tableHTML += '</tbody></table>';

            const data = {
                title: 'تقرير نتائج البحث - جدول الاجتماعات',
                subtitle: `${meetings.length} اجتماع - نظام إدارة المواعيد`,
                content: [
                    {
                        icon: 'fas fa-table',
                        label: 'جدول الاجتماعات',
                        value: tableHTML
                    }
                ],
                showBorder: true
            };

            printUnifiedReport(data);
        }

        // وظيفة مساعدة لطباعة عدة اجتماعات (نسخة للاختبار)
        function printMultipleMeetingsDetailed(meetings) {
            console.log('🖨️ اختبار طباعة عدة اجتماعات بالتفصيل:', meetings.length);

            // إنشاء محتوى HTML موحد يحتوي على جميع الاجتماعات
            let allMeetingsHTML = '';

            meetings.forEach((meeting, index) => {
                // إنشاء محتوى كل اجتماع بالتفصيل الكامل
                const meetingContent = [
                    { icon: 'fas fa-file-alt', label: 'الموضوع', value: meeting.subject },
                    { icon: 'fas fa-tag', label: 'نوع الفعالية', value: meeting.meeting_type },
                    { icon: 'fas fa-calendar', label: 'التاريخ', value: meeting.meeting_date },
                    { icon: 'fas fa-clock', label: 'الوقت', value: meeting.meeting_time },
                    { icon: 'fas fa-map-marker-alt', label: 'المكان', value: meeting.location },
                    { icon: 'fas fa-building', label: 'جهة الدعوة', value: meeting.inviting_party },
                    { icon: 'fas fa-user-clock', label: 'الحضور قبل الموعد', value: `${meeting.arrival_time_before} دقيقة` },
                    { icon: 'fas fa-hashtag', label: 'رقم الكتاب', value: meeting.book_number },
                    { icon: 'fas fa-calendar-alt', label: 'تاريخ الكتاب', value: meeting.book_date },
                    { icon: 'fas fa-user-tie', label: 'زي الحضور', value: meeting.dress_code }
                ];

                // إضافة الملاحظات إذا وجدت
                if (meeting.notes && meeting.notes.trim()) {
                    meetingContent.push({
                        icon: 'fas fa-sticky-note',
                        label: 'ملاحظات',
                        value: meeting.notes
                    });
                }

                // إنشاء HTML لكل اجتماع
                const meetingData = {
                    title: meeting.subject,
                    subtitle: `الاجتماع رقم ${meeting.index} - تفاصيل كاملة`,
                    content: meetingContent,
                    showBorder: true
                };

                // إنشاء HTML للاجتماع
                const meetingHTML = createUnifiedPrintTemplate(meetingData);

                // إضافة فاصل صفحة بين الاجتماعات (إلا للاجتماع الأخير)
                if (index < meetings.length - 1) {
                    allMeetingsHTML += meetingHTML.replace('</body>', '<div style="page-break-after: always;"></div></body>');
                } else {
                    allMeetingsHTML += meetingHTML;
                }
            });

            // فتح نافذة طباعة جديدة
            const printWindow = window.open('', '_blank', 'width=800,height=600');
            printWindow.document.write(allMeetingsHTML);
            printWindow.document.close();

            // انتظار تحميل المحتوى ثم الطباعة
            printWindow.onload = function() {
                setTimeout(() => {
                    printWindow.focus();
                    printWindow.print();
                    printWindow.onafterprint = function() {
                        setTimeout(() => printWindow.close(), 1000);
                    };
                }, 500);
            };
        }

        // تنظيف موضوع الاجتماع من حالة الاجتماع (نسخة للاختبار)
        function cleanMeetingSubjectTest(subject) {
            if (!subject || subject === 'غير محدد') {
                return subject;
            }

            // قائمة بحالات الاجتماعات التي يجب إزالتها
            const meetingStates = [
                'اجتماع عادي',
                'اجتماع بلوتو',
                'اجتماع نشط',
                'اجتماع ملغي',
                'اجتماع مؤجل',
                'اجتماع طارئ',
                'اجتماع استثنائي',
                'اجتماع دوري',
                'اجتماع أسبوعي',
                'اجتماع شهري',
                'اجتماع ربعي',
                'اجتماع سنوي'
            ];

            let cleanedSubject = subject;

            // إزالة حالات الاجتماع من بداية أو نهاية الموضوع
            meetingStates.forEach(state => {
                // إزالة من البداية
                if (cleanedSubject.startsWith(state)) {
                    cleanedSubject = cleanedSubject.substring(state.length).trim();
                }

                // إزالة من النهاية
                if (cleanedSubject.endsWith(state)) {
                    cleanedSubject = cleanedSubject.substring(0, cleanedSubject.length - state.length).trim();
                }

                // إزالة من الوسط مع فواصل
                cleanedSubject = cleanedSubject.replace(new RegExp(`\\s*${state}\\s*`, 'gi'), ' ').trim();
            });

            // إزالة الفواصل الزائدة والمسافات المتعددة
            cleanedSubject = cleanedSubject
                .replace(/\s*-\s*/g, ' ') // إزالة الشرطات
                .replace(/\s*:\s*/g, ' ') // إزالة النقطتين
                .replace(/\s*،\s*/g, ' ') // إزالة الفواصل العربية
                .replace(/\s*,\s*/g, ' ') // إزالة الفواصل الإنجليزية
                .replace(/\s+/g, ' ') // إزالة المسافات المتعددة
                .trim();

            // إذا أصبح الموضوع فارغاً، إرجاع النص الأصلي
            if (!cleanedSubject || cleanedSubject.length < 3) {
                return subject;
            }

            console.log(`🧹 تنظيف الموضوع (اختبار): "${subject}" → "${cleanedSubject}"`);
            return cleanedSubject;
        }

        // معاينة التصميم
        function previewDesign() {
            alert('سيتم فتح معاينة التصميم في نافذة جديدة بدون طباعة فعلية');

            const data = {
                title: 'معاينة التصميم الموحد',
                subtitle: 'نموذج للتصميم الجديد',
                content: [
                    {
                        icon: 'fas fa-palette',
                        label: 'التصميم',
                        value: 'موحد وجميل'
                    },
                    {
                        icon: 'fas fa-mobile-alt',
                        label: 'التجاوب',
                        value: 'يعمل على جميع الأحجام'
                    }
                ],
                showBorder: true
            };

            // إنشاء HTML بدون طباعة
            const html = createUnifiedPrintTemplate(data);
            const previewWindow = window.open('', '_blank', 'width=800,height=600');
            previewWindow.document.write(html);
            previewWindow.document.close();
        }

        // رسالة ترحيب
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ تم تحميل صفحة اختبار القالب الموحد');
            console.log('🎨 جميع الوظائف جاهزة للاختبار');
        });
    </script>
</body>
</html>
