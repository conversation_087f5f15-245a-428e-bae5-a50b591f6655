// -*- coding: utf-8 -*-
/**
 * JavaScript لصفحة إضافة اجتماع جديد
 * نظام إدارة المواعيد - القوات المسلحة الأردنية
 */

document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

function initializePage() {
    setupDressCodeSelection();
    setupFileUpload();
    setupFormValidation();
    setupConflictCheck();
    setDefaultValues();
    enhanceDateInputs();
}

// إعداد اختيار نوع اللباس
function setupDressCodeSelection() {
    const dressCodeOptions = document.querySelectorAll('.dress-code-option');
    const dressCodeInput = document.getElementById('dress_code');
    
    dressCodeOptions.forEach(option => {
        option.addEventListener('click', function() {
            // إزالة التحديد من جميع الخيارات
            dressCodeOptions.forEach(opt => opt.classList.remove('selected'));
            
            // تحديد الخيار المختار
            this.classList.add('selected');
            dressCodeInput.value = this.dataset.value;
            
            // تأثير بصري
            this.style.transform = 'scale(1.05)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 200);
        });
    });
}

// إعداد رفع الملفات
function setupFileUpload() {
    const fileUploadArea = document.getElementById('fileUploadArea');
    const fileInput = document.getElementById('book_attachment');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const removeFileBtn = document.getElementById('removeFile');
    
    // النقر على منطقة الرفع
    fileUploadArea.addEventListener('click', () => {
        fileInput.click();
    });
    
    // تغيير الملف
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            if (validateFile(file)) {
                showFileInfo(file);
            } else {
                this.value = '';
                NotificationUtils.showError('يرجى اختيار ملف PDF صالح (الحد الأقصى: 16 ميجابايت)');
            }
        }
    });
    
    // سحب وإفلات الملفات
    fileUploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });
    
    fileUploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });
    
    fileUploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            if (validateFile(file)) {
                fileInput.files = files;
                showFileInfo(file);
            } else {
                NotificationUtils.showError('يرجى اختيار ملف PDF صالح (الحد الأقصى: 16 ميجابايت)');
            }
        }
    });
    
    // إزالة الملف
    removeFileBtn.addEventListener('click', function() {
        fileInput.value = '';
        fileInfo.style.display = 'none';
        NotificationUtils.showInfo('تم إزالة الملف');
    });
    
    function validateFile(file) {
        const maxSize = 16 * 1024 * 1024; // 16MB
        const allowedTypes = ['application/pdf'];
        
        return file.size <= maxSize && allowedTypes.includes(file.type);
    }
    
    function showFileInfo(file) {
        fileName.textContent = file.name + ' (' + formatFileSize(file.size) + ')';
        fileInfo.style.display = 'block';
        // استخدام النظام الموحد للإشعارات
        if (typeof showNotification === 'function') {
            showNotification('📎 تم اختيار الملف بنجاح', 'success');
        }
    }
    
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 بايت';
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// إعداد التحقق من صحة النموذج
function setupFormValidation() {
    const form = document.getElementById('meetingForm');
    if (!form) return;

    // التحقق من تاريخ الكتاب مقارنة بتاريخ الاجتماع
    const bookDateInput = document.getElementById('book_date');
    const meetingDateInput = document.getElementById('meeting_date');

    // دالة التحقق من تاريخ الكتاب
    function validateBookDate() {
        // التحقق من وجود قيم
        if (!bookDateInput || !meetingDateInput) return true;
        if (!bookDateInput.value || !meetingDateInput.value) return true;

        const bookDate = new Date(bookDateInput.value + 'T00:00:00');
        const meetingDate = new Date(meetingDateInput.value + 'T00:00:00');

        // التحقق من صحة التواريخ
        if (isNaN(bookDate.getTime()) || isNaN(meetingDate.getTime())) {
            return true;
        }

        if (bookDate > meetingDate) {
            // إضافة الصف الأحمر
            bookDateInput.classList.add('is-invalid');

            // إزالة أي رسائل خطأ سابقة
            hideFieldError(bookDateInput);

            // إضافة رسالة الخطأ تحت الحقل
            showFieldError(bookDateInput, 'تاريخ الكتاب يجب أن يكون مساوياً أو أقل من تاريخ الاجتماع');

            // إظهار تنبيه في منتصف الشاشة
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: 'خطأ في التاريخ',
                    text: 'تاريخ الكتاب يجب أن يكون مساوياً أو أقل من تاريخ الاجتماع',
                    icon: 'error',
                    confirmButtonText: 'حسناً',
                    confirmButtonColor: '#dc3545',
                    background: '#fff',
                    customClass: {
                        popup: 'rtl-alert'
                    }
                }).then(() => {
                    // مسح قيمة تاريخ الكتاب بعد الضغط على "حسناً"
                    bookDateInput.value = '';
                });
            } else {
                alert('تاريخ الكتاب يجب أن يكون مساوياً أو أقل من تاريخ الاجتماع');
                bookDateInput.value = '';
            }

            return false;
        } else {
            bookDateInput.classList.remove('is-invalid');
            hideFieldError(bookDateInput);
            return true;
        }
    }

    // إضافة مستمعي الأحداث
    if (bookDateInput) {
        bookDateInput.addEventListener('change', validateBookDate);
        bookDateInput.addEventListener('blur', validateBookDate);
    }
    if (meetingDateInput) {
        meetingDateInput.addEventListener('change', validateBookDate);
        meetingDateInput.addEventListener('blur', validateBookDate);
    }

    // إضافة التحقق إلى النموذج العام
    window.validateBookDateGlobal = validateBookDate;
    
    // إعداد فحص تضارب المواعيد
function setupConflictCheck() {
    const dateInput = document.getElementById('meeting_date');
    const timeInput = document.getElementById('meeting_time');
    
    function checkConflicts() {
        const date = dateInput.value;
        const time = timeInput.value;

        if (date && time) {
            fetch('/api/check_conflict', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    meeting_date: date,
                    meeting_time: time
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.has_conflict) {
                    showConflictModal(data.conflicts);
                } else {
                    hideConflictWarning();
                }
            })
            .catch(error => {
                console.error('Error checking conflict:', error);
                hideConflictWarning();
            });
        }
    }
    
    dateInput.addEventListener('change', checkConflicts);
    timeInput.addEventListener('change', checkConflicts);
}

// عرض رسالة التضارب في منتصف الشاشة
function showConflictModal(conflicts) {
    // إنشاء المودال إذا لم يكن موجوداً
    let modal = document.getElementById('conflictModal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'conflictModal';
        modal.className = 'conflict-modal';
        modal.innerHTML = `
            <div class="conflict-modal-content">
                <div class="conflict-modal-header">
    function validateBookDate() {
        if (!bookDateInput || !meetingDateInput) return true;
        if (!bookDateInput.value || !meetingDateInput.value) return true;

        // تحويل التواريخ إلى كائنات Date
        const bookDate = new Date(bookDateInput.value);
        const meetingDate = new Date(meetingDateInput.value);
        bookDate.setHours(0, 0, 0, 0);
        meetingDate.setHours(0, 0, 0, 0);

        if (isNaN(bookDate.getTime()) || isNaN(meetingDate.getTime())) {
            return true;
        }

        if (bookDate > meetingDate) {
            bookDateInput.classList.add('is-invalid');
            hideFieldError(bookDateInput);
            showFieldError(bookDateInput, 'تاريخ الكتاب يجب أن يكون مساوياً أو أقل من تاريخ الاجتماع');
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: 'خطأ في التاريخ',
                    text: 'تاريخ الكتاب يجب أن يكون مساوياً أو أقل من تاريخ الاجتماع',
                    icon: 'error',
                    confirmButtonText: 'حسناً',
                    confirmButtonColor: '#dc3545',
                    background: '#fff',
                    customClass: {
                        popup: 'rtl-alert'
                    }
                }).then(() => {
                    bookDateInput.value = '';
                });
            } else {
                alert('تاريخ الكتاب يجب أن يكون مساوياً أو أقل من تاريخ الاجتماع');
                bookDateInput.value = '';
            }
            return false;
        } else {
            bookDateInput.classList.remove('is-invalid');
            hideFieldError(bookDateInput);
            return true;
        }
    }
// إخفاء رسالة التضارب
function hideConflictModal() {
    const modal = document.getElementById('conflictModal');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => modal.style.display = 'none', 300);
    }
}

// إخفاء تحذير التضارب
function hideConflictWarning() {
    const conflictWarning = document.getElementById('conflictWarning');
    if (conflictWarning) {
        conflictWarning.style.display = 'none';
    }
}

// المتابعة رغم التضارب
function proceedWithConflict() {
    hideConflictModal();
    // يمكن إضافة علامة للإشارة إلى أن المستخدم وافق على التضارب
    window.conflictAccepted = true;
}

// تعيين القيم الافتراضية
function setDefaultValues() {
    // تعيين التاريخ الحالي كحد أدنى
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('meeting_date').min = today;
    document.getElementById('book_date').max = today;
    
    // تعيين تاريخ الكتاب إلى اليوم
    document.getElementById('book_date').value = today;
}

// التحقق من صحة النموذج
function validateForm() {
    let isValid = true;
    const requiredFields = [
        'meeting_type', 'meeting_date', 'meeting_time', 'location', 
        'subject', 'inviting_party', 'book_number', 'book_date'
    ];
    
    // التحقق من الحقول المطلوبة
    requiredFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            showFieldError(field, 'هذا الحقل مطلوب');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
            hideFieldError(field);
        }
    });
    
    // التحقق من اختيار نوع اللباس
    const dressCode = document.getElementById('dress_code');
    if (!dressCode.value) {
        NotificationUtils.showError('يرجى اختيار نوع اللباس المطلوب');
        document.querySelector('.dress-code-grid').scrollIntoView({ behavior: 'smooth' });
        isValid = false;
    }
    
    // التحقق من رقم الكتاب
    const bookNumber = document.getElementById('book_number').value;
    if (bookNumber && !ValidationUtils.validateBookNumber(bookNumber)) {
        document.getElementById('book_number').classList.add('is-invalid');
        showFieldError(document.getElementById('book_number'), 'يجب أن يكون رقم الكتاب رقماً صحيحاً');
        isValid = false;
    }
    
    // التحقق من التاريخ
    const meetingDate = new Date(document.getElementById('meeting_date').value);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (meetingDate < today) {
        document.getElementById('meeting_date').classList.add('is-invalid');
        showFieldError(document.getElementById('meeting_date'), 'لا يمكن أن يكون تاريخ الاجتماع في الماضي');
        isValid = false;
    }
    
    return isValid;
}

// إرسال النموذج
function submitForm() {
    const form = document.getElementById('meetingForm');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    // تعطيل الزر وإظهار مؤشر التحميل
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
    
    // إنشاء FormData
    const formData = new FormData(form);
    
    // إرسال البيانات
    fetch(form.action || window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // استخدام النظام الموحد للإشعارات
            if (typeof showNotification === 'function') {
                showNotification('✅ تم حفظ الاجتماع بنجاح', 'success');
            }

            // إعادة تعيين النموذج
            form.reset();

            // إعادة تعيين الأزرار
            document.querySelectorAll('.dress-code-option').forEach(opt => opt.classList.remove('selected'));
            document.querySelector('.dress-code-option[data-value="رسمي"]')?.classList.add('selected');

            // إعادة تفعيل الزر
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>حفظ الاجتماع';

            // إعادة تعيين القيم الافتراضية
            setDefaultValues();

        } else {
            NotificationUtils.showError(data.message || 'حدث خطأ أثناء حفظ الاجتماع');
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>حفظ الاجتماع';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        NotificationUtils.showError('حدث خطأ في الاتصال بالخادم');
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>حفظ الاجتماع';
    });
}

// إظهار خطأ الحقل
function showFieldError(field, message) {
    hideFieldError(field);
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

// إخفاء خطأ الحقل
function hideFieldError(field) {
    const existingError = field.parentNode.querySelector('.invalid-feedback');
    if (existingError) {
        existingError.remove();
    }
}

// إعادة تعيين النموذج
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
        document.getElementById('meetingForm').reset();
        
        // إعادة تعيين اختيار نوع اللباس
        document.querySelectorAll('.dress-code-option').forEach(opt => {
            opt.classList.remove('selected');
        });
        document.getElementById('dress_code').value = '';
        
        // إخفاء معلومات الملف
        document.getElementById('fileInfo').style.display = 'none';
        
        // إخفاء تحذير التضارب
        document.getElementById('conflictWarning').style.display = 'none';
        
        // إزالة أخطاء التحقق
        document.querySelectorAll('.is-invalid').forEach(field => {
            field.classList.remove('is-invalid');
        });
        document.querySelectorAll('.invalid-feedback').forEach(error => {
            error.remove();
        });
        
        // إعادة تعيين القيم الافتراضية
        setDefaultValues();
        
        NotificationUtils.showInfo('تم إعادة تعيين النموذج');
    }
}

// دالة تنسيق التاريخ بالصيغة الجديدة (سنة/شهر/يوم بدون أصفار زائدة)
function formatDateRTL(dateString) {
    if (!dateString) return '';

    const [year, month, day] = dateString.split('-');
    // إزالة الأصفار الزائدة وتنسيق سنة/شهر/يوم
    const formattedDate = `${year}/${parseInt(month)}/${parseInt(day)}`;

    return ensureEnglishNumbers(formattedDate);
}

// دالة ضمان الأرقام الإنجليزية
function ensureEnglishNumbers(text) {
    if (!text) return '';

    const arabicDigits = '٠١٢٣٤٥٦٧٨٩';
    const englishDigits = '0123456789';

    let result = text.toString();

    // تحويل الأرقام العربية للإنجليزية فقط
    for (let i = 0; i < arabicDigits.length; i++) {
        result = result.replace(new RegExp(arabicDigits[i], 'g'), englishDigits[i]);
    }

    // لا نحذف الأحرف العربية أو الرموز الأخرى - فقط نحول الأرقام
    return result;
}

// ضمان الأرقام الإنجليزية في جميع الحقول الرقمية
function ensureEnglishTimeInputs() {
    // جميع حقول الوقت والأرقام والبحث
    const timeInputs = document.querySelectorAll(
        'input[type="time"], input[type="number"], input[type="tel"], input[type="search"], ' +
        '#meeting_time, #arrival_time_before, #book_number, #attendance_count, ' +
        '#meeting_duration, #search_book_number, #filter_year, #filter_month, ' +
        '.number-input, .time-input, .search-input, .filter-input'
    );

    timeInputs.forEach(input => {
        // تطبيق الأرقام الإنجليزية عند الكتابة
        input.addEventListener('input', function() {
            const oldValue = this.value;
            const newValue = ensureEnglishNumbers(oldValue);
            if (oldValue !== newValue) {
                this.value = newValue;
            }
        });

        // تطبيق الأرقام الإنجليزية عند التركيز
        input.addEventListener('focus', function() {
            this.value = ensureEnglishNumbers(this.value);
        });

        // تطبيق الأرقام الإنجليزية عند فقدان التركيز
        input.addEventListener('blur', function() {
            this.value = ensureEnglishNumbers(this.value);
        });

        // تطبيق الأرقام الإنجليزية عند لصق النص
        input.addEventListener('paste', function(e) {
            setTimeout(() => {
                this.value = ensureEnglishNumbers(this.value);
            }, 10);
        });

        // تطبيق الأرقام الإنجليزية على القيمة الحالية
        if (input.value) {
            input.value = ensureEnglishNumbers(input.value);
        }
    });
}

// دالة تحويل الأرقام العربية للإنجليزية (للإرسال للخادم)
function convertToEnglishNumbers(str) {
    const englishDigits = '0123456789';
    const arabicDigits = '٠١٢٣٤٥٦٧٨٩';

    let result = str;
    for (let i = 0; i < arabicDigits.length; i++) {
        result = result.replace(new RegExp(arabicDigits[i], 'g'), englishDigits[i]);
    }
    return result;
}

// دالة تحسين حقول التاريخ لعرض الأرقام العربية
function enhanceDateInputs() {
    console.log('🗓️ تحسين حقول التاريخ للأرقام العربية...');

    // العثور على جميع حقول التاريخ
    const dateInputs = document.querySelectorAll('input[type="date"]');

    dateInputs.forEach(input => {
        // إضافة عنصر عرض مخصص
        const displayDiv = document.createElement('div');
        displayDiv.className = 'arabic-date-display';
        displayDiv.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: white;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
            padding: 0.75rem;
            font-family: 'Cairo', monospace;
            font-size: 1rem;
            color: #495057;
            direction: rtl;
            text-align: right;
            cursor: pointer;
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            transition: all 0.2s ease;
        `;

        // إخفاء حقل التاريخ الأصلي
        input.style.opacity = '0';
        input.style.position = 'relative';
        input.style.zIndex = '1';

        // إضافة العنصر المخصص
        input.parentNode.style.position = 'relative';
        input.parentNode.appendChild(displayDiv);

        // دالة تحديث العرض
        function updateDisplay() {
            if (input.value) {
                const [year, month, day] = input.value.split('-');
                const formattedDate = formatDateRTL(input.value);
                displayDiv.textContent = formattedDate;
                displayDiv.style.color = '#495057';
                displayDiv.style.fontWeight = '600';
            } else {
                displayDiv.textContent = 'اختر التاريخ';
                displayDiv.style.color = '#6c757d';
                displayDiv.style.fontWeight = '400';
            }
        }

        // تحديث العرض عند تغيير القيمة
        input.addEventListener('change', updateDisplay);
        input.addEventListener('input', updateDisplay);

        // فتح منتقي التاريخ عند النقر على العرض المخصص
        displayDiv.addEventListener('click', () => {
            input.focus();
            input.showPicker && input.showPicker();
        });

        // تأثيرات بصرية
        displayDiv.addEventListener('mouseenter', () => {
            displayDiv.style.borderColor = '#007bff';
            displayDiv.style.boxShadow = '0 0 0 0.2rem rgba(0, 123, 255, 0.25)';
        });

        displayDiv.addEventListener('mouseleave', () => {
            displayDiv.style.borderColor = '#ced4da';
            displayDiv.style.boxShadow = 'none';
        });

        // تحديث العرض الأولي
        updateDisplay();

        console.log('✅ تم تحسين حقل التاريخ:', input.id || input.name);
    });
}

// تشغيل الدوال عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تحميل صفحة إضافة الاجتماع...');

    // تطبيق الأرقام الإنجليزية في حقول الوقت
    ensureEnglishTimeInputs();

    // تحسين حقول التاريخ
    enhanceDateInputs();

    // تطبيق الأرقام الإنجليزية على جميع العناصر الموجودة
    applyEnglishNumbersToPage();

    // تحويل دوري للأرقام العربية
    setInterval(() => {
        applyEnglishNumbersToPage();
    }, 1000);

    console.log('✅ تم تحميل جميع التحسينات بنجاح!');
});

// دالة تطبيق الأرقام الإنجليزية على الصفحة بالكامل
function applyEnglishNumbersToPage() {
    // تطبيق على جميع العناصر التي قد تحتوي على أرقام عربية
    const elementsWithNumbers = document.querySelectorAll(`
        .number, .count, .badge, .meeting-id, .book-number,
        .attendance-count, .stats-number, .total-count,
        .active-count, .cancelled-count, .postponed-count,
        .metric-value, .arabic-date, .arabic-time,
        .date-display, .time-display, .card-text
    `);

    elementsWithNumbers.forEach(element => {
        // تطبيق تحويل الأرقام على جميع العناصر
        if (element.textContent && /[٠-٩]/.test(element.textContent)) {
            element.textContent = ensureEnglishNumbers(element.textContent);
        }

        // تطبيق على قيم الحقول
        if (element.value && /[٠-٩]/.test(element.value)) {
            element.value = ensureEnglishNumbers(element.value);
        }
    });

    // تطبيق شامل على جميع عناصر الجدول
    const tableElements = document.querySelectorAll('table td, table th, .table-cell');
    tableElements.forEach(element => {
        if (element.textContent && /[٠-٩]/.test(element.textContent)) {
            element.textContent = ensureEnglishNumbers(element.textContent);
        }
    });

    // تطبيق على جميع العناصر التي تحتوي على أرقام عربية
    const allElements = document.querySelectorAll('*');
    allElements.forEach(element => {
        // تحقق من النص المباشر فقط (ليس النصوص الفرعية)
        if (element.childNodes.length === 1 &&
            element.childNodes[0].nodeType === Node.TEXT_NODE &&
            /[٠-٩]/.test(element.textContent)) {
            element.textContent = ensureEnglishNumbers(element.textContent);
        }
    });

    console.log('✅ تم تطبيق الأرقام الإنجليزية على جميع العناصر');
}
