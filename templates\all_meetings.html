{% extends "base.html" %}

{% block title %}كل الاجتماعات - نظام إدارة المواعيد{% endblock %}

{% block extra_css %}
<style>
/* نظام التنبيهات الجميل */
.custom-alert {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px 40px;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
    z-index: 10000;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    min-width: 300px;
    animation: alertSlideIn 0.3s ease-out;
}

.custom-alert.success {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

.custom-alert.info {
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
}

.custom-alert.warning {
    background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
}

.custom-alert.error {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
}

.custom-alert-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 9999;
    animation: fadeIn 0.3s ease-out;
}

@keyframes alertSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.custom-alert-close {
    position: absolute;
    top: 10px;
    right: 15px;
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    opacity: 0.7;
}

.custom-alert-close:hover {
    opacity: 1;
}

/* تحسينات البطاقات */
.stats-card {
    transition: all 0.4s ease;
    cursor: pointer;
    border: none;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    min-height: 180px;
}

.stats-card:hover {
    transform: translateY(-8px) scale(1.03);
    box-shadow: 0 12px 35px rgba(0,0,0,0.25);
}

/* ظل أحمر خاص للاجتماعات المنتهية عند النقر */
.stats-card.finished.selected {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 15px 40px rgba(220, 53, 69, 0.6), 0 0 30px rgba(220, 53, 69, 0.4);
    border: 2px solid rgba(220, 53, 69, 0.8);
}

.stats-card.finished.selected .card-body {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    animation: finishedPulse 2s ease-in-out infinite;
}

@keyframes finishedPulse {
    0%, 100% {
        box-shadow: inset 0 0 20px rgba(255, 255, 255, 0.2);
    }
    50% {
        box-shadow: inset 0 0 30px rgba(255, 255, 255, 0.4);
    }
}

.stats-card.selected {
    transform: translateY(-10px) scale(1.03);
    box-shadow: 0 15px 40px rgba(27, 67, 50, 0.4);
}

.stats-card .card-body {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
}

/* تحسينات الأزرار */
.filter-btn {
    transition: all 0.3s ease;
    border-radius: 30px;
    padding: 12px 25px;
    font-weight: 700;
    font-size: 16px;
    border: none;
    margin: 8px;
    min-width: 130px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.filter-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.25);
}

.filter-btn.active {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.35);
}

/* تحسينات الاجتماعات */
.meeting-item {
    transition: all 0.3s ease;
    border-radius: 10px;
    margin-bottom: 15px;
    border: 1px solid #e0e0e0;
}

.meeting-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* تحسينات responsive */
@media (max-width: 1200px) {
    .stats-card {
        min-height: 160px;
    }
}

@media (max-width: 992px) {
    .stats-card {
        min-height: 150px;
        margin-bottom: 20px;
    }

    .filter-btn {
        min-width: 110px;
        padding: 10px 20px;
        font-size: 14px;
    }
}

@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 15px;
        min-height: 140px;
    }

    .filter-btn {
        width: 100%;
        margin: 5px 0;
        min-width: auto;
    }
}
    
    .custom-alert {
        min-width: 280px;
        padding: 20px 25px;
        font-size: 16px;
    }
}

/* أنماط الأزرار الجميلة */
.action-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    margin-top: 8px;
    flex-wrap: wrap;
}

.action-btn {
    border: none;
    padding: 14px 24px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    color: white;
    position: relative;
    overflow: hidden;
    min-height: 48px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
    flex-shrink: 0;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.action-btn:hover::before {
    left: 100%;
}

.print-btn {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    border: 1px solid rgba(220, 38, 38, 0.2);
}

.print-btn:hover {
    background: linear-gradient(135deg, #b91c1c, #991b1b);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
}

.details-btn {
    background: linear-gradient(135deg, #4f46e5, #3730a3);
    border: 1px solid rgba(79, 70, 229, 0.2);
}

.details-btn:hover {
    background: linear-gradient(135deg, #3730a3, #312e81);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
}

.edit-btn {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.edit-btn:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
}

.action-btn:active {
    transform: translateY(-1px);
    transition: transform 0.1s;
}

.action-btn i {
    font-size: 16px;
    opacity: 0.9;
}

.action-btn span {
    font-weight: 700;
    letter-spacing: 0.025em;
}

/* تجاوب الأزرار على الشاشات الصغيرة */
@media (max-width: 768px) {
    .action-buttons {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .action-btn {
        padding: 16px 20px;
        font-size: 15px;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .action-buttons {
        gap: 10px;
    }

    .action-btn {
        padding: 14px 18px;
        font-size: 14px;
        min-height: 44px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- العنوان الرئيسي -->
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="page-title text-center">
                <i class="fas fa-calendar-alt me-3"></i>
                كل الاجتماعات ({{ meetings|length }})
            </h2>
        </div>
    </div>

    <!-- البطاقات الإحصائية -->
    <div class="row mb-4">
        <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-3">
            <div class="card stats-card h-100" data-filter="all" style="cursor: pointer;" title="انقر لعرض جميع الاجتماعات" onclick="filterMeetings('all')">
                <div class="card-body text-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px;">
                    <i class="fas fa-list fa-3x mb-3"></i>
                    <h2 class="mb-2">{{ meetings|length }}</h2>
                    <p class="mb-0 fs-5 fw-bold">الكل</p>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-3">
            <div class="card stats-card active h-100" data-filter="active" style="cursor: pointer;" title="انقر لعرض الاجتماعات النشطة" onclick="filterMeetings('active')">
                <div class="card-body text-center" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); color: white; padding: 25px;">
                    <i class="fas fa-play fa-3x mb-3"></i>
                    <h2 class="mb-2">{{ active_meetings }}</h2>
                    <p class="mb-0 fs-5 fw-bold">نشط</p>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-3">
            <div class="card stats-card postponed h-100" data-filter="postponed" style="cursor: pointer;" title="انقر لعرض الاجتماعات المؤجلة" onclick="filterMeetings('postponed')">
                <div class="card-body text-center" style="background: linear-gradient(135deg, #FFB75E 0%, #ED8F03 100%); color: white; padding: 25px;">
                    <i class="fas fa-clock fa-3x mb-3"></i>
                    <h2 class="mb-2">{{ postponed_meetings }}</h2>
                    <p class="mb-0 fs-5 fw-bold">مؤجل</p>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-3">
            <div class="card stats-card cancelled h-100" data-filter="cancelled" style="cursor: pointer;" title="انقر لعرض الاجتماعات الملغية" onclick="filterMeetings('cancelled')">
                <div class="card-body text-center" style="background: linear-gradient(135deg, #FF416C 0%, #FF4B2B 100%); color: white; padding: 25px;">
                    <i class="fas fa-times fa-3x mb-3"></i>
                    <h2 class="mb-2">{{ cancelled_meetings }}</h2>
                    <p class="mb-0 fs-5 fw-bold">ملغي</p>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 mb-3">
            <div class="card stats-card finished h-100" data-filter="finished" style="cursor: pointer;" title="انقر لعرض الاجتماعات المنتهية" onclick="filterMeetings('finished')">
                <div class="card-body text-center" style="background: linear-gradient(135deg, #654ea3 0%, #eaafc8 100%); color: white; padding: 25px;">
                    <i class="fas fa-check fa-3x mb-3"></i>
                    <h2 class="mb-2">{{ finished_meetings }}</h2>
                    <p class="mb-0 fs-5 fw-bold">منتهية</p>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الفلترة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex flex-wrap justify-content-center gap-3">
                <button class="btn btn-primary filter-btn active px-4 py-2" data-filter="all" title="عرض جميع الاجتماعات" onclick="filterMeetings('all')">
                    <i class="fas fa-list me-2"></i>الكل
                </button>
                <button class="btn btn-success filter-btn px-4 py-2" data-filter="active" title="عرض الاجتماعات النشطة" onclick="filterMeetings('active')">
                    <i class="fas fa-play me-2"></i>نشط
                </button>
                <button class="btn btn-warning filter-btn px-4 py-2" data-filter="postponed" title="عرض الاجتماعات المؤجلة" onclick="filterMeetings('postponed')">
                    <i class="fas fa-clock me-2"></i>مؤجل
                </button>
                <button class="btn btn-danger filter-btn px-4 py-2" data-filter="cancelled" title="عرض الاجتماعات الملغية" onclick="filterMeetings('cancelled')">
                    <i class="fas fa-times me-2"></i>ملغي
                </button>
                <button class="btn btn-secondary filter-btn px-4 py-2" data-filter="finished" title="عرض الاجتماعات المنتهية" onclick="filterMeetings('finished')">
                    <i class="fas fa-check me-2"></i>منتهية
                </button>
            </div>
        </div>
    </div>

    <!-- قائمة الاجتماعات -->
    <div class="row">
        <div class="col-12">
            <!-- رسالة تصحيح -->
            <div class="alert alert-success mb-3">
                <strong>📊 إحصائيات التصحيح:</strong>
                عدد الاجتماعات المرسلة من الخادم: {{ meetings|length }}
                {% if meetings %}
                <br><strong>الاجتماعات:</strong>
                {% for meeting in meetings %}
                    <br>- {{ meeting.subject }} ({{ meeting.meeting_date | arabic_date }})
                {% endfor %}
                {% endif %}
            </div>

            <div id="meetings-container">
                {% for meeting in meetings %}
                <div class="meeting-item card mb-3"
                     data-status="{{ 'cancelled' if meeting.is_cancelled else ('postponed' if meeting.is_postponed else 'active') }}"
                     data-date="{{ meeting.meeting_date | arabic_date }}"
                     style="display: block !important; visibility: visible !important; opacity: 1 !important;">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h5 class="card-title mb-2">
                                    <i class="fas fa-calendar-check me-2 text-primary"></i>
                                    {{ meeting.subject }}
                                </h5>
                                <div class="row">
                                    <div class="col-sm-6">
                                        <p class="mb-1">
                                            <i class="fas fa-calendar text-success me-1"></i>
                                            <strong>التاريخ:</strong> {{ meeting.meeting_date | arabic_date }}
                                        </p>
                                        <p class="mb-1">
                                            <i class="fas fa-clock text-warning me-1"></i>
                                            <strong>الوقت:</strong> {{ meeting.meeting_time | military_time }}
                                        </p>
                                    </div>
                                    <div class="col-sm-6">
                                        <p class="mb-1">
                                            <i class="fas fa-map-marker-alt me-1"></i>
                                            <strong>مكان الاجتماع:</strong> {{ meeting.location }}
                                        </p>
                                        <p class="mb-1">
                                            <i class="fas fa-building text-info me-1"></i>
                                            <strong>جهة الدعوة:</strong> {{ meeting.inviting_party }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="mb-2">
                                    {% if meeting.is_cancelled %}
                                        <span class="badge bg-danger fs-6 px-3 py-2">
                                            <i class="fas fa-times-circle me-1"></i>ملغي
                                        </span>
                                    {% elif meeting.is_postponed %}
                                        <span class="badge bg-warning text-dark fs-6 px-3 py-2">
                                            <i class="fas fa-clock me-1"></i>مؤجل
                                        </span>
                                    {% else %}
                                        <span class="badge bg-success fs-6 px-3 py-2">
                                            <i class="fas fa-check-circle me-1"></i>نشط
                                        </span>
                                    {% endif %}
                                </div>
                                <div class="action-buttons">
                                    <button class="action-btn print-btn"
                                            data-meeting-id="{{ meeting.id }}"
                                            data-meeting-subject="{{ meeting.subject }}"
                                            data-meeting-type="{{ meeting.meeting_type }}"
                                            data-meeting-date="{{ meeting.meeting_date }}"
                                            data-meeting-time="{{ meeting.meeting_time }}"
                                            data-meeting-location="{{ meeting.location }}"
                                            data-meeting-party="{{ meeting.inviting_party }}"
                                            onclick="testPrint(this)">
                                        <i class="fas fa-print"></i>
                                        <span>طباعة</span>
                                    </button>

                                    <a href="{{ url_for('meeting_details', meeting_id=meeting.id) }}" class="action-btn details-btn">
                                        <i class="fas fa-eye"></i>
                                        <span>عرض التفاصيل</span>
                                    </a>

                                    <a href="{{ url_for('edit_meeting', id=meeting.id) }}" class="action-btn edit-btn">
                                        <i class="fas fa-edit"></i>
                                        <span>تعديل</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}

                {% if not meetings %}
                <div class="alert alert-info text-center mt-4">
                    <i class="fas fa-info-circle me-2"></i>
                    لا توجد اجتماعات مسجلة حالياً
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

<script>
// اختبار أساسي لـ JavaScript
console.log('JavaScript يعمل!');
</script>

<script>
// نظام التنبيهات - استخدام النظام الموحد
function showCustomAlert(message, type = 'info', duration = 3000) {
    if (window.UnifiedNotifications) {
        window.UnifiedNotifications.showNotification(message, type, duration);
    } else {
        console.log('تنبيه:', message, type);
        alert(message);
    }
}

    overlay.appendChild(alert);
    document.body.appendChild(overlay);

    // إزالة التنبيه تلقائياً
    setTimeout(() => {
        if (overlay.parentElement) {
            overlay.remove();
        }
    }, duration);

    // إزالة عند النقر على الخلفية
    overlay.addEventListener('click', function(e) {
        if (e.target === overlay) {
            overlay.remove();
        }
    });
}

// وظيفة فلترة الاجتماعات
function filterMeetings(filterType) {
    console.log('🔍 تطبيق فلتر:', filterType);

    const meetings = document.querySelectorAll('.meeting-item');
    console.log('📋 عدد الاجتماعات الموجودة في DOM:', meetings.length);
    let visibleCount = 0;

    meetings.forEach(function(meeting, index) {
        const status = meeting.getAttribute('data-status') || 'active';
        const meetingDate = meeting.getAttribute('data-date');

        console.log(`📝 اجتماع ${index + 1}:`, {
            status: status,
            date: meetingDate,
            element: meeting
        });

        // تحديد ما إذا كان الاجتماع منتهياً
        let isFinished = false;
        if (meetingDate) {
            const today = new Date();
            today.setHours(0, 0, 0, 0); // تعيين الوقت لبداية اليوم
            const mDate = new Date(meetingDate);
            mDate.setHours(0, 0, 0, 0); // تعيين الوقت لبداية اليوم
            isFinished = mDate < today && status === 'active';
            console.log(`📅 مقارنة التاريخ: اليوم=${today.toDateString()}, الاجتماع=${mDate.toDateString()}, منتهي=${isFinished}`);
        }

        let shouldShow = false;

        if (filterType === 'all') {
            shouldShow = true;
        } else if (filterType === 'finished') {
            shouldShow = isFinished;
        } else if (filterType === 'active') {
            // الاجتماعات النشطة: حالة نشطة وليست منتهية
            shouldShow = (status === 'active' && !isFinished);
        } else if (filterType === 'postponed') {
            shouldShow = (status === 'postponed');
        } else if (filterType === 'cancelled') {
            shouldShow = (status === 'cancelled');
        } else {
            shouldShow = (status === filterType);
        }

        console.log(`👁️ اجتماع ${index + 1} - يجب عرضه: ${shouldShow}`);

        // تطبيق الفلترة الصحيحة
        if (shouldShow) {
            meeting.style.display = 'block';
            meeting.style.visibility = 'visible';
            meeting.style.opacity = '1';
            visibleCount++;
            console.log(`✅ عرض اجتماع ${index + 1}`);
        } else {
            meeting.style.display = 'none';
            meeting.style.visibility = 'hidden';
            meeting.style.opacity = '0';
            console.log(`❌ إخفاء اجتماع ${index + 1}`);
        }
    });

    // تحديث العنوان
    const pageTitle = document.querySelector('.page-title');
    if (pageTitle) {
        const icon = pageTitle.querySelector('i');
        const iconHtml = icon ? icon.outerHTML : '<i class="fas fa-calendar-alt me-3"></i>';
        pageTitle.innerHTML = iconHtml + 'كل الاجتماعات (' + visibleCount + ')';
    }

    // تحديث مظهر البطاقات والأزرار
    updateActiveStates(filterType);

    console.log('✅ تم عرض ' + visibleCount + ' اجتماع');

    // إظهار تنبيه جميل
    const filterNames = {
        'all': 'جميع الاجتماعات',
        'active': 'الاجتماعات النشطة',
        'postponed': 'الاجتماعات المؤجلة',
        'cancelled': 'الاجتماعات الملغية',
        'finished': 'الاجتماعات المنتهية'
    };

    showCustomAlert(`✅ تم تطبيق فلتر: ${filterNames[filterType]}<br>عدد الاجتماعات: ${visibleCount}`, 'success', 2000);
}

// تحديث حالة البطاقات والأزرار
function updateActiveStates(filterType) {
    // تحديث البطاقات
    const cards = document.querySelectorAll('.stats-card');
    cards.forEach(function(card) {
        const cardFilter = card.getAttribute('data-filter');
        if (cardFilter === filterType) {
            card.classList.add('selected');
        } else {
            card.classList.remove('selected');
        }
    });

    // تحديث الأزرار
    const buttons = document.querySelectorAll('.filter-btn');
    buttons.forEach(function(button) {
        const buttonFilter = button.getAttribute('data-filter');
        if (buttonFilter === filterType) {
            button.classList.add('active');
        } else {
            button.classList.remove('active');
        }
    });
}

// وظيفة عرض تفاصيل الاجتماع
function showMeetingDetails(id, subject, type, date, time, location, inviting, bookNum, dress, arrival, notes, bookDate, status) {
    console.log('🔍 عرض تفاصيل الاجتماع:', id);

    // إنشاء نافذة جديدة بالتفاصيل
    const detailsWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');

    const htmlContent = \`
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الاجتماع - \${subject}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 700px;
            margin: 0 auto;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        .card-header {
            background: linear-gradient(135deg, #1B4332 0%, #2D5A3D 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }
        .detail-row {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-left: 20px;
            flex-shrink: 0;
        }
        .detail-content {
            flex: 1;
        }
        .detail-label {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 5px;
        }
        .detail-value {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
        }
        .btn-close-window {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-close-window:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .print-btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            margin-left: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .print-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header text-center">
                <h2><i class="fas fa-calendar-check me-3"></i>تفاصيل الاجتماع</h2>
                <p class="mb-0">القوات المسلحة الأردنية - مديرية الدائرة المالية</p>
            </div>
            <div class="card-body">
                <div class="detail-row">
                    <div class="detail-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="detail-content">
                        <div class="detail-label">موضوع الاجتماع</div>
                        <div class="detail-value">\${subject}</div>
                    </div>
                </div>

                <div class="detail-row">
                    <div class="detail-icon">
                        <i class="fas fa-tag"></i>
                    </div>
                    <div class="detail-content">
                        <div class="detail-label">نوع الفعالية</div>
                        <div class="detail-value">\${type}</div>
                    </div>
                </div>

                <div class="detail-row">
                    <div class="detail-icon">
                        <i class="fas fa-calendar"></i>
                    </div>
                    <div class="detail-content">
                        <div class="detail-label">تاريخ الاجتماع</div>
                        <div class="detail-value">\${date}</div>
                    </div>
                </div>

                <div class="detail-row">
                    <div class="detail-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="detail-content">
                        <div class="detail-label">وقت الاجتماع</div>
                        <div class="detail-value">\${time}</div>
                    </div>
                </div>

                <div class="detail-row">
                    <div class="detail-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="detail-content">
                        <div class="detail-label">مكان الاجتماع</div>
                        <div class="detail-value">\${location}</div>
                    </div>
                </div>

                <div class="detail-row">
                    <div class="detail-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="detail-content">
                        <div class="detail-label">جهة الدعوة</div>
                        <div class="detail-value">\${inviting}</div>
                    </div>
                </div>

                <div class="detail-row">
                    <div class="detail-icon">
                        <i class="fas fa-user-clock"></i>
                    </div>
                    <div class="detail-content">
                        <div class="detail-label">الحضور قبل</div>
                        <div class="detail-value">\${arrival} دقيقة</div>
                    </div>
                </div>

                <div class="detail-row">
                    <div class="detail-icon">
                        <i class="fas fa-tshirt"></i>
                    </div>
                    <div class="detail-content">
                        <div class="detail-label">نوع اللباس</div>
                        <div class="detail-value">\${dress}</div>
                    </div>
                </div>

                <div class="detail-row">
                    <div class="detail-icon">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="detail-content">
                        <div class="detail-label">رقم الكتاب</div>
                        <div class="detail-value">\${bookNum}</div>
                    </div>
                </div>

                \${notes ? \`
                <div class="detail-row">
                    <div class="detail-icon">
                        <i class="fas fa-sticky-note"></i>
                    </div>
                    <div class="detail-content">
                        <div class="detail-label">ملاحظات إضافية</div>
                        <div class="detail-value">\${notes}</div>
                    </div>
                </div>
                \` : ''}

                <div class="text-center mt-4">
                    <button onclick="window.close()" class="btn-close-window">
                        <i class="fas fa-times me-2"></i>
                        إغلاق النافذة
                    </button>
                    <button onclick="window.print()" class="print-btn">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
    \`;

    detailsWindow.document.write(htmlContent);
    detailsWindow.document.close();
    detailsWindow.focus();

    console.log('✅ تم عرض تفاصيل الاجتماع في نافذة جديدة');
}

// تشغيل عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('✅ تم تحميل الصفحة بالكامل');
    console.log('🔧 بدء تهيئة البطاقات والأزرار...');

    // إظهار جميع الاجتماعات فور تحميل الصفحة
    const meetings = document.querySelectorAll('.meeting-item');
    console.log('🔍 إظهار الاجتماعات فور التحميل - العدد:', meetings.length);
    meetings.forEach((meeting, index) => {
        meeting.style.display = 'block';
        meeting.style.visibility = 'visible';
        meeting.style.opacity = '1';
        console.log(`✅ تم إظهار اجتماع ${index + 1}`);
    });

    // إضافة event listeners للبطاقات
    const cards = document.querySelectorAll('.stats-card');
    console.log('🎯 تم العثور على', cards.length, 'بطاقة');
    cards.forEach(function(card) {
        card.addEventListener('click', function() {
            const filterType = this.getAttribute('data-filter');
            console.log('تم النقر على البطاقة:', filterType);

            // رسائل مختلفة حسب نوع البطاقة
            const messages = {
                'all': '🔍 تم النقر على بطاقة جميع الاجتماعات',
                'active': '🟢 تم النقر على بطاقة الاجتماعات النشطة',
                'postponed': '🟡 تم النقر على بطاقة الاجتماعات المؤجلة',
                'cancelled': '🔴 تم النقر على بطاقة الاجتماعات الملغية',
                'finished': '⚫ تم النقر على بطاقة الاجتماعات المنتهية'
            };

            const types = {
                'all': 'info',
                'active': 'success',
                'postponed': 'warning',
                'cancelled': 'error',
                'finished': 'info'
            };

            showCustomAlert(messages[filterType], types[filterType], 1500);
            console.log('✅ تطبيق الفلترة من البطاقات');
            filterMeetings(filterType);
        });
    });

    // إضافة event listeners للأزرار
    const buttons = document.querySelectorAll('.filter-btn');
    console.log('🎯 تم العثور على', buttons.length, 'زر');
    buttons.forEach(function(button) {
        button.addEventListener('click', function() {
            const filterType = this.getAttribute('data-filter');
            console.log('تم النقر على الزر:', filterType);
            console.log('✅ تطبيق الفلترة من الأزرار');
            filterMeetings(filterType);
        });
    });

    // اختبار النظام
    console.log('🚀 سيتم عرض تنبيه الترحيب...');
    showCustomAlert('🚀 تم تحميل نظام إدارة الاجتماعات بنجاح!', 'success', 2000);

    // اختبار وجود الاجتماعات في DOM وإظهارها بشكل صريح
    const meetings = document.querySelectorAll('.meeting-item');
    console.log('🔍 عدد الاجتماعات في DOM:', meetings.length);

    // إظهار جميع الاجتماعات بشكل صريح
    meetings.forEach((meeting, index) => {
        meeting.style.display = 'block';
        meeting.style.visibility = 'visible';
        meeting.style.opacity = '1';
        console.log(`📋 اجتماع ${index + 1}:`, meeting);
        console.log(`   - العنوان: ${meeting.querySelector('.card-title')?.textContent}`);
        console.log(`   - مرئي: ${meeting.style.display !== 'none'}`);
    });

    if (meetings.length === 0) {
        console.error('❌ لا توجد اجتماعات في DOM!');
    } else {
        console.log(`✅ تم إظهار ${meetings.length} اجتماع بنجاح`);
    }

    // تطبيق الفلتر الافتراضي لإظهار جميع الاجتماعات
    console.log('✅ تطبيق الفلترة التلقائية لإظهار جميع الاجتماعات');
    filterMeetings('all');
});

// اختبار بسيط - إضافة alert للبطاقات
window.addEventListener('load', function() {
    console.log('🚀 الصفحة تم تحميلها بالكامل');

    // إضافة onclick للبطاقات
    const cards = document.querySelectorAll('.stats-card');
    console.log('عدد البطاقات:', cards.length);

    cards.forEach(function(card, index) {
        card.onclick = function() {
            const filter = this.getAttribute('data-filter');
            alert('تم النقر على البطاقة: ' + filter);
            console.log('تم النقر على البطاقة:', filter);
        };
    });

    // إضافة onclick للأزرار
    const buttons = document.querySelectorAll('.filter-btn');
    console.log('عدد الأزرار:', buttons.length);

    buttons.forEach(function(button, index) {
        button.onclick = function() {
            const filter = this.getAttribute('data-filter');
            alert('تم النقر على الزر: ' + filter);
            console.log('تم النقر على الزر:', filter);
        };
    });

    alert('تم تحميل الصفحة وتفعيل البطاقات والأزرار!');
});

// وظيفة اختبار بسيطة
function testPrint(button) {
    alert('تم النقر على زر الطباعة!');
    console.log('🖨️ اختبار الطباعة');
    console.log('الزر:', button);

    // استخراج البيانات
    const subject = button.getAttribute('data-meeting-subject');
    const date = button.getAttribute('data-meeting-date');
    const time = button.getAttribute('data-meeting-time');

    console.log('الموضوع:', subject);
    console.log('التاريخ:', date);
    console.log('الوقت:', time);

    if (subject && subject !== 'None') {
        // إنشاء نافذة طباعة بسيطة
        const printWindow = window.open('', '_blank', 'width=600,height=400');
        printWindow.document.write(`
            <html dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>اختبار الطباعة</title>
                <style>
                    body { font-family: Arial; padding: 20px; direction: rtl; }
                    h1 { color: #1B4332; }
                    p { margin: 10px 0; }
                </style>
            </head>
            <body>
                <h1>اختبار طباعة الاجتماع</h1>
                <p><strong>الموضوع:</strong> ${subject}</p>
                <p><strong>التاريخ:</strong> ${date}</p>
                <p><strong>الوقت:</strong> ${time}</p>
                <script>
                    setTimeout(() => window.print(), 1000);
                </script>
            </body>
            </html>
        `);
        printWindow.document.close();
    } else {
        alert('لا توجد بيانات صالحة للطباعة');
    }
}

// وظيفة طباعة بسيطة وموثوقة
function printMeetingSimple(buttonElement) {
    console.log('🖨️ بدء طباعة الاجتماع');
    console.log('🔍 عنصر الزر:', buttonElement);

    // استخراج البيانات من attributes الزر مباشرة
    const meetingData = {
        id: buttonElement.getAttribute('data-meeting-id') || 'غير محدد',
        subject: buttonElement.getAttribute('data-meeting-subject') || 'غير محدد',
        meeting_type: buttonElement.getAttribute('data-meeting-type') || 'اجتماع',
        meeting_date: buttonElement.getAttribute('data-meeting-date') || 'غير محدد',
        meeting_time: buttonElement.getAttribute('data-meeting-time') || 'غير محدد',
        location: buttonElement.getAttribute('data-meeting-location') || 'غير محدد',
        inviting_party: buttonElement.getAttribute('data-meeting-party') || 'غير محدد'
    };

    console.log('📄 البيانات المستخرجة:', meetingData);

    // التحقق من صحة البيانات
    if (!meetingData.subject || meetingData.subject === 'غير محدد' || meetingData.subject.length < 2) {
        console.error('❌ بيانات الاجتماع غير صالحة:', meetingData);
        alert('خطأ: بيانات الاجتماع غير صالحة للطباعة');
        return;
    }

    // طباعة مباشرة
    console.log('🖨️ بدء الطباعة المباشرة');
    printMeetingDirectly(meetingData);
}

// وظيفة طباعة مباشرة مبسطة
function printMeetingDirectly(meetingData) {
    console.log('🖨️ إنشاء نافذة الطباعة...');

    try {
        const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');

        if (!printWindow) {
            alert('تم حظر النوافذ المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة.');
            return;
        }

        const printHTML = `<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الاجتماع</title>
    <style>
        body {
            font-family: 'Arial', 'Tahoma', sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #1B4332, #2D5A3D);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        .header p {
            margin: 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-right: 4px solid #1B4332;
        }
        .icon {
            width: 35px;
            height: 35px;
            background: #1B4332;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-left: 15px;
            font-size: 14px;
        }
        .text {
            flex: 1;
        }
        .label {
            font-weight: bold;
            color: #1B4332;
            font-size: 14px;
            margin-bottom: 5px;
        }
        .value {
            color: #333;
            font-size: 16px;
        }
        @media print {
            body { background: white; padding: 10px; }
            .container { box-shadow: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${meetingData.subject || 'اجتماع'}</h1>
            <p>تفاصيل الاجتماع - نظام إدارة المواعيد</p>
        </div>
        <div class="content">
            <div class="item">
                <div class="icon">📋</div>
                <div class="text">
                    <div class="label">الموضوع</div>
                    <div class="value">${meetingData.subject || 'غير محدد'}</div>
                </div>
            </div>
            <div class="item">
                <div class="icon">🏷️</div>
                <div class="text">
                    <div class="label">نوع الفعالية</div>
                    <div class="value">${meetingData.meeting_type || 'اجتماع'}</div>
                </div>
            </div>
            <div class="item">
                <div class="icon">📅</div>
                <div class="text">
                    <div class="label">التاريخ</div>
                    <div class="value">${meetingData.meeting_date || 'غير محدد'}</div>
                </div>
            </div>
            <div class="item">
                <div class="icon">🕐</div>
                <div class="text">
                    <div class="label">الوقت</div>
                    <div class="value">${meetingData.meeting_time || 'غير محدد'}</div>
                </div>
            </div>
            <div class="item">
                <div class="icon">📍</div>
                <div class="text">
                    <div class="label">مكان الاجتماع</div>
                    <div class="value">${meetingData.location || 'غير محدد'}</div>
                </div>
            </div>
            <div class="item">
                <div class="icon">🏢</div>
                <div class="text">
                    <div class="label">جهة الدعوة</div>
                    <div class="value">${meetingData.inviting_party || 'غير محدد'}</div>
                </div>
            </div>
        </div>
    </div>
    <script>
        window.onload = function() {
            console.log('تم تحميل نافذة الطباعة');
            setTimeout(function() {
                window.print();
            }, 1000);
        };
    </script>
</body>
</html>`;

        printWindow.document.write(printHTML);
        printWindow.document.close();

        console.log('✅ تم إنشاء نافذة الطباعة بنجاح');

    } catch (error) {
        console.error('❌ خطأ في إنشاء نافذة الطباعة:', error);
        alert('خطأ في فتح نافذة الطباعة: ' + error.message);
    }
}


</script>

<!-- قالب الطباعة الموحد -->
<script src="{{ url_for('static', filename='js/unified-print-template.js') }}"></script>

<script>
// التحقق من تحميل القالب الموحد
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        if (typeof printMeetingReport === 'function') {
            console.log('✅ تم تحميل القالب الموحد بنجاح في صفحة جميع الاجتماعات');
        } else {
            console.error('❌ فشل في تحميل القالب الموحد في صفحة جميع الاجتماعات');
        }
    }, 500);
});
</script>
