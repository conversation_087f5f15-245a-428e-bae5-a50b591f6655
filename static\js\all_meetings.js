/**
 * جافا سكريبت صفحة جميع الاجتماعات - نسخة مبسطة وفعالة
 * All Meetings Page JavaScript for JAF Meeting System - Simplified & Working
 */

// متغيرات عامة
let currentFilter = 'all';
let allMeetings = [];

// تهيئة الصفحة
function initializeAllMeetings() {
    console.log('📅 تهيئة صفحة جميع الاجتماعات...');

    // جمع جميع عناصر الاجتماعات
    allMeetings = document.querySelectorAll('.meeting-item');
    console.log(`تم العثور على ${allMeetings.length} اجتماع`);

    // تهيئة البطاقات الإحصائية
    initializeStatsCards();

    // تهيئة أزرار الفلترة
    initializeFilterTabs();

    // تهيئة البحث
    initializeSearch();

    console.log('✅ تم تهيئة صفحة جميع الاجتماعات بنجاح');
}

// تهيئة البطاقات الإحصائية
function initializeStatsCards() {
    console.log('📊 تهيئة البطاقات الإحصائية...');

    const statsCards = document.querySelectorAll('.stats-card');

    statsCards.forEach((card, index) => {
        // إضافة مستمع الأحداث
        card.addEventListener('click', function() {
            console.log(`تم النقر على البطاقة ${index}`);

            // تحديد نوع الفلتر حسب البطاقة
            let filterType = 'all';
            switch(index) {
                case 0: filterType = 'all'; break;      // إجمالي الاجتماعات
                case 1: filterType = 'active'; break;   // اجتماعات نشطة
                case 2: filterType = 'postponed'; break; // اجتماعات مؤجلة
                case 3: filterType = 'cancelled'; break; // اجتماعات ملغية
            }

            // تطبيق الفلتر
            applyFilter(filterType);

            // تحديث مظهر البطاقات
            updateStatsCardsAppearance(index);

            // تحديث أزرار الفلترة
            updateFilterTabsAppearance(filterType);

            // إظهار إشعار
            showSimpleNotification(`تم تطبيق فلتر: ${getFilterName(filterType)}`);
        });

        // إضافة تأثيرات التمرير
        card.addEventListener('mouseenter', function() {
            if (!card.classList.contains('selected')) {
                card.style.transform = 'translateY(-5px) scale(1.02)';
                card.style.boxShadow = '0 8px 25px rgba(0,0,0,0.2)';
            }
        });

        card.addEventListener('mouseleave', function() {
            if (!card.classList.contains('selected')) {
                card.style.transform = 'translateY(0) scale(1)';
                card.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
            }
        });
    });

    console.log('✅ تم تهيئة البطاقات الإحصائية');
}

// تهيئة أزرار الفلترة
function initializeFilterTabs() {
    console.log('🔍 تهيئة أزرار الفلترة...');

    const filterTabs = document.querySelectorAll('.filter-tab');

    filterTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const filterType = tab.getAttribute('data-filter');
            console.log(`تم النقر على زر الفلترة: ${filterType}`);

            // تطبيق الفلتر
            applyFilter(filterType);

            // تحديث مظهر الأزرار
            updateFilterTabsAppearance(filterType);

            // تحديث البطاقات الإحصائية
            updateStatsCardsAppearance(getCardIndexForFilter(filterType));

            // إظهار إشعار
            showSimpleNotification(`تم تطبيق فلتر: ${getFilterName(filterType)}`);
        });

        // إضافة تأثيرات التمرير
        tab.addEventListener('mouseenter', function() {
            if (!tab.classList.contains('active')) {
                tab.style.backgroundColor = 'rgba(27, 67, 50, 0.1)';
                tab.style.transform = 'translateY(-2px)';
            }
        });

        tab.addEventListener('mouseleave', function() {
            if (!tab.classList.contains('active')) {
                tab.style.backgroundColor = 'transparent';
                tab.style.transform = 'translateY(0)';
            }
        });
    });

    console.log('✅ تم تهيئة أزرار الفلترة');
}

// تهيئة البحث
function initializeSearch() {
    console.log('🔍 تهيئة البحث...');

    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            applySearchFilter(searchTerm);
        });
    }

    console.log('✅ تم تهيئة البحث');
}

// تطبيق الفلتر الرئيسي
function applyFilter(filterType) {
    console.log(`🔍 تطبيق فلتر: ${filterType}`);

    currentFilter = filterType;

    allMeetings.forEach(meeting => {
        const meetingStatus = meeting.getAttribute('data-status') || 'active';
        let shouldShow = false;

        if (filterType === 'all') {
            shouldShow = true;
        } else {
            shouldShow = meetingStatus === filterType;
        }

        // إظهار أو إخفاء الاجتماع مع تأثير انتقالي
        if (shouldShow) {
            meeting.style.display = 'block';
            setTimeout(() => {
                meeting.style.opacity = '1';
                meeting.style.transform = 'scale(1)';
            }, 50);
        } else {
            meeting.style.opacity = '0';
            meeting.style.transform = 'scale(0.95)';
            setTimeout(() => {
                meeting.style.display = 'none';
            }, 300);
        }
    });

    // تحديث العداد
    updateMeetingsCounter();
}

// تطبيق فلتر البحث
function applySearchFilter(searchTerm) {
    console.log(`🔍 البحث عن: ${searchTerm}`);

    allMeetings.forEach(meeting => {
        const searchData = meeting.getAttribute('data-search') || '';
        const meetingStatus = meeting.getAttribute('data-status') || 'active';

        let shouldShow = false;

        // فحص البحث النصي
        const matchesSearch = !searchTerm || searchData.toLowerCase().includes(searchTerm);

        // فحص الفلتر الحالي
        const matchesFilter = currentFilter === 'all' || meetingStatus === currentFilter;

        shouldShow = matchesSearch && matchesFilter;

        // إظهار أو إخفاء الاجتماع
        if (shouldShow) {
            meeting.style.display = 'block';
            meeting.style.opacity = '1';
            meeting.style.transform = 'scale(1)';
        } else {
            meeting.style.opacity = '0';
            meeting.style.transform = 'scale(0.95)';
            setTimeout(() => {
                meeting.style.display = 'none';
            }, 300);
        }
    });

    // تحديث العداد
    updateMeetingsCounter();
}

// تهيئة أزرار الفلترة
function initializeFilterTabs() {
    console.log('🔍 تهيئة أزرار الفلترة...');

    const filterTabs = document.querySelectorAll('.filter-tab');
    filterTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            handleFilterTabClick(tab);
        });

        // إضافة تأثيرات بصرية
        tab.addEventListener('mouseenter', function() {
            if (!tab.classList.contains('active')) {
                tab.style.backgroundColor = 'rgba(27, 67, 50, 0.1)';
                tab.style.transform = 'translateY(-2px)';
            }
        });

        tab.addEventListener('mouseleave', function() {
            if (!tab.classList.contains('active')) {
                tab.style.backgroundColor = 'transparent';
                tab.style.transform = 'translateY(0)';
            }
        });
    });

    console.log('✅ تم تهيئة أزرار الفلترة');
}

// تهيئة الفلاتر
function initializeFilters() {
    console.log('🔍 تهيئة الفلاتر...');
    
    // فلتر الحالة
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        populateStatusFilter();
    }
    
    // فلتر النوع
    const typeFilter = document.getElementById('typeFilter');
    if (typeFilter) {
        populateTypeFilter();
    }
    
    console.log('✅ تم تهيئة الفلاتر');
}

// ملء فلتر الحالة
function populateStatusFilter() {
    const statusFilter = document.getElementById('statusFilter');
    const statuses = [...new Set(allMeetingsData.map(meeting => meeting.status))];
    
    // مسح الخيارات الحالية
    statusFilter.innerHTML = '<option value="">جميع الحالات</option>';
    
    // إضافة الحالات
    statuses.forEach(status => {
        if (status) {
            const option = document.createElement('option');
            option.value = status;
            option.textContent = status;
            statusFilter.appendChild(option);
        }
    });
}

// ملء فلتر النوع
function populateTypeFilter() {
    const typeFilter = document.getElementById('typeFilter');
    const types = [...new Set(allMeetingsData.map(meeting => meeting.type))];
    
    // مسح الخيارات الحالية
    typeFilter.innerHTML = '<option value="">جميع الأنواع</option>';
    
    // إضافة الأنواع
    types.forEach(type => {
        if (type) {
            const option = document.createElement('option');
            option.value = type;
            option.textContent = type;
            typeFilter.appendChild(option);
        }
    });
}

// معالجة نقر البطاقات الإحصائية
function handleStatsCardClick(card, index) {
    console.log('📊 نقر بطاقة إحصائية:', index);

    // الحصول على الفلتر من البطاقة
    const filter = card.getAttribute('data-filter') || 'all';

    // إضافة تأثير بصري فوري
    card.style.transform = 'translateY(-10px) scale(1.03)';

    // إزالة التحديد من جميع البطاقات
    document.querySelectorAll('.stats-card').forEach(c => {
        c.classList.remove('selected');
        c.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
        if (c !== card) {
            c.style.transform = 'translateY(0) scale(1)';
        }
    });

    // تحديد البطاقة المنقورة
    card.classList.add('selected');
    card.style.boxShadow = '0 12px 35px rgba(27, 67, 50, 0.4)';

    // تحديث الفلتر الحالي
    currentFilter = filter;

    // تحديث أزرار الفلترة
    updateFilterTabs();

    // تطبيق الفلتر مع تأثير انتقالي
    setTimeout(() => {
        applyFilters();

        // إظهار رسالة تأكيد
        showNotification(`تم تطبيق فلتر: ${getFilterName(currentFilter)}`, 'success');

        // تحديث شكل البطاقة النهائي
        setTimeout(() => {
            card.style.transform = 'translateY(-8px) scale(1.02)';
        }, 200);
    }, 100);
}

// معالجة نقر أزرار الفلترة
function handleFilterTabClick(tab) {
    const filter = tab.getAttribute('data-filter');
    console.log('🔍 نقر زر فلترة:', filter);

    // تحديث الفلتر الحالي
    currentFilter = filter;

    // تحديث أزرار الفلترة
    updateFilterTabs();

    // تحديث البطاقات الإحصائية
    updateStatsCards();

    // تطبيق الفلتر
    applyFilters();

    // إظهار رسالة تأكيد
    showNotification(`تم تطبيق فلتر: ${getFilterName(filter)}`, 'info');
}

// تحديث أزرار الفلترة
function updateFilterTabs() {
    const filterTabs = document.querySelectorAll('.filter-tab');
    filterTabs.forEach(tab => {
        const filter = tab.getAttribute('data-filter');
        if (filter === currentFilter) {
            tab.classList.add('active');
            tab.style.backgroundColor = '';
            tab.style.transform = '';
        } else {
            tab.classList.remove('active');
        }
    });
}

// تحديث البطاقات الإحصائية
function updateStatsCards() {
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        const shouldSelect = (
            (index === 0 && currentFilter === 'all') ||
            (index === 1 && currentFilter === 'active') ||
            (index === 2 && currentFilter === 'postponed') ||
            (index === 3 && currentFilter === 'cancelled')
        );

        if (shouldSelect) {
            card.classList.add('selected');
            card.style.boxShadow = '0 8px 25px rgba(27, 67, 50, 0.3)';
        } else {
            card.classList.remove('selected');
            card.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
        }
    });
}

// معالجة البحث
function handleSearch(event) {
    const searchTerm = event.target.value.toLowerCase();
    console.log('🔍 البحث عن:', searchTerm);

    applyFilters();
}

// معالجة تغيير الفلاتر
function handleFilterChange(event) {
    console.log('🔄 تغيير الفلتر:', event.target.id, event.target.value);

    applyFilters();
}

// تطبيق الفلاتر
function applyFilters() {
    console.log('🔍 تطبيق الفلاتر...');

    const searchTerm = document.getElementById('searchInput')?.value.toLowerCase() || '';

    filteredMeetings = allMeetingsData.filter(meeting => {
        // فلتر البحث النصي
        const matchesSearch = !searchTerm || meeting.searchData.includes(searchTerm);

        // فلتر الحالة
        let matchesStatus = true;
        if (currentFilter !== 'all') {
            matchesStatus = meeting.status === currentFilter;
        }

        return matchesSearch && matchesStatus;
    });

    console.log(`🔍 تم العثور على ${filteredMeetings.length} من ${allMeetingsData.length} اجتماع`);

    // تحديث العرض
    updateMeetingsDisplay();

    // تحديث عداد النتائج
    updateResultsCounter();

    // تحديث الإحصائيات
    updateStatistics();
}

// تحديث عرض الاجتماعات
function updateMeetingsDisplay() {
    console.log('🔄 تحديث عرض الاجتماعات...');

    // إخفاء جميع العناصر
    allMeetingsData.forEach(meeting => {
        meeting.element.style.display = 'none';
        meeting.element.style.opacity = '0';
        meeting.element.style.transform = 'scale(0.95)';
    });

    // إظهار العناصر المفلترة مع تأثير انتقالي
    filteredMeetings.forEach((meeting, index) => {
        setTimeout(() => {
            meeting.element.style.display = 'block';
            meeting.element.style.transition = 'all 0.3s ease';
            setTimeout(() => {
                meeting.element.style.opacity = '1';
                meeting.element.style.transform = 'scale(1)';
            }, 50);
        }, index * 50); // تأخير تدريجي لتأثير أنيق
    });

    // إظهار رسالة إذا لم توجد نتائج
    showNoResultsMessage();
}

// تحديث عداد النتائج
function updateResultsCounter() {
    const counter = document.getElementById('resultsCounter');
    if (counter) {
        counter.textContent = `عرض ${filteredMeetings.length} من ${allMeetingsData.length} اجتماع`;
    }

    // تحديث العنوان الرئيسي
    const pageTitle = document.querySelector('.page-title');
    if (pageTitle) {
        const icon = pageTitle.querySelector('i');
        const iconHtml = icon ? icon.outerHTML : '<i class="fas fa-calendar-alt me-3"></i>';
        pageTitle.innerHTML = `${iconHtml}كل الاجتماعات (${filteredMeetings.length})`;
    }
}

// إظهار رسالة عدم وجود نتائج
function showNoResultsMessage() {
    const container = document.getElementById('meetingsContainer');
    let noResultsDiv = document.getElementById('noResultsMessage');

    if (filteredMeetings.length === 0) {
        if (!noResultsDiv) {
            noResultsDiv = document.createElement('div');
            noResultsDiv.id = 'noResultsMessage';
            noResultsDiv.className = 'col-12';
            noResultsDiv.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-search fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد نتائج</h4>
                    <p class="text-muted">لم يتم العثور على اجتماعات تطابق معايير البحث</p>
                    <button class="btn btn-outline-primary" onclick="clearAllFilters()">
                        <i class="fas fa-refresh me-2"></i>مسح جميع الفلاتر
                    </button>
                </div>
            `;
            container.appendChild(noResultsDiv);
        }
        noResultsDiv.style.display = 'block';
    } else {
        if (noResultsDiv) {
            noResultsDiv.style.display = 'none';
        }
    }
}

// معالجة نقرات الأزرار
function handleActionClick(event) {
    const button = event.target.closest('button');
    const action = button.getAttribute('onclick');
    const meetingId = button.getAttribute('data-meeting-id');
    
    console.log('🔘 نقر زر:', action, 'للاجتماع:', meetingId);
}

// مسح الفلاتر
function clearFilters() {
    console.log('🧹 مسح جميع الفلاتر...');
    
    // مسح البحث
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.value = '';
    }
    
    // مسح الفلاتر
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        statusFilter.value = '';
    }
    
    const typeFilter = document.getElementById('typeFilter');
    if (typeFilter) {
        typeFilter.value = '';
    }
    
    // تطبيق الفلاتر المحدثة
    applyFilters();
    
    console.log('✅ تم مسح جميع الفلاتر');
}

// تحديث البيانات
function refreshMeetings() {
    console.log('🔄 تحديث بيانات الاجتماعات...');
    
    // إعادة تحميل الصفحة
    location.reload();
}

// تصدير الاجتماعات
function exportMeetings(format) {
    console.log('📤 تصدير الاجتماعات بصيغة:', format);
    
    if (format === 'pdf') {
        // تصدير PDF
        window.open('/export_report_pdf', '_blank');
    } else if (format === 'excel') {
        // تصدير Excel
        alert('ميزة تصدير Excel قيد التطوير');
    }
}

// طباعة الاجتماعات
function printMeetings() {
    console.log('🖨️ طباعة الاجتماعات...');
    
    window.print();
}

// تهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 تم تحميل صفحة جميع الاجتماعات');

    // تأخير قصير لضمان تحميل جميع العناصر
    setTimeout(() => {
        initializeAllMeetings();
    }, 100);
});

// إظهار مؤشر التحميل
function showLoadingIndicator() {
    const indicator = document.createElement('div');
    indicator.id = 'loadingIndicator';
    indicator.className = 'position-fixed top-50 start-50 translate-middle';
    indicator.style.zIndex = '9999';
    indicator.innerHTML = `
        <div class="text-center">
            <div class="loading-spinner mb-3"></div>
            <p class="text-muted">جاري تحميل الاجتماعات...</p>
        </div>
    `;
    document.body.appendChild(indicator);
}

// إخفاء مؤشر التحميل
function hideLoadingIndicator() {
    const indicator = document.getElementById('loadingIndicator');
    if (indicator) {
        indicator.style.opacity = '0';
        indicator.style.transition = 'opacity 0.3s ease';
        setTimeout(() => indicator.remove(), 300);
    }
}

// إضافة تأثيرات الدخول
function addEntranceAnimations() {
    // تأثير دخول للبطاقات الإحصائية
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // تأثير دخول لشريط البحث
    const searchBox = document.querySelector('.search-box');
    if (searchBox) {
        searchBox.style.opacity = '0';
        searchBox.style.transform = 'translateY(20px)';
        setTimeout(() => {
            searchBox.style.transition = 'all 0.5s ease';
            searchBox.style.opacity = '1';
            searchBox.style.transform = 'translateY(0)';
        }, 400);
    }

    // تأثير دخول للاجتماعات
    const meetingItems = document.querySelectorAll('.meeting-item');
    meetingItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        setTimeout(() => {
            item.style.transition = 'all 0.4s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, 600 + (index * 50));
    });
}

// وظائف مساعدة إضافية

// الحصول على اسم الفلتر
function getFilterName(filter) {
    const filterNames = {
        'all': 'جميع الاجتماعات',
        'active': 'الاجتماعات النشطة',
        'postponed': 'الاجتماعات المؤجلة',
        'cancelled': 'الاجتماعات الملغية'
    };
    return filterNames[filter] || 'غير محدد';
}

// إظهار الإشعارات - استخدام النظام الموحد
function showNotification(message, type = 'info') {
    if (window.UnifiedNotifications) {
        // تحويل أنواع الإشعارات للنظام الموحد
        const unifiedType = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
        window.UnifiedNotifications.showNotification(message, unifiedType);
    } else {
        // fallback للنظام القديم
        console.log('إشعار:', message);
        alert(message);
    }
}

// تحديث الإحصائيات
function updateStatistics() {
    const stats = {
        total: allMeetingsData.length,
        active: allMeetingsData.filter(m => m.status === 'active').length,
        postponed: allMeetingsData.filter(m => m.status === 'postponed').length,
        cancelled: allMeetingsData.filter(m => m.status === 'cancelled').length
    };

    // تحديث أرقام البطاقات
    const statsCards = document.querySelectorAll('.stats-card');
    if (statsCards.length >= 4) {
        statsCards[0].querySelector('.card-title').textContent = stats.total;
        statsCards[1].querySelector('.card-title').textContent = stats.active;
        statsCards[2].querySelector('.card-title').textContent = stats.postponed;
        statsCards[3].querySelector('.card-title').textContent = stats.cancelled;
    }
}

// مسح جميع الفلاتر
function clearAllFilters() {
    console.log('🧹 مسح جميع الفلاتر...');

    // مسح البحث
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.value = '';
    }

    // إعادة تعيين الفلتر
    currentFilter = 'all';

    // تحديث الواجهة
    updateFilterTabs();
    updateStatsCards();

    // تطبيق الفلاتر
    applyFilters();

    // إظهار رسالة تأكيد
    showNotification('تم مسح جميع الفلاتر', 'success');
}

// إضافة أنيميشن CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInLeft {
        from { transform: translateX(-100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOutLeft {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(-100%); opacity: 0; }
    }

    .stats-card.selected {
        transform: translateY(-5px) scale(1.02) !important;
        box-shadow: 0 8px 25px rgba(27, 67, 50, 0.3) !important;
    }

    .filter-tab {
        transition: all 0.3s ease;
    }

    .meeting-item {
        transition: all 0.3s ease;
    }
`;
document.head.appendChild(style);

// تصدير الوظائف للاستخدام العام
window.clearFilters = clearFilters;
window.clearAllFilters = clearAllFilters;
window.refreshMeetings = refreshMeetings;
window.exportMeetings = exportMeetings;
window.printMeetings = printMeetings;
window.showNotification = showNotification;

// تحديث مظهر البطاقات الإحصائية
function updateStatsCardsAppearance(selectedIndex) {
    const statsCards = document.querySelectorAll('.stats-card');

    statsCards.forEach((card, index) => {
        if (index === selectedIndex) {
            card.classList.add('selected');
            card.style.transform = 'translateY(-8px) scale(1.02)';
            card.style.boxShadow = '0 12px 35px rgba(27, 67, 50, 0.4)';
        } else {
            card.classList.remove('selected');
            card.style.transform = 'translateY(0) scale(1)';
            card.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
        }
    });
}

// تحديث مظهر أزرار الفلترة
function updateFilterTabsAppearance(activeFilter) {
    const filterTabs = document.querySelectorAll('.filter-tab');

    filterTabs.forEach(tab => {
        const tabFilter = tab.getAttribute('data-filter');
        if (tabFilter === activeFilter) {
            tab.classList.add('active');
            tab.style.backgroundColor = '';
            tab.style.transform = '';
        } else {
            tab.classList.remove('active');
            tab.style.backgroundColor = 'transparent';
            tab.style.transform = 'translateY(0)';
        }
    });
}

// الحصول على فهرس البطاقة للفلتر
function getCardIndexForFilter(filterType) {
    const filterMap = {
        'all': 0,
        'active': 1,
        'postponed': 2,
        'cancelled': 3
    };
    return filterMap[filterType] || 0;
}

// الحصول على اسم الفلتر
function getFilterName(filterType) {
    const filterNames = {
        'all': 'جميع الاجتماعات',
        'active': 'الاجتماعات النشطة',
        'postponed': 'الاجتماعات المؤجلة',
        'cancelled': 'الاجتماعات الملغية'
    };
    return filterNames[filterType] || 'غير محدد';
}

// تحديث عداد الاجتماعات
function updateMeetingsCounter() {
    const visibleMeetings = Array.from(allMeetings).filter(meeting =>
        meeting.style.display !== 'none' && meeting.style.opacity !== '0'
    );

    // تحديث العنوان الرئيسي
    const pageTitle = document.querySelector('.page-title');
    if (pageTitle) {
        const icon = pageTitle.querySelector('i');
        const iconHtml = icon ? icon.outerHTML : '<i class="fas fa-calendar-alt me-3"></i>';
        pageTitle.innerHTML = `${iconHtml}كل الاجتماعات (${visibleMeetings.length})`;
    }

    console.log(`عرض ${visibleMeetings.length} من ${allMeetings.length} اجتماع`);
}

// إظهار إشعار بسيط - استخدام النظام الموحد
function showSimpleNotification(message) {
    if (window.UnifiedNotifications) {
        window.UnifiedNotifications.showSuccess(message);
    } else {
        console.log('إشعار بسيط:', message);
        alert(message);
    }
}

// إضافة أنيميشن CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInLeft {
        from { transform: translateX(-100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOutLeft {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(-100%); opacity: 0; }
    }

    .meeting-item {
        transition: all 0.3s ease;
    }
`;
document.head.appendChild(style);

console.log('📅 تم تحميل جافا سكريبت صفحة جميع الاجتماعات - النسخة المبسطة والفعالة');
