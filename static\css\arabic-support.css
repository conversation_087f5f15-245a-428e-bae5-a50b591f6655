/**
 * دعم اللغة العربية - نظام إدارة الاجتماعات
 * Arabic Language Support for JAF Meeting System
 */

/* إعدادات عامة للغة العربية */
body[dir="rtl"] {
    font-family: 'Segoe UI', '<PERSON><PERSON><PERSON>', 'Aria<PERSON>', sans-serif;
    text-align: right;
}

/* تحسين عرض النصوص العربية */
.arabic-text {
    font-family: 'Segoe UI', 'Ta<PERSON><PERSON>', 'Arial', sans-serif;
    direction: rtl;
    text-align: right;
    line-height: 1.6;
}

/* تحسين عرض التواريخ العربية */
.arabic-date {
    direction: ltr;
    text-align: center;
    font-family: 'Segoe UI', monospace;
    font-weight: bold;
}

/* تحسين عرض الأرقام العربية */
.arabic-numbers {
    font-family: 'Traditional Arabic', 'Segoe UI', sans-serif;
    font-size: 1.1em;
}

/* تحسين الجداول للغة العربية */
.table-rtl {
    direction: rtl;
    text-align: right;
}

.table-rtl th,
.table-rtl td {
    text-align: right;
    padding: 12px 8px;
}

.table-rtl .text-center {
    text-align: center !important;
}

/* تحسين النماذج للغة العربية */
.form-rtl {
    direction: rtl;
    text-align: right;
}

.form-rtl .form-label {
    text-align: right;
    margin-bottom: 8px;
    font-weight: 600;
}

.form-rtl .form-control {
    text-align: right;
    direction: rtl;
}

.form-rtl .form-control[type="date"],
.form-rtl .form-control[type="time"],
.form-rtl .form-control[type="number"] {
    direction: ltr;
    text-align: center;
}

/* تحسين الأزرار للغة العربية */
.btn-rtl {
    direction: rtl;
    text-align: center;
}

.btn-rtl i {
    margin-left: 8px;
    margin-right: 0;
}

/* تحسين التنبيهات للغة العربية */
.alert-rtl {
    direction: rtl;
    text-align: right;
}

.alert-rtl .alert-heading {
    text-align: right;
}

/* تحسين البطاقات للغة العربية */
.card-rtl {
    direction: rtl;
    text-align: right;
}

.card-rtl .card-header {
    text-align: right;
    font-weight: bold;
}

.card-rtl .card-body {
    text-align: right;
}

/* تحسين القوائم المنسدلة */
.dropdown-menu-rtl {
    direction: rtl;
    text-align: right;
}

.dropdown-menu-rtl .dropdown-item {
    text-align: right;
    padding: 8px 16px;
}

/* تحسين شريط التنقل */
.navbar-rtl {
    direction: rtl;
}

.navbar-rtl .navbar-nav {
    direction: rtl;
}

.navbar-rtl .nav-link {
    text-align: right;
}

/* تحسين الإحصائيات */
.stats-rtl {
    direction: rtl;
    text-align: center;
}

.stats-rtl .stats-number {
    font-family: 'Segoe UI', monospace;
    font-size: 2.5rem;
    font-weight: bold;
    color: #1B4332;
}

.stats-rtl .stats-label {
    font-size: 0.9rem;
    color: #666;
    margin-top: 8px;
}

/* تحسين البحث */
.search-rtl {
    direction: rtl;
    text-align: right;
}

.search-rtl .form-control {
    text-align: right;
    direction: rtl;
}

.search-rtl .btn {
    margin-right: 8px;
    margin-left: 0;
}

/* تحسين التقارير */
.report-rtl {
    direction: rtl;
    text-align: right;
}

.report-rtl .table {
    direction: rtl;
}

.report-rtl .chart-container {
    direction: ltr;
    text-align: center;
}

/* تحسين الطباعة */
@media print {
    .arabic-text {
        font-family: 'Traditional Arabic', 'Segoe UI', sans-serif;
        font-size: 12pt;
        line-height: 1.5;
    }
    
    .arabic-date {
        font-family: 'Segoe UI', monospace;
        font-size: 11pt;
    }
    
    .table-rtl {
        font-size: 10pt;
    }
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .arabic-text {
        font-size: 14px;
        line-height: 1.5;
    }
    
    .table-rtl {
        font-size: 12px;
    }
    
    .stats-rtl .stats-number {
        font-size: 2rem;
    }
    
    .form-rtl .form-control {
        font-size: 16px; /* منع التكبير في iOS */
    }
}

/* تحسين إمكانية الوصول */
.sr-only-ar {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* تحسين التركيز للوحة المفاتيح */
.form-control:focus,
.btn:focus {
    outline: 2px solid #1B4332;
    outline-offset: 2px;
}

/* تحسين الألوان للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .arabic-text {
        color: #f8f9fa;
    }
    
    .stats-rtl .stats-number {
        color: #4CAF50;
    }
    
    .stats-rtl .stats-label {
        color: #adb5bd;
    }
}
