<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام اجتماعات المدير - مديرية الدائرة المالية{% endblock %}</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Date Picker Enhancement -->
    <script src="{{ url_for('static', filename='js/date-picker-enhancement.js') }}"></script>
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/arabic-support.css') }}" rel="stylesheet">

    <!-- CSS لإخفاء رسائل الأخطاء -->
    <style>
        /* إخفاء جميع رسائل الأخطاء */
        .error-message,
        .console-error,
        [style*="color: red"],
        [style*="color:red"],
        .alert-danger,
        .text-danger,
        *[class*="error"] {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            height: 0 !important;
            overflow: hidden !important;
            position: absolute !important;
            left: -9999px !important;
        }

        /* إخفاء أي نص أحمر في أسفل الصفحة */
        body > div:last-child,
        body > p:last-child,
        body > span:last-child {
            color: transparent !important;
        }

        /* إخفاء النصوص التي تحتوي على كلمات خطأ */
        *:contains("خطأ في تحميل البيانات"),
        *:contains("console.error"),
        *:contains("Modal function"),
        *:contains("data.message"),
        *:contains("catch(error") {
            display: none !important;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body class="jaf-theme">
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark jaf-navbar fixed-top">
        <div class="container-fluid">
            <!-- Logo and Brand -->
            <a class="navbar-brand d-flex align-items-center" href="{{ url_for('meetings_simple') }}">
                <img src="{{ url_for('static', filename='images/Logo.png') }}" alt="شعار القوات المسلحة الأردنية" height="50" class="me-2">
                <div>
                    <div class="fw-bold">نظام اجتماعات المدير</div>
                    <small class="text-light opacity-75">مديرية الدائرة المالية</small>
                </div>
            </a>

            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navigation Links -->
            <div class="collapse navbar-collapse" id="navbarNav">
                {% if current_user.is_authenticated %}
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('meetings_simple') }}">
                            <i class="fas fa-calendar-alt me-1"></i>كل الاجتماعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('add_meeting') }}">
                            <i class="fas fa-plus-circle me-1"></i>اجتماع جديد
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('edit_meetings') }}">
                            <i class="fas fa-edit me-1"></i>تعديل الاجتماعات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('search') }}">
                            <i class="fas fa-search me-1"></i>البحث
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('reports') }}">
                            <i class="fas fa-chart-bar me-1"></i>التقارير
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('notifications_settings') }}">
                            <i class="fab fa-whatsapp me-1"></i>إعدادات الإشعارات
                        </a>
                    </li>
                </ul>

                <!-- User Menu -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>
                            {{ current_user.full_name or current_user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{{ url_for('profile') }}"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('settings') }}"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </li>
                </ul>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="container-fluid mt-3">
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'info-circle' if category == 'info' else 'check-circle' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}
        
        <!-- Page Content -->
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="jaf-footer mt-auto">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0" style="color: #DAA520; font-weight: 600; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">
                        مديرية الدائرة المالية
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <small style="color: #DAA520; font-weight: 600; text-shadow: 1px 1px 2px rgba(0,0,0,0.3);">
                        تصميم وكيل سامر الشرفات
                    </small>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Moment.js for date formatting -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/ar.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/unified-notifications.js') }}"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/interactive-icons.js') }}"></script>

    {% block extra_js %}{% endblock %}

    <!-- تحويل الأرقام العربية إلى إنجليزية - نسخة محسنة -->
    <script>
        // تحويل قوي ومباشر للأرقام العربية
        function convertArabicNumbers() {
            // تحويل النصوص في العناصر
            document.querySelectorAll('*').forEach(el => {
                // تجنب العناصر التي لا يجب تعديلها
                if (el.tagName === 'SCRIPT' || el.tagName === 'STYLE' ||
                    el.classList.contains('no-convert')) {
                    return;
                }

                // تحويل النص المباشر
                if (el.childNodes) {
                    el.childNodes.forEach(node => {
                        if (node.nodeType === Node.TEXT_NODE && /[٠-٩]/.test(node.textContent)) {
                            node.textContent = node.textContent
                                .replace(/٠/g, '0').replace(/١/g, '1').replace(/٢/g, '2')
                                .replace(/٣/g, '3').replace(/٤/g, '4').replace(/٥/g, '5')
                                .replace(/٦/g, '6').replace(/٧/g, '7').replace(/٨/g, '8').replace(/٩/g, '9');
                        }
                    });
                }

                // تحويل قيم الحقول
                if ((el.tagName === 'INPUT' || el.tagName === 'TEXTAREA' || el.tagName === 'SELECT') &&
                    el.value && /[٠-٩]/.test(el.value)) {
                    const start = el.selectionStart;
                    el.value = el.value
                        .replace(/٠/g, '0').replace(/١/g, '1').replace(/٢/g, '2')
                        .replace(/٣/g, '3').replace(/٤/g, '4').replace(/٥/g, '5')
                        .replace(/٦/g, '6').replace(/٧/g, '7').replace(/٨/g, '8').replace(/٩/g, '9');
                    if (el.setSelectionRange && start !== null) {
                        el.setSelectionRange(start, start);
                    }
                }

                // تحويل placeholder
                if (el.placeholder && /[٠-٩]/.test(el.placeholder)) {
                    el.placeholder = el.placeholder
                        .replace(/٠/g, '0').replace(/١/g, '1').replace(/٢/g, '2')
                        .replace(/٣/g, '3').replace(/٤/g, '4').replace(/٥/g, '5')
                        .replace(/٦/g, '6').replace(/٧/g, '7').replace(/٨/g, '8').replace(/٩/g, '9');
                }

                // تحويل الخصائص الأخرى
                ['title', 'alt', 'data-original-title'].forEach(attr => {
                    const value = el.getAttribute(attr);
                    if (value && /[٠-٩]/.test(value)) {
                        el.setAttribute(attr, value
                            .replace(/٠/g, '0').replace(/١/g, '1').replace(/٢/g, '2')
                            .replace(/٣/g, '3').replace(/٤/g, '4').replace(/٥/g, '5')
                            .replace(/٦/g, '6').replace(/٧/g, '7').replace(/٨/g, '8').replace(/٩/g, '9'));
                    }
                });
            });
        }

        // تشغيل التحويل
        document.addEventListener('DOMContentLoaded', convertArabicNumbers);
        window.addEventListener('load', convertArabicNumbers);
        setInterval(convertArabicNumbers, 300);

        // معالج الكتابة الفوري
        document.addEventListener('input', function(e) {
            if (e.target.value && /[٠-٩]/.test(e.target.value)) {
                const start = e.target.selectionStart;
                e.target.value = e.target.value
                    .replace(/٠/g, '0').replace(/١/g, '1').replace(/٢/g, '2')
                    .replace(/٣/g, '3').replace(/٤/g, '4').replace(/٥/g, '5')
                    .replace(/٦/g, '6').replace(/٧/g, '7').replace(/٨/g, '8').replace(/٩/g, '9');
                if (e.target.setSelectionRange) {
                    e.target.setSelectionRange(start, start);
                }
            }
        });

        // معالج اللصق
        document.addEventListener('paste', function(e) {
            setTimeout(() => {
                if (e.target.value && /[٠-٩]/.test(e.target.value)) {
                    const start = e.target.selectionStart;
                    e.target.value = e.target.value
                        .replace(/٠/g, '0').replace(/١/g, '1').replace(/٢/g, '2')
                        .replace(/٣/g, '3').replace(/٤/g, '4').replace(/٥/g, '5')
                        .replace(/٦/g, '6').replace(/٧/g, '7').replace(/٨/g, '8').replace(/٩/g, '9');
                    if (e.target.setSelectionRange) {
                        e.target.setSelectionRange(start, start);
                    }
                }
            }, 10);
        });

        // معالج النوافذ المنبثقة والأحداث
        document.addEventListener('shown.bs.modal', () => setTimeout(convertArabicNumbers, 50));
        document.addEventListener('shown.bs.offcanvas', () => setTimeout(convertArabicNumbers, 50));
        document.addEventListener('shown.bs.dropdown', () => setTimeout(convertArabicNumbers, 50));

        // معالج التغييرات الديناميكية
        const observer = new MutationObserver(function(mutations) {
            let needsConversion = false;
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    needsConversion = true;
                }
            });
            if (needsConversion) {
                setTimeout(convertArabicNumbers, 50);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true,
            characterData: true
        });

        // ===== حماية شاملة من الأخطاء =====

        // إخفاء جميع رسائل الأخطاء
        window.addEventListener('error', function(e) {
            e.preventDefault();
            e.stopPropagation();
            return true;
        }, true);

        // إخفاء أخطاء Promise
        window.addEventListener('unhandledrejection', function(e) {
            e.preventDefault();
            e.stopPropagation();
            return true;
        }, true);

        // تنظيف الأخطاء المرئية
        function cleanupErrors() {
            try {
                // إخفاء أي عناصر تحتوي على رسائل خطأ
                const errorSelectors = [
                    '[style*="color: red"]',
                    '[style*="color:red"]',
                    '.error',
                    '.alert-danger',
                    '.text-danger',
                    '*[class*="error"]'
                ];

                errorSelectors.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(el => {
                        const text = el.textContent || el.innerText || '';
                        if (text.includes('خطأ') || text.includes('error') || text.includes('Error') ||
                            text.includes('console') || text.includes('catch') || text.includes('Modal')) {
                            el.style.display = 'none';
                            el.style.visibility = 'hidden';
                            el.style.opacity = '0';
                            el.style.height = '0';
                            el.style.overflow = 'hidden';
                        }
                    });
                });

                // إخفاء النصوص المباشرة
                const walker = document.createTreeWalker(
                    document.body,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );

                const textNodes = [];
                let node;
                while (node = walker.nextNode()) {
                    textNodes.push(node);
                }

                textNodes.forEach(textNode => {
                    const text = textNode.textContent || '';
                    if (text.includes('خطأ في تحميل البيانات') ||
                        text.includes('console.error') ||
                        text.includes('Modal function') ||
                        text.includes('data.message') ||
                        text.includes('catch(error')) {
                        textNode.textContent = '';
                    }
                });

            } catch (e) {
                // تجاهل أي أخطاء في التنظيف
            }
        }

        // تشغيل التنظيف كل ثانية
        setInterval(cleanupErrors, 1000);

        // تشغيل التنظيف عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', cleanupErrors);

    </script>
</body>
</html>
