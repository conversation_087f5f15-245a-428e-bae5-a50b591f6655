// -*- coding: utf-8 -*-
/**
 * JavaScript لصفحة التقارير والإحصائيات
 * نظام إدارة المواعيد - القوات المسلحة الأردنية
 */

let currentChart = null;
let currentChartType = 'monthly';

// أدوات الإشعارات - استخدام النظام الموحد
const NotificationUtils = {
    showSuccess: function(message) {
        if (window.UnifiedNotifications) {
            window.UnifiedNotifications.showSuccess(message);
        } else {
            console.log('نجاح:', message);
            alert(message);
        }
    },

    showError: function(message) {
        if (window.UnifiedNotifications) {
            window.UnifiedNotifications.showError(message);
        } else {
            console.log('خطأ:', message);
            alert(message);
        }
    },

    showWarning: function(message) {
        if (window.UnifiedNotifications) {
            window.UnifiedNotifications.showWarning(message);
        } else {
            console.log('تحذير:', message);
            alert(message);
        }
    },

    showInfo: function(message) {
        if (window.UnifiedNotifications) {
            window.UnifiedNotifications.showInfo(message);
        } else {
            console.log('معلومات:', message);
            alert(message);
        }
    },

    showNotification: function(message, type) {
        if (window.UnifiedNotifications) {
            window.UnifiedNotifications.showNotification(message, type);
        } else {
            console.log('إشعار:', message, type);
            alert(message);
        }
    }
};

document.addEventListener('DOMContentLoaded', function() {
    console.log('تحميل صفحة التقارير...');
    try {
        initializePage();
    } catch (error) {
        console.error('خطأ في تهيئة الصفحة:', error);
        NotificationUtils.showError('حدث خطأ في تحميل الصفحة');
    }
});

function initializePage() {
    console.log('تهيئة صفحة التقارير...');
    setupDateFilters();
    loadInitialChart();
    setupPeriodFilter();

    // تحديث الإحصائيات عند التحميل - معطل لتجنب التضارب مع reports-fixed.js
    // setTimeout(() => {
    //     updateStatistics();
    // }, 1000);
}

// إعداد فلاتر التاريخ
function setupDateFilters() {
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    
    document.getElementById('startDate').value = firstDayOfMonth.toISOString().split('T')[0];
    document.getElementById('endDate').value = today.toISOString().split('T')[0];
}

// إعداد فلتر الفترة
function setupPeriodFilter() {
    const periodFilter = document.getElementById('periodFilter');
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    
    periodFilter.addEventListener('change', function() {
        const period = this.value;
        const today = new Date();
        let start, end;
        
        switch (period) {
            case 'month':
                start = new Date(today.getFullYear(), today.getMonth(), 1);
                end = today;
                break;
            case 'quarter':
                const quarter = Math.floor(today.getMonth() / 3);
                start = new Date(today.getFullYear(), quarter * 3, 1);
                end = today;
                break;
            case 'year':
                start = new Date(today.getFullYear(), 0, 1);
                end = today;
                break;
            case 'custom':
                // المستخدم سيحدد التواريخ يدوياً
                return;
        }
        
        if (start && end) {
            startDate.value = start.toISOString().split('T')[0];
            endDate.value = end.toISOString().split('T')[0];
            updateStatistics();
        }
    });
}

// تحميل الرسم البياني الأولي
function loadInitialChart() {
    switchChart('monthly');
}

// تبديل نوع الرسم البياني
function switchChart(chartType) {
    // تحديث التبويبات
    document.querySelectorAll('.chart-tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // البحث عن التبويب الصحيح وتفعيله
    const activeTab = document.querySelector(`[onclick="switchChart('${chartType}')"]`);
    if (activeTab) {
        activeTab.classList.add('active');
    }
    
    currentChartType = chartType;
    
    // تدمير الرسم البياني الحالي
    if (currentChart) {
        currentChart.destroy();
    }
    
    // إنشاء رسم بياني جديد
    createChart(chartType);
}

// إنشاء الرسم البياني
function createChart(type) {
    const ctx = document.getElementById('mainChart').getContext('2d');
    
    switch (type) {
        case 'monthly':
            createMonthlyChart(ctx);
            break;
        case 'types':
            createTypesChart(ctx);
            break;
        case 'status':
            createStatusChart(ctx);
            break;
        case 'departments':
            createDepartmentsChart(ctx);
            break;
    }
}

// رسم بياني للاتجاه الشهري
function createMonthlyChart(ctx) {
    // جلب البيانات الحقيقية من الخادم
    fetchMonthlyData().then(monthlyData => {
        const data = {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
            datasets: [{
                label: 'عدد الاجتماعات',
                data: monthlyData || [12, 19, 15, 25, 22, 18, 28, 24, 20, 16, 14, 10],
                borderColor: '#1B4332',
                backgroundColor: 'rgba(27, 67, 50, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        };
    
    currentChart = new Chart(ctx, {
        type: 'line',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'الاتجاه الشهري للاجتماعات',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                }
            }
        }
    });
}

// رسم بياني لأنواع الفعاليات
function createTypesChart(ctx) {
    const data = {
        labels: ['اجتماع', 'دعوة', 'زيارة', 'إيجاز', 'تمرين', 'اتصال مرئي'],
        datasets: [{
            data: [35, 25, 15, 12, 8, 5],
            backgroundColor: [
                '#1B4332',
                '#2D6A4F',
                '#40916C',
                '#52B788',
                '#F77F00',
                '#FFD60A'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    };
    
    currentChart = new Chart(ctx, {
        type: 'doughnut',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'توزيع أنواع الفعاليات',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                legend: {
                    display: true,
                    position: 'right'
                }
            }
        }
    });
}

// رسم بياني لحالة الاجتماعات
function createStatusChart(ctx) {
    const data = {
        labels: ['مكتملة', 'قادمة', 'ملغية', 'مؤجلة'],
        datasets: [{
            data: [65, 25, 7, 3],
            backgroundColor: [
                '#40916C',
                '#52B788',
                '#D62828',
                '#F77F00'
            ],
            borderWidth: 2,
            borderColor: '#fff'
        }]
    };
    
    currentChart = new Chart(ctx, {
        type: 'pie',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'حالة الاجتماعات',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                legend: {
                    display: true,
                    position: 'bottom'
                }
            }
        }
    });
}

// رسم بياني للجهات الداعية
function createDepartmentsChart(ctx) {
    const data = {
        labels: ['وزارة الدفاع', 'الأمن العام', 'الدرك', 'المخابرات', 'الحرس الملكي', 'أخرى'],
        datasets: [{
            label: 'عدد الاجتماعات',
            data: [45, 32, 28, 22, 18, 15],
            backgroundColor: [
                '#1B4332',
                '#2D6A4F',
                '#40916C',
                '#52B788',
                '#F77F00',
                '#FFD60A'
            ],
            borderWidth: 1,
            borderColor: '#fff'
        }]
    };
    
    currentChart = new Chart(ctx, {
        type: 'bar',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'الاجتماعات حسب الجهة الداعية',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                }
            }
        }
    });
}

// تحديث الإحصائيات
function updateStatistics() {
    console.log('بدء تحديث الإحصائيات...');

    const startDate = document.getElementById('startDate')?.value || '';
    const endDate = document.getElementById('endDate')?.value || '';

    console.log('التواريخ:', { startDate, endDate });

    // السماح بالتحديث حتى بدون تواريخ (لعرض جميع البيانات)
    if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
        NotificationUtils.showError('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
        return;
    }
    
    // إظهار مؤشر التحميل
    showLoadingIndicator();
    
    fetch('/api/reports/statistics', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            start_date: startDate,
            end_date: endDate
        })
    })
    .then(response => {
        console.log('استجابة الخادم:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('بيانات الاستجابة:', data);
        hideLoadingIndicator();

        if (data.success) {
            updateStatisticsDisplay(data.statistics);
            createChart(currentChartType);
            NotificationUtils.showSuccess('تم تحديث الإحصائيات بنجاح');
        } else {
            NotificationUtils.showError(data.message || 'فشل في تحديث الإحصائيات');
        }
    })
    .catch(error => {
        console.error('خطأ في تحديث الإحصائيات:', error);
        hideLoadingIndicator();
        NotificationUtils.showError('حدث خطأ في الاتصال بالخادم: ' + error.message);
    });
}

// تحديث عرض الإحصائيات
function updateStatisticsDisplay(stats) {
    console.log('تحديث عرض الإحصائيات:', stats);

    // تحديث البطاقات مع التحقق من وجود العناصر
    const elements = {
        'totalMeetings': stats.total_meetings || 0,
        'upcomingMeetings': stats.upcoming_meetings || 0,
        'completedMeetings': stats.completed_meetings || 0,
        'cancelledMeetings': stats.cancelled_meetings || 0,
        'averagePerMonth': stats.avg_per_month || 0,
        'mostActiveMonth': stats.most_active_month || 'غير محدد'
    };

    Object.keys(elements).forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            // تأثير الانتقال
            element.style.transform = 'scale(0.8)';
            element.style.opacity = '0.5';

            setTimeout(() => {
                element.textContent = elements[elementId];
                element.style.transform = 'scale(1)';
                element.style.opacity = '1';
                element.style.transition = 'all 0.3s ease';
            }, 200);
        } else {
            console.warn(`العنصر غير موجود: ${elementId}`);
        }
    });
}

// تصدير التقارير
function exportReport(format) {
    console.log(`بدء تصدير التقرير بصيغة: ${format}`);

    const startDate = document.getElementById('startDate')?.value || '';
    const endDate = document.getElementById('endDate')?.value || '';

    console.log('بيانات التصدير:', { format, startDate, endDate });

    // التحقق من صحة الصيغة
    const validFormats = ['excel', 'pdf', 'word'];
    if (!validFormats.includes(format)) {
        NotificationUtils.showError('صيغة التصدير غير مدعومة');
        return;
    }

    // إظهار مؤشر التحميل
    showExportLoading(format);

    // معالجة خاصة لتصدير PDF
    if (format === 'pdf') {
        try {
            // فتح رابط تصدير PDF في نافذة جديدة
            window.open('/export_report_pdf', '_blank');

            setTimeout(() => {
                hideExportLoading(format);
                NotificationUtils.showSuccess('تم بدء تحميل التقرير الشامل بصيغة PDF');
            }, 1000);
        } catch (error) {
            hideExportLoading(format);
            console.error('خطأ في تصدير PDF:', error);
            NotificationUtils.showError('فشل في تصدير التقرير: ' + error.message);
        }
        return;
    }

    // إنشاء رابط التحميل المباشر للصيغ الأخرى
    let downloadUrl = `/api/reports/export/${format}`;

    // إضافة معاملات التاريخ إذا كانت متوفرة
    const params = new URLSearchParams();
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);

    if (params.toString()) {
        downloadUrl += '?' + params.toString();
    }

    console.log('رابط التحميل:', downloadUrl);

    // إنشاء عنصر رابط مخفي للتحميل
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `meetings_report_${new Date().toISOString().split('T')[0]}.${getFileExtension(format)}`;
    link.style.display = 'none';
    document.body.appendChild(link);

    try {
        link.click();

        setTimeout(() => {
            document.body.removeChild(link);
            hideExportLoading(format);
            NotificationUtils.showSuccess(`تم بدء تحميل التقرير بصيغة ${format.toUpperCase()}`);
        }, 1000);

    } catch (error) {
        document.body.removeChild(link);
        hideExportLoading(format);
        console.error('خطأ في التصدير:', error);
        NotificationUtils.showError('فشل في تصدير التقرير: ' + error.message);
    }
}

// الحصول على امتداد الملف
function getFileExtension(format) {
    const extensions = {
        'excel': 'csv',
        'pdf': 'html',
        'word': 'rtf'
    };
    return extensions[format] || 'txt';
}

// إظهار مؤشر تحميل التصدير
function showExportLoading(format) {
    const button = document.querySelector(`[onclick="exportReport('${format}')"]`);
    if (button) {
        button.disabled = true;
        button.innerHTML = `<i class="fas fa-spinner fa-spin me-2"></i>جاري التصدير...`;
    }
}

// إخفاء مؤشر تحميل التصدير
function hideExportLoading(format) {
    const button = document.querySelector(`[onclick="exportReport('${format}')"]`);
    if (button) {
        button.disabled = false;
        const icons = {
            'excel': 'file-excel',
            'pdf': 'file-pdf',
            'word': 'file-word'
        };
        const labels = {
            'excel': 'Excel',
            'pdf': 'PDF',
            'word': 'Word'
        };
        button.innerHTML = `<i class="fas fa-${icons[format]} me-2"></i>${labels[format]}`;
    }
}

    return; // إنهاء الدالة هنا

    fetch('/api/export-report-old', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(exportData)
    })
    .then(response => {
        if (response.ok) {
            return response.blob();
        }
        throw new Error('فشل في تصدير التقرير');
    })
    .then(blob => {
        hideExportLoading(format);
        
        // تحميل الملف
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `تقرير_الاجتماعات_${startDate}_${endDate}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        
        NotificationUtils.showSuccess(`تم تصدير التقرير بصيغة ${format.toUpperCase()} بنجاح`);
    })
    .catch(error => {
        hideExportLoading(format);
        console.error('Error:', error);
        NotificationUtils.showError('حدث خطأ أثناء تصدير التقرير');
    });
}

// طباعة الرسم البياني
function printChart() {
    if (!currentChart) {
        NotificationUtils.showWarning('لا يوجد رسم بياني لطباعته');
        return;
    }
    
    const canvas = document.getElementById('mainChart');
    const printWindow = window.open('', '_blank');
    
    printWindow.document.write(`
        <html>
        <head>
            <title>طباعة الرسم البياني</title>
            <style>
                body { 
                    font-family: 'Cairo', sans-serif; 
                    text-align: center; 
                    margin: 20px;
                }
                img { 
                    max-width: 100%; 
                    height: auto; 
                }
                h1 {
                    color: #1B4332;
                    margin-bottom: 20px;
                }
                .footer {
                    margin-top: 20px;
                    font-size: 12px;
                    color: #666;
                }
            </style>
        </head>
        <body>
            <h1>تقرير الرسم البياني - نظام إدارة المواعيد</h1>
            <img src="${canvas.toDataURL()}" alt="الرسم البياني">
            <div class="footer">
                <p>القوات المسلحة الأردنية - الدائرة المالية</p>
                <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-JO')}</p>
            </div>
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.print();
}

// تحميل الرسم البياني كصورة
function downloadChart() {
    if (!currentChart) {
        NotificationUtils.showWarning('لا يوجد رسم بياني لتحميله');
        return;
    }
    
    const canvas = document.getElementById('mainChart');
    const url = canvas.toDataURL('image/png');
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `رسم_بياني_${currentChartType}_${new Date().toISOString().split('T')[0]}.png`;
    a.click();
    
    NotificationUtils.showSuccess('تم تحميل الرسم البياني كصورة');
}

// تحديث التقارير
function refreshReports() {
    updateStatistics();
    NotificationUtils.showInfo('جاري تحديث البيانات...');
}

// إظهار مؤشر التحميل للإحصائيات
function showLoadingIndicator() {
    // يمكن إضافة مؤشر تحميل هنا
}

// إخفاء مؤشر التحميل للإحصائيات
function hideLoadingIndicator() {
    // يمكن إخفاء مؤشر التحميل هنا
}

// إظهار مؤشر التحميل للتصدير
function showExportLoading(format) {
    console.log('إظهار مؤشر التحميل لـ:', format);

    // البحث عن الزر بطريقة أكثر دقة
    const buttons = document.querySelectorAll('.btn-export');
    buttons.forEach(button => {
        const card = button.closest('.export-card');
        if (card && card.textContent.toLowerCase().includes(format.toLowerCase())) {
            button.innerHTML = `<i class="fas fa-spinner fa-spin me-1"></i>جاري التصدير...`;
            button.disabled = true;
        }
    });
}

// إخفاء مؤشر التحميل للتصدير
function hideExportLoading(format) {
    console.log('إخفاء مؤشر التحميل لـ:', format);

    // البحث عن الزر بطريقة أكثر دقة
    const buttons = document.querySelectorAll('.btn-export');
    buttons.forEach(button => {
        const card = button.closest('.export-card');
        if (card && card.textContent.toLowerCase().includes(format.toLowerCase())) {
            let icon = 'fa-download';
            let text = format.toUpperCase();

            if (format.toLowerCase() === 'excel') {
                icon = 'fa-file-excel';
                text = 'EXCEL';
            } else if (format.toLowerCase() === 'pdf') {
                icon = 'fa-file-pdf';
                text = 'PDF';
            } else if (format.toLowerCase() === 'word') {
                icon = 'fa-file-word';
                text = 'WORD';
            }

            button.innerHTML = `<i class="fas ${icon} me-1"></i>تصدير ${text}`;
            button.disabled = false;
        }
    });
}

// وظائف الطباعة للتقارير
function printExcelReport() {
    NotificationUtils.showInfo('سيتم فتح معاينة الطباعة لتقرير Excel');
}

function printPdfReport() {
    console.log('طباعة تقرير PDF');

    try {
        // فتح التقرير في نافذة جديدة للطباعة
        const printWindow = window.open('/export_report_pdf', '_blank');

        // انتظار تحميل النافذة ثم طباعة
        printWindow.onload = function() {
            setTimeout(() => {
                printWindow.print();
            }, 1000);
        };

        NotificationUtils.showSuccess('تم فتح التقرير للطباعة');
    } catch (error) {
        console.error('خطأ في طباعة التقرير:', error);
        NotificationUtils.showError('فشل في فتح التقرير للطباعة');
    }
}

function printWordReport() {
    NotificationUtils.showInfo('سيتم فتح معاينة الطباعة لتقرير Word');
}

// وظائف جلب البيانات الحقيقية
async function fetchMonthlyData() {
    try {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;

        const response = await fetch('/api/statistics', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                start_date: startDate || '2025-01-01',
                end_date: endDate || '2025-12-31'
            })
        });

        const data = await response.json();
        if (data.success && data.statistics.monthly_data) {
            return data.statistics.monthly_data;
        }
        return null;
    } catch (error) {
        console.error('خطأ في جلب البيانات الشهرية:', error);
        return null;
    }
}

async function fetchTypesData() {
    try {
        const response = await fetch('/api/statistics', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                start_date: '2025-01-01',
                end_date: '2025-12-31'
            })
        });

        const data = await response.json();
        if (data.success && data.statistics.types_data) {
            return data.statistics.types_data;
        }
        return null;
    } catch (error) {
        console.error('خطأ في جلب بيانات الأنواع:', error);
        return null;
    }
}

async function fetchStatusData() {
    try {
        const response = await fetch('/api/statistics', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                start_date: '2025-01-01',
                end_date: '2025-12-31'
            })
        });

        const data = await response.json();
        if (data.success && data.statistics) {
            return {
                completed: data.statistics.completed_meetings || 0,
                upcoming: data.statistics.upcoming_meetings || 0,
                cancelled: data.statistics.cancelled_meetings || 0
            };
        }
        return null;
    } catch (error) {
        console.error('خطأ في جلب بيانات الحالة:', error);
        return null;
    }
}

// طباعة التقرير
function printReport() {
    console.log('طباعة التقرير...');
    NotificationUtils.showInfo('جاري تحضير التقرير للطباعة...');

    // إخفاء العناصر غير المرغوب في طباعتها
    const elementsToHide = document.querySelectorAll('.no-print, .btn, .form-control, .navbar, .sidebar, .export-buttons, .filter-section');
    const originalDisplays = [];

    elementsToHide.forEach((el, index) => {
        originalDisplays[index] = el.style.display;
        el.style.display = 'none';
    });

    // إضافة أنماط خاصة بالطباعة
    const printStyles = document.createElement('style');
    printStyles.id = 'print-styles';
    printStyles.innerHTML = `
        @media print {
            body {
                font-size: 12pt;
                color: black !important;
                background: white !important;
            }
            .statistics-card {
                break-inside: avoid;
                margin-bottom: 10px;
                border: 1px solid #000 !important;
                background: white !important;
            }
            .chart-container {
                break-inside: avoid;
                page-break-inside: avoid;
            }
            h1, h2, h3, h4, h5, h6 {
                color: black !important;
                page-break-after: avoid;
            }
            .card {
                border: 1px solid #000 !important;
                background: white !important;
                box-shadow: none !important;
            }
            .professional-section {
                border: 1px solid #000 !important;
                background: white !important;
                box-shadow: none !important;
            }
            .section-title {
                background: #f0f0f0 !important;
                color: black !important;
            }
            canvas {
                max-width: 100% !important;
                height: auto !important;
            }
        }
    `;
    document.head.appendChild(printStyles);

    // طباعة الصفحة
    setTimeout(() => {
        window.print();

        // إعادة إظهار العناصر المخفية
        elementsToHide.forEach((el, index) => {
            el.style.display = originalDisplays[index];
        });

        // إزالة أنماط الطباعة
        const printStylesElement = document.getElementById('print-styles');
        if (printStylesElement) {
            document.head.removeChild(printStylesElement);
        }

        NotificationUtils.showSuccess('تم فتح نافذة الطباعة');
    }, 1000);
}

// مشاركة التقرير
function shareReport() {
    console.log('مشاركة التقرير...');

    const shareData = {
        title: 'تقرير الاجتماعات - مديرية الدائرة المالية',
        url: window.location.href,
        text: 'تقرير شامل عن الاجتماعات والفعاليات'
    };

    // نسخ الرابط إلى الحافظة
    if (navigator.clipboard) {
        navigator.clipboard.writeText(shareData.url).then(() => {
            NotificationUtils.showSuccess('تم نسخ رابط التقرير إلى الحافظة');
        }).catch(() => {
            NotificationUtils.showInfo('يمكنك نسخ الرابط من شريط العنوان');
        });
    } else {
        NotificationUtils.showInfo('يمكنك نسخ الرابط من شريط العنوان');
    }
}

/**
 * طباعة بطاقة إحصائية محددة
 * @param {string} cardType - نوع البطاقة
 */
function printStatCard(cardType) {
    console.log('🖨️ بدء عملية الطباعة للبطاقة:', cardType);

    try {
        // التحقق من وجود البيانات في الصفحة
        console.log('🔍 البحث عن البيانات في الصفحة...');

        // الحصول على بيانات البطاقة
        const cardData = getCardData(cardType);
        console.log('📊 البيانات المستخرجة:', cardData);

        // التحقق من صحة البيانات
        if (!cardData || !cardData.title || cardData.value === undefined) {
            console.error('❌ البيانات غير صحيحة:', cardData);
            NotificationUtils.showError('لا توجد بيانات صالحة للطباعة');
            return;
        }

        // إنشاء محتوى الطباعة
        console.log('📄 إنشاء محتوى الطباعة...');
        const printContent = createPrintContent(cardData);

        if (!printContent || printContent.trim() === '') {
            console.error('❌ فشل في إنشاء محتوى الطباعة');
            NotificationUtils.showError('فشل في إنشاء محتوى الطباعة');
            return;
        }

        console.log('✅ تم إنشاء محتوى الطباعة بنجاح');

        // حفظ المحتوى الأصلي
        const originalContent = document.body.innerHTML;
        const originalTitle = document.title;

        // استبدال المحتوى بمحتوى الطباعة
        console.log('🔄 استبدال محتوى الصفحة...');
        document.body.innerHTML = printContent;
        document.title = `طباعة - ${cardData.title}`;

        // انتظار قصير لضمان تحميل المحتوى
        setTimeout(() => {
            console.log('🖨️ فتح حوار الطباعة...');
            window.print();

            // استعادة المحتوى الأصلي بعد الطباعة
            setTimeout(() => {
                console.log('🔄 استعادة المحتوى الأصلي...');
                document.body.innerHTML = originalContent;
                document.title = originalTitle;

                // إعادة تحميل الصفحة لاستعادة الأحداث
                setTimeout(() => {
                    location.reload();
                }, 500);
            }, 1000);
        }, 300);

        // إنشاء محتوى HTML للطباعة
        const printContent = `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>طباعة إحصائية - ${cardData.title}</title>
                <style>
                    * {
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                    }

                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        direction: rtl;
                        text-align: right;
                        padding: 40px;
                        background: white;
                        color: #333;
                    }

                    .print-header {
                        text-align: center;
                        margin-bottom: 40px;
                        border-bottom: 3px solid #007bff;
                        padding-bottom: 20px;
                    }

                    .print-title {
                        font-size: 28px;
                        font-weight: bold;
                        color: #007bff;
                        margin-bottom: 10px;
                    }

                    .print-subtitle {
                        font-size: 16px;
                        color: #666;
                    }

                    .stat-card-print {
                        background: #f8f9fa;
                        border: 2px solid #dee2e6;
                        border-radius: 12px;
                        padding: 30px;
                        margin: 20px 0;
                        text-align: center;
                    }

                    .stat-number-print {
                        font-size: 48px;
                        font-weight: bold;
                        color: ${cardData.color};
                        margin-bottom: 15px;
                    }

                    .stat-title-print {
                        font-size: 24px;
                        font-weight: bold;
                        color: #333;
                        margin-bottom: 10px;
                    }

                    .stat-description-print {
                        font-size: 16px;
                        color: #666;
                        line-height: 1.6;
                    }

                    .print-footer {
                        margin-top: 40px;
                        text-align: center;
                        font-size: 14px;
                        color: #999;
                        border-top: 1px solid #dee2e6;
                        padding-top: 20px;
                    }

                    @media print {
                        body {
                            padding: 20px;
                        }

                        .print-header {
                            margin-bottom: 30px;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="print-header">
                    <div class="print-title">نظام إدارة الاجتماعات</div>
                    <div class="print-subtitle">تقرير إحصائي - ${cardData.title}</div>
                </div>

                <div class="stat-card-print">
                    <div class="stat-number-print">${cardData.value}</div>
                    <div class="stat-title-print">${cardData.title}</div>
                    <div class="stat-description-print">${cardData.description}</div>
                </div>

                <div class="print-footer">
                    <p>تم إنشاء هذا التقرير في: ${new Date().toLocaleDateString('ar-SA')}</p>
                    <p>نظام إدارة الاجتماعات - القوات المسلحة الأردنية</p>
                </div>
            </body>
            </html>
        `;

        // كتابة المحتوى في النافذة الجديدة
        printWindow.document.write(printContent);
        printWindow.document.close();

        // انتظار تحميل المحتوى ثم الطباعة
        printWindow.onload = function() {
            printWindow.print();
            printWindow.close();
        };

        NotificationUtils.showSuccess('تم إعداد الطباعة بنجاح');

    } catch (error) {
        console.error('خطأ في الطباعة:', error);
        NotificationUtils.showError('حدث خطأ أثناء إعداد الطباعة');
    }
}

/**
 * إنشاء محتوى HTML للطباعة
 * @param {Object} cardData - بيانات البطاقة
 * @returns {string} محتوى HTML
 */
function createPrintContent(cardData) {
    console.log('🖨️ إنشاء محتوى الطباعة للبيانات:', cardData);

    // التأكد من وجود البيانات
    const safeCardData = {
        title: cardData.title || 'بيانات غير متاحة',
        value: cardData.value || '0',
        description: cardData.description || 'لا توجد معلومات إضافية',
        color: cardData.color || '#007bff'
    };

    const currentDate = new Date();
    // English date and time, 24-hour, always English digits
    const englishDate = currentDate.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        numberingSystem: 'latn'
    });
    const englishTime = currentDate.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
        numberingSystem: 'latn'
    });

    return `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>طباعة إحصائية - ${safeCardData.title}</title>
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }

                body {
                    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                    direction: rtl;
                    text-align: right;
                    padding: 40px;
                    background: white;
                    color: #333;
                    line-height: 1.6;
                    font-variant-numeric: normal;
                    font-feature-settings: "lnum", "tnum", "ss01";
                    /* Force English digits */
                    unicode-bidi: plaintext;
                }

                .print-header {
                    text-align: center;
                    margin-bottom: 40px;
                    border-bottom: 4px solid ${safeCardData.color};
                    padding-bottom: 25px;
                    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                    border-radius: 12px 12px 0 0;
                    padding: 30px;
                }

                .print-title {
                    font-size: 32px;
                    font-weight: bold;
                    color: ${safeCardData.color};
                    margin-bottom: 15px;
                    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
                }

                .print-subtitle {
                    font-size: 18px;
                    color: #666;
                    font-weight: 500;
                }

                .stat-card-print {
                    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
                    border: 3px solid ${safeCardData.color};
                    border-radius: 16px;
                    padding: 50px;
                    margin: 30px 0;
                    text-align: center;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                    position: relative;
                    overflow: hidden;
                }

                .stat-card-print::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 6px;
                    background: linear-gradient(90deg, ${safeCardData.color}, ${safeCardData.color}aa);
                }

                .stat-number-print {
                    font-size: 72px;
                    font-weight: bold;
                    color: ${safeCardData.color};
                    margin-bottom: 25px;
                    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
                    display: block;
                    font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                    font-feature-settings: "lnum", "tnum", "ss01";
                }

                .stat-title-print {
                    font-size: 28px;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 15px;
                    border-bottom: 2px solid #eee;
                    padding-bottom: 10px;
                }

                .stat-description-print {
                    font-size: 18px;
                    color: #666;
                    line-height: 1.8;
                    font-style: italic;
                }

                .print-footer {
                    margin-top: 50px;
                    text-align: center;
                    font-size: 14px;
                    color: #999;
                    border-top: 2px solid #dee2e6;
                    padding-top: 25px;
                    background: #f8f9fa;
                    border-radius: 8px;
                    padding: 25px;
                }

                .footer-info {
                    margin: 8px 0;
                    font-weight: 500;
                }

                .footer-org {
                    font-weight: bold;
                    color: ${safeCardData.color};
                    font-size: 16px;
                    margin-top: 15px;
                }

                @media print {
                    body {
                        padding: 20px;
                    }

                    .print-header {
                        margin-bottom: 30px;
                        padding: 20px;
                    }

                    .stat-card-print {
                        padding: 30px;
                        margin: 20px 0;
                    }

                    .print-footer {
                        padding: 15px;
                    }
                }
            </style>
        </head>
        <body>
            <div class="print-header">
                <div class="print-title">🏛️ نظام إدارة الاجتماعات</div>
                <div class="print-subtitle">📊 تقرير إحصائي مفصل - ${safeCardData.title}</div>
            </div>

            <div class="stat-card-print">
                <div class="stat-number-print">${safeCardData.value}</div>
                <div class="stat-title-print">${safeCardData.title}</div>
                <div class="stat-description-print">${safeCardData.description}</div>
            </div>

            <div class="print-footer">
                <div class="footer-info"><strong>📅 Date Created:</strong> ${englishDate}</div>
                <div class="footer-info"><strong>🕐 Time Created:</strong> ${englishTime}</div>
                <div class="footer-info"><strong>👤 المستخدم:</strong> مدير النظام</div>
                <div class="footer-org">القوات المسلحة الأردنية - نظام إدارة الاجتماعات</div>
            </div>
        </body>
        </html>
    `;
}

/**
 * تصدير بطاقة إحصائية إلى PDF
 * @param {string} cardType - نوع البطاقة
 */
function exportStatToPDF(cardType) {
    console.log('📄 تم استدعاء دالة تصدير PDF للبطاقة:', cardType);
    try {
        // الحصول على بيانات البطاقة
        const cardData = getCardData(cardType);


        // إنشاء محتوى PDF مباشرة في الصفحة الحالية
        const pdfContent = createPDFContent(cardData);

        // إنشاء عنصر مخفي للطباعة
        const pdfDiv = document.createElement('div');
        pdfDiv.innerHTML = pdfContent;
        pdfDiv.style.display = 'none';
        document.body.appendChild(pdfDiv);

        // حفظ المحتوى الأصلي
        const originalContent = document.body.innerHTML;

        // استبدال المحتوى بمحتوى PDF
        document.body.innerHTML = pdfContent;

        // فتح حوار الطباعة (يمكن حفظ كـ PDF)
        setTimeout(() => {
            window.print();
            // استعادة المحتوى الأصلي بعد الطباعة
            setTimeout(() => {
                document.body.innerHTML = originalContent;
                location.reload();
            }, 1000);
        }, 500);

        // إذا كنت بحاجة لفتح نافذة جديدة للتصدير، استخدم اسم متغير مختلف
        // const pdfContentHtml = `...`; // هنا يمكن وضع الكود إذا لزم الأمر

        // NotificationUtils.showSuccess('تم إعداد تصدير PDF بنجاح');
        NotificationUtils.showSuccess('تم إعداد تصدير PDF بنجاح');

    } catch (error) {
        console.error('خطأ في تصدير PDF:', error);
        NotificationUtils.showError('حدث خطأ أثناء تصدير PDF');
    }
}

/**
 * إنشاء محتوى HTML لتصدير PDF
 * @param {Object} cardData - بيانات البطاقة
 * @returns {string} محتوى HTML
 */
function createPDFContent(cardData) {
    console.log('📄 إنشاء محتوى PDF للبيانات:', cardData);

    // التأكد من وجود البيانات
    const safeCardData = {
        title: cardData.title || 'بيانات غير متاحة',
        value: cardData.value || '0',
        description: cardData.description || 'لا توجد معلومات إضافية',
        color: cardData.color || '#007bff'
    };

    const currentDate = new Date();
    const arabicDate = currentDate.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    const arabicTime = currentDate.toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit'
    });

    return `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>تصدير PDF - ${safeCardData.title}</title>
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }

                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    direction: rtl;
                    text-align: right;
                    padding: 40px;
                    background: white;
                    color: #333;
                    line-height: 1.6;
                }

                .pdf-header {
                    text-align: center;
                    margin-bottom: 40px;
                    border-bottom: 3px solid #007bff;
                    padding-bottom: 20px;
                }

                .pdf-title {
                    font-size: 32px;
                    font-weight: bold;
                    color: #007bff;
                    margin-bottom: 10px;
                }

                .pdf-subtitle {
                    font-size: 18px;
                    color: #666;
                }

                .stat-card-pdf {
                    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                    border: 2px solid ${cardData.color};
                    border-radius: 16px;
                    padding: 40px;
                    margin: 30px 0;
                    text-align: center;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                }

                .stat-number-pdf {
                    font-size: 64px;
                    font-weight: bold;
                    color: ${cardData.color};
                    margin-bottom: 20px;
                    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
                }

                .stat-title-pdf {
                    font-size: 28px;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 15px;
                }

                .stat-description-pdf {
                    font-size: 18px;
                    color: #666;
                    line-height: 1.8;
                }

                .pdf-footer {
                    margin-top: 50px;
                    text-align: center;
                    font-size: 14px;
                    color: #999;
                    border-top: 2px solid #dee2e6;
                    padding-top: 30px;
                }

                .instructions {
                    background: #e3f2fd;
                    border: 1px solid #2196f3;
                    border-radius: 8px;
                    padding: 20px;
                    margin: 20px 0;
                    text-align: center;
                    font-size: 16px;
                    color: #1976d2;
                }

                @media print {
                    body {
                        padding: 20px;
                    }

                    .instructions {
                        display: none;
                    }
                }
            </style>
        </head>
        <body>
            <div class="pdf-header">
                <div class="pdf-title">نظام إدارة الاجتماعات</div>
                <div class="pdf-subtitle">تقرير إحصائي مفصل - ${cardData.title}</div>
            </div>

            <div class="instructions">
                <strong>تعليمات:</strong> لحفظ هذا التقرير كملف PDF، اختر "حفظ كـ PDF" من خيارات الطباعة
            </div>

            <div class="stat-card-pdf">
                <div class="stat-number-pdf">${cardData.value}</div>
                <div class="stat-title-pdf">${cardData.title}</div>
                <div class="stat-description-pdf">${cardData.description}</div>
            </div>

            <div class="pdf-footer">
                <p><strong>تاريخ الإنشاء:</strong> ${new Date().toLocaleDateString('ar-SA')}</p>
                <p><strong>الوقت:</strong> ${new Date().toLocaleTimeString('ar-SA')}</p>
                <p><strong>المصدر:</strong> نظام إدارة الاجتماعات - القوات المسلحة الأردنية</p>
            </div>
        </body>
        </html>
    `;
}

/**
 * الحصول على بيانات البطاقة حسب النوع
 * @param {string} cardType - نوع البطاقة
 * @returns {Object} بيانات البطاقة
 */
function getCardData(cardType) {
    console.log('📊 جلب بيانات البطاقة:', cardType);

    // البحث عن العناصر بطرق مختلفة
    let element = null;
    let value = '0';

    // محاولة العثور على العنصر بطرق مختلفة
    if (cardType === 'total-meetings') {
        element = document.getElementById('totalMeetings') ||
                 document.querySelector('[data-stat="total-meetings"]') ||
                 document.querySelector('.stat-number');
    } else if (cardType === 'upcoming-meetings') {
        element = document.getElementById('upcomingMeetings') ||
                 document.querySelector('[data-stat="upcoming-meetings"]') ||
                 document.querySelectorAll('.stat-number')[1];
    } else if (cardType === 'completed-meetings') {
        element = document.getElementById('completedMeetings') ||
                 document.querySelector('[data-stat="completed-meetings"]') ||
                 document.querySelectorAll('.stat-number')[2];
    } else if (cardType === 'cancelled-meetings') {
        element = document.getElementById('cancelledMeetings') ||
                 document.querySelector('[data-stat="cancelled-meetings"]') ||
                 document.querySelectorAll('.stat-number')[3];
    } else if (cardType === 'monthly-average') {
        element = document.getElementById('monthlyAverage') ||
                 document.querySelector('[data-stat="monthly-average"]') ||
                 document.querySelectorAll('.stat-number')[4];
    } else if (cardType === 'most-active-month') {
        element = document.getElementById('mostActiveMonth') ||
                 document.querySelector('[data-stat="most-active-month"]') ||
                 document.querySelectorAll('.stat-number')[5];
    }

    // الحصول على القيمة
    if (element) {
        value = element.textContent || element.innerText || '0';
        value = value.trim();
        console.log('✅ تم العثور على القيمة:', value);
    } else {
        console.log('⚠️ لم يتم العثور على العنصر، استخدام القيم الافتراضية');
        // استخدام القيم من الصفحة مباشرة
        const allNumbers = document.querySelectorAll('.stat-number');
        if (allNumbers.length > 0) {
            const index = ['total-meetings', 'upcoming-meetings', 'completed-meetings', 'cancelled-meetings', 'monthly-average', 'most-active-month'].indexOf(cardType);
            if (index >= 0 && allNumbers[index]) {
                value = allNumbers[index].textContent || allNumbers[index].innerText || '0';
                value = value.trim();
                console.log('✅ تم العثور على القيمة من المؤشر:', value);
            }
        }
    }

    const cardElements = {
        'total-meetings': {
            title: 'إجمالي الاجتماعات',
            description: 'العدد الكلي للاجتماعات المسجلة في النظام',
            color: '#007bff'
        },
        'upcoming-meetings': {
            title: 'الاجتماعات القادمة',
            description: 'الاجتماعات المجدولة في المستقبل',
            color: '#28a745'
        },
        'completed-meetings': {
            title: 'الاجتماعات المكتملة',
            description: 'الاجتماعات التي تم عقدها بنجاح',
            color: '#17a2b8'
        },
        'cancelled-meetings': {
            title: 'الاجتماعات الملغية',
            description: 'الاجتماعات التي تم إلغاؤها',
            color: '#dc3545'
        },
        'monthly-average': {
            title: 'المتوسط الشهري',
            description: 'معدل الاجتماعات شهرياً',
            color: '#6f42c1'
        },
        'most-active-month': {
            title: 'أكثر الشهور نشاطاً',
            description: 'الشهر الذي سجل أعلى عدد من الاجتماعات',
            color: '#fd7e14'
        }
    };

    const cardInfo = cardElements[cardType];
    if (!cardInfo) {
        console.log('⚠️ نوع البطاقة غير معروف:', cardType);
        return {
            title: 'غير محدد',
            value: '0',
            description: 'لا توجد بيانات متاحة',
            color: '#6c757d'
        };
    }

    const result = {
        title: cardInfo.title,
        value: value,
        description: cardInfo.description,
        color: cardInfo.color
    };

    console.log('📊 البيانات النهائية:', result);
    return result;
}
