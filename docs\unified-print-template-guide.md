# دليل استخدام قالب الطباعة الموحد

## نظرة عامة

تم إنشاء قالب طباعة موحد لجميع التقارير في النظام بحيث يكون لها نفس الشكل والتصميم الجميل. هذا القالب يوفر:

- تصميم موحد لجميع التقارير
- إطار جميل مع حدود ملونة
- هيدر مبسط بدون شعار
- تذييل موحد مع التاريخ والوقت ومديرية الدائرة المالية
- دعم الألوان والأيقونات
- تصميم متجاوب للطباعة

## الملفات المطلوبة

### 1. ملف JavaScript الرئيسي
```
static/js/unified-print-template.js
```

### 2. ملف CSS للأنماط
```
static/css/unified-print-styles.css
```

## كيفية الاستخدام

### 1. إضافة المراجع في HTML

```html
<!-- في نهاية الصفحة قبل إغلاق body -->
<script src="{{ url_for('static', filename='js/unified-print-template.js') }}"></script>
<link rel="stylesheet" href="{{ url_for('static', filename='css/unified-print-styles.css') }}">
```

### 2. طباعة تفاصيل اجتماع

```javascript
function printMeetingDetails() {
    const meeting = {
        subject: 'موضوع الاجتماع',
        meeting_type: 'نوع الاجتماع',
        meeting_date: '2025/01/15',
        meeting_time: '10:30',
        location: 'مكان الاجتماع',
        inviting_party: 'جهة الدعوة',
        arrival_time_before: '15',
        book_number: 'رقم الكتاب',
        book_date: '2025/01/10',
        dress_code: 'زي الحضور',
        notes: 'ملاحظات إضافية'
    };

    // استخدام وظيفة الطباعة الموحدة
    printMeetingReport(meeting);
}
```

### 3. طباعة إحصائية

```javascript
function printStatCard(cardType) {
    // البيانات ستُستخرج تلقائياً من الصفحة
    // أو يمكن تمرير البيانات مباشرة
    const cardData = {
        title: 'عنوان الإحصائية',
        value: '150',
        description: 'وصف الإحصائية',
        color: '#1B4332'
    };

    printStatCard(cardType, cardData);
}
```

### 4. طباعة تقرير مخصص

```javascript
function printCustomReport() {
    const data = {
        title: 'عنوان التقرير',
        subtitle: 'العنوان الفرعي',
        content: [
            {
                icon: 'fas fa-file-alt',
                label: 'التسمية',
                value: 'القيمة'
            },
            {
                icon: 'fas fa-calendar',
                label: 'التاريخ',
                value: '2025/01/15'
            }
        ],
        showLogo: true,
        showBorder: true,
        customStyles: `
            .custom-style {
                color: red;
            }
        `
    };

    printUnifiedReport(data);
}
```

## الوظائف المتاحة

### 1. `printUnifiedReport(data)`
الوظيفة الرئيسية لطباعة أي تقرير

**المعاملات:**
- `title`: عنوان التقرير
- `subtitle`: العنوان الفرعي
- `content`: مصفوفة من العناصر
- `showLogo`: إظهار الشعار (افتراضي: true)
- `showBorder`: إظهار الحدود (افتراضي: true)
- `customStyles`: أنماط CSS إضافية

### 2. `printMeetingReport(meeting)`
طباعة تفاصيل اجتماع

**المعاملات:**
- `meeting`: كائن يحتوي على تفاصيل الاجتماع

### 3. `printStatCard(cardType, cardData)`
طباعة بطاقة إحصائية

**المعاملات:**
- `cardType`: نوع البطاقة
- `cardData`: بيانات البطاقة (اختياري)

### 4. `createUnifiedPrintTemplate(data)`
إنشاء HTML للطباعة بدون فتح نافذة

## تخصيص الألوان

يمكن تخصيص الألوان من خلال متغيرات CSS:

```css
:root {
    --primary-color: #1B4332;
    --secondary-color: #2D5A3D;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
}
```

## الأيقونات المدعومة

القالب يدعم أيقونات Font Awesome مع ألوان مخصصة:

- `fa-file-alt`: أزرق فاتح
- `fa-tag`: بنفسجي
- `fa-calendar`: أخضر
- `fa-clock`: أصفر
- `fa-map-marker-alt`: أحمر
- `fa-building`: أزرق فاتح
- `fa-user-clock`: برتقالي

## مثال كامل

```html
<!DOCTYPE html>
<html>
<head>
    <title>صفحة التقارير</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/unified-print-styles.css') }}">
</head>
<body>
    <button onclick="printMyReport()">طباعة التقرير</button>

    <script src="{{ url_for('static', filename='js/unified-print-template.js') }}"></script>
    <script>
        function printMyReport() {
            const data = {
                title: 'تقرير الاجتماعات الشهري',
                subtitle: 'نظام إدارة المواعيد - يناير 2025',
                content: [
                    {
                        icon: 'fas fa-chart-bar',
                        label: 'إجمالي الاجتماعات',
                        value: '45 اجتماع'
                    },
                    {
                        icon: 'fas fa-check-circle',
                        label: 'الاجتماعات المكتملة',
                        value: '38 اجتماع'
                    }
                ]
            };

            printUnifiedReport(data);
        }
    </script>
</body>
</html>
```

## الصفحات المحدثة

تم تحديث الصفحات التالية لاستخدام القالب الموحد:

1. `templates/meeting_details.html` - تفاصيل الاجتماع
2. `templates/reports.html` - صفحة التقارير
3. `templates/search.html` - صفحة البحث

## ملاحظات مهمة

1. تأكد من تحميل ملف JavaScript قبل استخدام الوظائف
2. يمكن استخدام القالب مع أي نوع من البيانات
3. القالب يدعم الطباعة والمعاينة
4. جميع النصوص باللغة العربية مع دعم RTL
5. التصميم متجاوب ويعمل على جميع الأحجام

## استكشاف الأخطاء

### المشكلة: الوظيفة غير معرفة
**الحل:** تأكد من تحميل ملف `unified-print-template.js`

### المشكلة: التصميم لا يظهر بشكل صحيح
**الحل:** تأكد من تحميل ملف `unified-print-styles.css`

### المشكلة: البيانات لا تظهر
**الحل:** تحقق من صحة البيانات المرسلة للوظيفة
