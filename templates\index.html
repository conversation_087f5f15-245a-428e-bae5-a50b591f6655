{% extends "base.html" %}

{% block title %}الصفحة الرئيسية - نظام اجتماعات المدير{% endblock %}

{% block extra_css %}
<style>
    .welcome-section {
        background: linear-gradient(135deg, rgba(27, 67, 50, 0.1) 0%, rgba(45, 106, 79, 0.1) 100%);
        border-radius: 20px;
        padding: 3rem 2rem;
        margin-bottom: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    .welcome-section::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 214, 10, 0.1) 0%, transparent 70%);
        animation: rotate 20s linear infinite;
    }
    
    @keyframes rotate {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .welcome-content {
        position: relative;
        z-index: 1;
    }
    
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .action-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        cursor: pointer;
        border: 2px solid transparent;
    }
    
    .action-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        border-color: var(--jaf-primary);
    }
    
    .action-icon {
        width: 80px;
        height: 80px;
        background: var(--jaf-gradient);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 2rem;
        color: white;
    }
    
    .upcoming-meetings {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .meeting-item {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #f0f0f0;
        transition: all 0.3s ease;
    }
    
    .meeting-item:hover {
        background-color: rgba(27, 67, 50, 0.05);
    }
    
    .meeting-item:last-child {
        border-bottom: none;
    }
    
    .meeting-time {
        background: var(--jaf-primary);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
    }
    
    .meeting-type {
        background: var(--jaf-info);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Welcome Section -->
    <div class="welcome-section fade-in-up">
        <div class="welcome-content">
            <h1 class="display-4 fw-bold text-primary mb-3">
                <i class="fas fa-calendar-check me-3"></i>
                مرحباً بك في نظام اجتماعات المدير
            </h1>
            <p class="lead text-muted mb-4">
                مديرية الدائرة المالية
            </p>
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <p class="text-muted">
                        نظام متكامل لإدارة المواعيد والاجتماعات الخارجية مع إمكانيات متقدمة للتنظيم والمتابعة والتقارير
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics Cards -->
    <div class="stats-grid fade-in-up">
        <div class="stats-card">
            <div class="stats-number">{{ total_meetings or 0 }}</div>
            <div class="stats-label">
                <i class="fas fa-calendar-alt me-2"></i>
                إجمالي الاجتماعات
            </div>
        </div>
        
        <div class="stats-card">
            <div class="stats-number">{{ upcoming_meetings_count or 0 }}</div>
            <div class="stats-label">
                <i class="fas fa-clock me-2"></i>
                الاجتماعات القادمة
            </div>
        </div>
        
        <div class="stats-card">
            <div class="stats-number">{{ this_month_meetings or 0 }}</div>
            <div class="stats-label">
                <i class="fas fa-calendar-week me-2"></i>
                اجتماعات هذا الشهر
            </div>
        </div>
        
        <div class="stats-card">
            <div class="stats-number">{{ pending_notifications or 0 }}</div>
            <div class="stats-label">
                <i class="fas fa-bell me-2"></i>
                الإشعارات المعلقة
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="row">
        <div class="col-lg-8">
            <h3 class="mb-4">
                <i class="fas fa-bolt me-2 text-warning"></i>
                الإجراءات السريعة
            </h3>
            
            <div class="quick-actions">
                <div class="action-card" onclick="location.href='{{ url_for('all_meetings') }}'">
                    <div class="action-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h5 class="fw-bold mb-2">كل الاجتماعات</h5>
                    <p class="text-muted mb-0">عرض جميع الاجتماعات والفعاليات</p>
                </div>

                <div class="action-card" onclick="location.href='{{ url_for('add_meeting') }}'">
                    <div class="action-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <h5 class="fw-bold mb-2">اجتماع جديد</h5>
                    <p class="text-muted mb-0">إضافة موعد اجتماع جديد</p>
                </div>
                
                <div class="action-card" onclick="location.href='{{ url_for('search') }}'">
                    <div class="action-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h5 class="fw-bold mb-2">البحث</h5>
                    <p class="text-muted mb-0">البحث في الاجتماعات</p>
                </div>
                
                <div class="action-card" onclick="location.href='{{ url_for('reports') }}'">
                    <div class="action-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h5 class="fw-bold mb-2">التقارير</h5>
                    <p class="text-muted mb-0">عرض التقارير والإحصائيات</p>
                </div>
                
                <div class="action-card" onclick="location.href='{{ url_for('edit_meetings') }}'">
                    <div class="action-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <h5 class="fw-bold mb-2">تعديل الاجتماعات</h5>
                    <p class="text-muted mb-0">إدارة الاجتماعات الموجودة</p>
                </div>
            </div>
        </div>
        
        <!-- Upcoming Meetings -->
        <div class="col-lg-4">
            <h3 class="mb-4">
                <i class="fas fa-calendar-day me-2 text-info"></i>
                الاجتماعات القادمة
            </h3>
            
            <div class="upcoming-meetings">
                {% if upcoming_meetings %}
                    {% for meeting in upcoming_meetings %}
                    <div class="meeting-item">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div class="meeting-time">
                                {{ meeting.meeting_time | twelve_hour_time }}
                            </div>
                            <div class="meeting-type">
                                {{ meeting.meeting_type.value }}
                            </div>
                        </div>
                        <h6 class="fw-bold mb-1">{{ meeting.subject[:50] }}{% if meeting.subject|length > 50 %}...{% endif %}</h6>
                        <p class="text-muted mb-1">
                            <img src="{{ url_for('static', filename='images/map.png') }}" alt="المكان" style="width: 14px; height: 14px; margin-left: 4px;">
                            {{ meeting.location[:30] }}{% if meeting.location|length > 30 %}...{% endif %}
                        </p>
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            <span dir="ltr">{{ meeting.meeting_date.strftime('%d/%m/%Y') }}</span>
                        </small>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-times text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-3">لا توجد اجتماعات قادمة</p>
                        <a href="{{ url_for('add_meeting') }}" class="btn btn-jaf-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>
                            إضافة اجتماع جديد
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Recent Activity -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        النشاط الأخير
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_activities %}
                        <div class="list-group list-group-flush">
                            {% for activity in recent_activities %}
                            <div class="list-group-item border-0 px-0">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-{{ activity.icon }} me-2 text-{{ activity.color }}"></i>
                                        {{ activity.description }}
                                    </div>
                                    <small class="text-muted">{{ activity.timestamp }}</small>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-inbox text-muted" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2 mb-0">لا يوجد نشاط حديث</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحديث الوقت الحالي
    function updateCurrentTime() {
        const now = new Date();
        const timeString = now.toLocaleString('ar-JO', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
        
        // يمكن إضافة عنصر لعرض الوقت الحالي
    }
    
    // تحديث كل دقيقة
    setInterval(updateCurrentTime, 60000);
    updateCurrentTime();
    
    // تأثيرات الحركة للبطاقات
    document.querySelectorAll('.action-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
</script>
{% endblock %}
