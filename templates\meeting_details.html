{% extends "base.html" %}

{% block title %}تفاصيل الاجتماع - {{ meeting.subject }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- تقرير طباعة مبسط - صفحة واحدة فقط -->
    <div class="print-simple-report" style="display: none;">
        <!-- العنوان الرئيسي -->
        <div class="print-header-simple">
            <div class="header-top">
                <img src="{{ url_for('static', filename='logo.png') }}" alt="شعار القوات المسلحة" class="logo-print">
                <div class="header-text">
                    <h1 class="main-title">نظام اجتماعات المدير</h1>
                    <h2 class="sub-title">مديرية الدائرة المالية</h2>
                </div>
            </div>
        </div>

        <!-- تفاصيل الاجتماع الأساسية -->
        <div class="meeting-details-table">
            <table class="details-table">
                <tr>
                    <td class="label">موضوع الاجتماع</td>
                    <td class="value">{{ meeting.subject }}</td>
                </tr>
                <tr>
                    <td class="label">نوع الفعالية</td>
                    <td class="value">{{ meeting.meeting_type }}</td>
                </tr>
                <tr>
                    <td class="label">التاريخ</td>
                    <td class="value">{{ meeting.meeting_date.strftime('%Y/%m/%d') }}</td>
                </tr>
                <tr>
                    <td class="label">الوقت</td>
                    <td class="value">{{ meeting.meeting_time | military_time }}</td>
                </tr>
                <tr>
                    <td class="label">مكان الاجتماع</td>
                    <td class="value">{{ meeting.location }}</td>
                </tr>
                <tr>
                    <td class="label">جهة الدعوة</td>
                    <td class="value">{{ meeting.inviting_party }}</td>
                </tr>
                <tr>
                    <td class="label">الحضور قبل الموعد</td>
                    <td class="value">{{ meeting.arrival_time_before }} دقيقة</td>
                </tr>
                <tr>
                    <td class="label">رقم الكتاب</td>
                    <td class="value">{{ meeting.book_number }}</td>
                </tr>
                <tr>
                    <td class="label">تاريخ الكتاب</td>
                    <td class="value">{{ meeting.book_date.strftime('%Y/%m/%d') }}</td>
                </tr>
                <tr>
                    <td class="label">زي الحضور</td>
                    <td class="value">{{ meeting.dress_code }}</td>
                </tr>
                <tr>
                    <td class="label">عدد المرفقات</td>
                    <td class="value">
                        {% if attachments %}
                            {{ attachments|length }} ملف
                            {% set total_size = attachments|sum(attribute='file_size') %}
                            {% if total_size %}
                                ({{ "%.1f"|format(total_size / 1024) }} KB إجمالي)
                            {% endif %}
                        {% else %}
                            لا توجد مرفقات
                        {% endif %}
                    </td>
                </tr>
                {% if meeting.notes %}
                <tr>
                    <td class="label">الملاحظات</td>
                    <td class="value">{{ meeting.notes }}</td>
                </tr>
                {% endif %}
                <tr>
                    <td class="label">حالة الاجتماع</td>
                    <td class="value">
                        {% if meeting.is_cancelled %}
                            <span style="color: #dc3545; font-weight: bold;">ملغي</span>
                        {% elif meeting.is_postponed %}
                            <span style="color: #ffc107; font-weight: bold;">مؤجل</span>
                        {% else %}
                            <span style="color: #28a745; font-weight: bold;">نشط</span>
                        {% endif %}
                    </td>
                </tr>
            </table>
        </div>

        <!-- قائمة المرفقات إن وجدت -->
        {% if attachments %}
        <div class="attachments-section">
            <h3 class="section-title">المرفقات</h3>
            <table class="details-table">
                {% for attachment in attachments %}
                <tr>
                    <td class="label">{{ loop.index }}. {{ attachment.original_filename }}</td>
                    <td class="value">{{ "%.1f"|format(attachment.file_size / 1024) }} KB</td>
                </tr>
                {% endfor %}
            </table>
        </div>
        {% endif %}

        <!-- تذييل التقرير -->
        <div class="print-footer">
            <div class="footer-info">
                <p><strong>تاريخ الطباعة:</strong> <span id="printSimpleDateTime"></span></p>
                <p><strong>المؤسسة:</strong> القوات المسلحة الأردنية - مديرية الدائرة المالية</p>
                <p><strong>نظام إدارة الاجتماعات</strong></p>
            </div>
        </div>
    </div>

    <!-- Header مضغوط -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center p-3 rounded" style="background: linear-gradient(135deg, #1B4332 0%, #2D5A3D 100%); color: white;">
                <div>
                    <h4 class="mb-1">
                        <i class="fas fa-calendar-alt me-2"></i>
                        {{ meeting.subject }}
                    </h4>
                    <small class="opacity-75">{{ meeting.meeting_date.strftime('%Y/%m/%d') }} • {{ meeting.meeting_time | twelve_hour_time }}</small>
                </div>
                <div class="d-flex gap-2 align-items-center">
                    {% if meeting.is_cancelled %}
                        <span class="badge bg-danger fs-6">ملغي</span>
                    {% elif meeting.is_postponed %}
                        <span class="badge bg-warning fs-6">مؤجل</span>
                    {% else %}
                        <span class="badge bg-success fs-6">نشط</span>
                    {% endif %}
                    <button onclick="printMeetingDetails()" class="print-btn">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>



    <!-- معلومات الاجتماع الشاملة -->
    <div class="row">
        <!-- العمود الرئيسي الشامل -->
        <div class="col-lg-8">
            <div class="card shadow-sm mb-3">
                <div class="card-header bg-primary text-white py-2">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        تفاصيل الاجتماع الشاملة
                    </h6>
                </div>
                <div class="card-body p-3">
                    <!-- جدول شامل بجميع التفاصيل -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <tbody>
                                <tr>
                                    <td class="fw-bold bg-light" style="width: 25%;"><i class="fas fa-file-alt text-primary me-2"></i>موضوع الاجتماع</td>
                                    <td>{{ meeting.subject }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold bg-light"><i class="fas fa-tag text-info me-2"></i>نوع الفعالية</td>
                                    <td>{{ meeting.meeting_type }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold bg-light"><i class="fas fa-calendar text-success me-2"></i>التاريخ</td>
                                    <td>{{ meeting.meeting_date.strftime('%Y/%m/%d') }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold bg-light"><i class="fas fa-clock text-warning me-2"></i>الوقت</td>
                                    <td>{{ meeting.meeting_time | military_time }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold bg-light"><i class="fas fa-map-marker-alt text-danger me-2"></i>مكان الاجتماع</td>
                                    <td>{{ meeting.location }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold bg-light"><i class="fas fa-building text-primary me-2"></i>جهة الدعوة</td>
                                    <td>{{ meeting.inviting_party }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold bg-light"><i class="fas fa-user-clock text-info me-2"></i>الحضور قبل الموعد</td>
                                    <td>{{ meeting.arrival_time_before }} دقيقة</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold bg-light"><i class="fas fa-hashtag text-success me-2"></i>رقم الكتاب</td>
                                    <td>{{ meeting.book_number }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold bg-light"><i class="fas fa-calendar-alt text-info me-2"></i>تاريخ الكتاب</td>
                                    <td>{{ meeting.book_date.strftime('%Y/%m/%d') }}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold bg-light"><i class="fas fa-tshirt text-secondary me-2"></i>زي الحضور</td>
                                    <td>{{ meeting.dress_code }}</td>
                                </tr>

                                {% if meeting.notes %}
                                <tr>
                                    <td class="fw-bold bg-light"><i class="fas fa-sticky-note text-warning me-2"></i>ملاحظات إضافية</td>
                                    <td>{{ meeting.notes }}</td>
                                </tr>
                                {% endif %}

                                <tr>
                                    <td class="fw-bold bg-light"><i class="fas fa-paperclip text-info me-2"></i>عدد المرفقات</td>
                                    <td>
                                        {% if attachments %}
                                            {{ attachments|length }} ملف
                                            <small class="text-muted">
                                                {% set total_size = attachments|sum(attribute='file_size') %}
                                                ({{ "%.1f"|format(total_size / 1024) }} KB إجمالي)
                                            </small>
                                        {% else %}
                                            لا توجد مرفقات
                                        {% endif %}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>


                </div>
            </div>
        </div>

        <!-- عمود المرفقات -->
        <div class="col-lg-4">
            <!-- قسم المرفقات مضغوط -->
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center py-2">
                    <h6 class="mb-0">
                        <i class="fas fa-paperclip me-2"></i>
                        المرفقات ({{ attachments|length }})
                    </h6>
                    <button type="button" class="btn btn-sm btn-success" onclick="showAddAttachmentModal({{ meeting.id }})">
                        <i class="fas fa-plus me-1"></i>
                        إضافة
                    </button>
                </div>
                <div class="card-body p-2">
                    {% if attachments %}
                        {% for attachment in attachments %}
                        <div class="attachment-item-compact mb-2 p-2 border rounded">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center flex-grow-1">
                                    {% if attachment.file_type.startswith('image/') %}
                                        <i class="fas fa-image text-success me-2"></i>
                                    {% elif attachment.file_type == 'application/pdf' %}
                                        <i class="fas fa-file-pdf text-danger me-2"></i>
                                    {% elif attachment.file_type.startswith('application/vnd.ms-') or attachment.file_type.startswith('application/vnd.openxmlformats-') %}
                                        <i class="fas fa-file-word text-primary me-2"></i>
                                    {% else %}
                                        <i class="fas fa-file me-2"></i>
                                    {% endif %}
                                    <div class="flex-grow-1">
                                        <div class="fw-bold small text-truncate" style="max-width: 150px;" title="{{ attachment.original_filename }}">
                                            {{ attachment.original_filename }}
                                        </div>
                                        <small class="text-muted">{{ "%.1f"|format(attachment.file_size / 1024) }} KB</small>
                                    </div>
                                </div>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('view_attachment', attachment_id=attachment.id) }}"
                                       class="btn btn-outline-info btn-sm" title="عرض" target="_blank">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('download_attachment', attachment_id=attachment.id) }}"
                                       class="btn btn-outline-primary btn-sm" title="تحميل">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    {% if current_user.id == attachment.uploaded_by %}
                                    <a href="{{ url_for('delete_attachment', attachment_id=attachment.id) }}"
                                       class="btn btn-outline-danger btn-sm"
                                       onclick="return confirm('هل تريد حذف هذا المرفق؟')" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-muted text-center py-3">
                            <i class="fas fa-folder-open fa-2x mb-2 opacity-50"></i>
                            <p class="mb-0 small">لا توجد مرفقات</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>



</div>

<style>
/* تصميم مضغوط للصفحة */
.info-item {
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border-right: 3px solid #1b4332;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.info-item:hover {
    background: #e9ecef;
    transition: all 0.2s ease;
}

/* تحسين الجدول الشامل */
.table-bordered td {
    border: 1px solid #dee2e6 !important;
    padding: 12px !important;
    vertical-align: middle !important;
}

.table-bordered .bg-light {
    background-color: #f8f9fa !important;
    font-weight: 600 !important;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,.02) !important;
}

.table-responsive {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.attachment-item-compact {
    background: #f8f9fa;
    transition: all 0.2s ease;
}

.attachment-item-compact:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* تحسين responsive */
@media (max-width: 768px) {
    .col-lg-4 {
        margin-top: 1rem;
    }

    .table-responsive {
        font-size: 0.85rem;
    }

    .table-bordered td {
        padding: 8px !important;
    }

    .info-item {
        font-size: 0.8rem;
        padding: 6px 10px;
    }

    .attachment-item-compact .fw-bold {
        font-size: 0.8rem;
    }
}
    margin-bottom: 5px;
}

.detail-value {
    font-size: 1.1em;
    color: #333;
    line-height: 1.4;
}

.attachment-item {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    border-right: 4px solid #ffc107;
    transition: all 0.3s ease;
}

.attachment-item:hover {
    background: #e9ecef;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.attachment-item .btn {
    transition: all 0.2s ease;
}

.attachment-item .btn:hover {
    transform: scale(1.1);
}

/* زر الطباعة */
.print-btn {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border: none;
    color: white;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}
.print-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

@media print {
    /* إزالة هوامش الصفحة تماماً */
    @page {
        margin: 0 !important;
        padding: 0 !important;
        size: A4;
    }

    /* إخفاء العناصر غير المطلوبة للطباعة */
    .btn, .btn-group, .modern-back-btn, .card-header.bg-warning, .card-header.bg-info, .print-btn,
    .navbar, .header, .banner, .top-banner, .navigation, .breadcrumb, .sidebar,
    .footer, .bottom-footer, .social-links, .back-to-top, .scroll-top,
    .alert, .toast, .modal, .dropdown, .tooltip, .popover {
        display: none !important;
    }

    /* تحسين التخطيط للطباعة - إزالة جميع الهوامش */
    body {
        font-size: 18px !important;
        line-height: 1.4 !important;
        margin: 0 !important;
        padding: 0 !important;
        font-family: 'Arial', 'Tahoma', sans-serif !important;
        color: #000 !important;
    }

    .container-fluid {
        max-width: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    /* تصميم التقرير المبسط الجديد - بدون هوامش */
    .print-simple-report {
        display: block !important;
        padding: 15px;
        background: white;
        color: black;
        font-family: 'Arial', 'Tahoma', sans-serif;
        page-break-inside: avoid;
        max-height: 100vh;
        overflow: hidden;
        font-size: 12px;
        line-height: 1.4;
        direction: rtl;
        text-align: right;
        width: 100%;
        margin: 0;
        box-shadow: none;
        border: none;
    }

    .print-header-simple {
        text-align: center;
        margin-bottom: 15px;
        border-bottom: 3px solid #1b4332;
        padding-bottom: 10px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 8px 8px 0 0;
        padding: 15px;
    }

    .header-top {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;
        margin-bottom: 8px;
    }

    .logo-print {
        width: 40px;
        height: 40px;
        object-fit: contain;
        border: 2px solid #1b4332;
        border-radius: 50%;
        padding: 2px;
    }

    .header-text .main-title {
        font-size: 16px;
        font-weight: bold;
        color: #1b4332;
        margin: 0;
        text-align: center;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    }

    .header-text .sub-title {
        font-size: 12px;
        color: #2d5a3d;
        margin: 3px 0 0 0;
        text-align: center;
        font-weight: 500;
    }

    .meeting-details-table {
        margin: 3px 0;
    }

    .details-table {
        width: 100%;
        border-collapse: collapse;
        margin: 5px 0;
        font-size: 10px;
        direction: rtl;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .details-table td {
        padding: 4px 8px;
        border: 1px solid #1b4332;
        vertical-align: middle;
        line-height: 1.4;
    }

    .details-table .label {
        background-color: #1b4332;
        color: white;
        font-weight: bold;
        width: 35%;
        text-align: center;
        font-size: 10px;
    }

    .details-table .value {
        background-color: #f8f9fa;
        color: #333;
        text-align: right;
        width: 65%;
        font-size: 10px;
    }

    /* تنسيق قسم المرفقات */
    .attachments-section {
        margin: 10px 0;
        page-break-inside: avoid;
    }

    .section-title {
        font-size: 12px;
        font-weight: bold;
        color: #1b4332;
        margin: 8px 0 4px 0;
        text-align: center;
        border-bottom: 1px solid #1b4332;
        padding-bottom: 2px;
    }

    /* تنسيق التذييل */
    .print-footer {
        margin-top: 15px;
        border-top: 2px solid #1b4332;
        padding-top: 8px;
        text-align: center;
    }

    .footer-info {
        font-size: 9px;
        color: #666;
        line-height: 1.3;
    }

    .footer-info p {
        margin: 2px 0;
    }

    .print-footer {
        margin-top: 8px;
        text-align: center;
        border-top: 1px solid #1b4332;
        padding-top: 4px;
        font-size: 7px;
        color: #666;
    }

    .print-footer p {
        margin: 1px 0;
    }

    /* تحسين البطاقات للطباعة */
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
        margin-bottom: 10px !important;
        page-break-inside: avoid !important;
    }

    .card-body {
        padding: 8px !important;
    }

    /* تحسين عرض المعلومات */
    .info-item {
        padding: 4px 6px !important;
        margin-bottom: 4px !important;
        font-size: 10px !important;
        border-right: 2px solid #1b4332 !important;
        background: #f8f9fa !important;
    }

    /* تحسين المرفقات للطباعة */
    .attachment-item-compact {
        padding: 4px 6px !important;
        margin-bottom: 3px !important;
        font-size: 9px !important;
        border: 1px solid #ddd !important;
        background: #f8f9fa !important;
    }

    /* تحسين التخطيط الشبكي */
    .row {
        margin: 0 !important;
    }

    .col-lg-8, .col-lg-4 {
        padding: 0 5px !important;
    }



    /* منع تقطيع الصفحات */
    .no-break {
        page-break-inside: avoid !important;
    }

    /* إخفاء العناصر التفاعلية */
    .modal, .dropdown, .tooltip, .popover {
        display: none !important;
    }

    /* تحسينات إضافية للطباعة */
    .printing .print-simple-report {
        display: block !important;
    }

    .printing .btn,
    .printing .btn-group,
    .printing .modern-back-btn,
    .printing .row,
    .printing .card,
    .printing .container-fluid > *:not(.print-simple-report) {
        display: none !important;
    }

    /* تحسين عرض الحالة للطباعة */
    .badge {
        background: #f0f0f0 !important;
        color: #333 !important;
        border: 1px solid #ddd !important;
        padding: 2px 6px !important;
        font-size: 10px !important;
    }

    /* تحسين الأيقونات للطباعة */
    .fas, .far, .fab {
        font-size: 10px !important;
    }

    /* ضمان عدم تقطيع المحتوى */
    .card, .info-item, .attachment-item-compact, table {
        page-break-inside: avoid !important;
    }

    /* تحسين التباعد للطباعة */
    .mb-3, .mb-2 {
        margin-bottom: 10px !important;
    }

    .p-3, .p-2 {
        padding: 8px !important;
    }

    /* تحسين وضوح النص */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    /* تحسين الهوامش للصفحة الواحدة */
    @page {
        margin: 0.3cm !important;
        size: A4 !important;
    }

    /* ضمان أن التقرير يبقى في صفحة واحدة */
    .print-simple-report * {
        page-break-inside: avoid !important;
        page-break-after: avoid !important;
        page-break-before: avoid !important;
    }

    /* إزالة التحجيم المكرر لأنه موجود في التعريف الأساسي */

    /* تحسين عرض الأيقونات */
    .fas, .far, .fab {
        font-size: 18px !important;
        margin-left: 5px !important;
    }
}
</style>

<script>
function editMeeting(meetingId) {
    window.location.href = `/edit_meeting/${meetingId}`;
}

function printMeetingDetails() {
    console.log('🖨️ طباعة تقرير تفاصيل الاجتماع من صفحة التفاصيل...');

    // الحصول على معرف الاجتماع من URL أو من البيانات
    const meetingId = {{ meeting.id }};
    console.log('🔍 معرف الاجتماع:', meetingId);

    // جلب البيانات الحقيقية من قاعدة البيانات عبر API
    fetch(`/api/meeting/${meetingId}`)
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            console.log('✅ تم جلب بيانات الاجتماع الحقيقية بنجاح:', data.meeting.subject);

            // طباعة تقرير تفاصيل الاجتماع المحسن بالبيانات الحقيقية
            printUnifiedMeetingReport(data.meeting);
        } else {
            throw new Error(data.message || 'خطأ في جلب البيانات');
        }
    })
    .catch(error => {
        console.error('❌ خطأ في جلب بيانات الاجتماع:', error);
        console.log('🔄 استخدام البيانات المتاحة في الصفحة...');

        // استخدام البيانات المتاحة في الصفحة كبديل
        const fallbackMeeting = {
            subject: `{{ meeting.subject|safe }}`,
            meeting_type: `{{ meeting.meeting_type|safe }}`,
            meeting_date: `{{ meeting.meeting_date.strftime("%Y/%m/%d") if meeting.meeting_date else "" }}`,
            meeting_time: `{{ meeting.meeting_time.strftime("%H:%M") if meeting.meeting_time else "" }}`,
            location: `{{ meeting.location|safe }}`,
            inviting_party: `{{ meeting.inviting_party|safe }}`,
            arrival_time_before: `{{ meeting.arrival_time_before|safe }}`,
            book_number: `{{ meeting.book_number|safe }}`,
            book_date: `{{ meeting.book_date.strftime("%Y-%m-%d") if meeting.book_date else "" }}`,
            dress_code: `{{ meeting.dress_code|safe }}`,
            notes: `{{ meeting.notes|safe if meeting.notes else "" }}`,
            is_cancelled: {{ 'true' if meeting.is_cancelled else 'false' }},
            is_postponed: {{ 'true' if meeting.is_postponed else 'false' }}
        };

        printUnifiedMeetingReport(fallbackMeeting);
    });
}

// دالة إنشاء تقرير تفاصيل الاجتماع المحسن (نسخة محدثة)
function printUnifiedMeetingReport(meeting) {
    console.log('🖨️ إنشاء تقرير تفاصيل الاجتماع المحسن');

    // تحويل التاريخ إلى صيغة YYYY/MM/DD
    const formatDate = (dateStr) => {
        if (!dateStr) return 'غير محدد';
        const date = new Date(dateStr);
        return date.toISOString().split('T')[0].replace(/-/g, '/');
    };

    // تحويل الوقت إلى صيغة مقروءة
    const formatTime = (timeStr) => {
        if (!timeStr) return 'غير محدد';
        return timeStr;
    };

    // تحويل الوقت إلى النظام العسكري
    const formatTimeWithPeriod = (timeStr) => {
        if (!timeStr) return 'غير محدد';

        // تحويل الوقت إلى ساعة ودقيقة
        const [hours, minutes] = timeStr.split(':');
        const hour = parseInt(hours);
        const minute = minutes || '00';

        // تنسيق النظام العسكري: HHMM (بدون نقطتين)
        return `${hour.toString().padStart(2, '0')}${minute.toString().padStart(2, '0')}`;
    };

    // تحديد الحالة الحقيقية للاجتماع
    const getMeetingStatus = (meeting) => {
        if (meeting.is_cancelled) return 'ملغي';
        if (meeting.is_postponed) return 'مؤجل';

        // فحص إذا كان الاجتماع منتهي بناءً على التاريخ
        const today = new Date();
        const meetingDate = new Date(meeting.meeting_date);

        if (meetingDate < today) {
            return 'منتهي';
        }

        return 'نشط';
    };

    // إنشاء HTML للتقرير المحسن
    const reportHTML = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>تفاصيل الاجتماع</title>
            <style>
                body {
                    font-family: 'Cairo', 'Tahoma', 'Arial', sans-serif;
                    direction: rtl;
                    margin: 0;
                    padding: 15px;
                    background: #f8f9fa;
                    line-height: 1.4;
                    font-size: 14px;
                }
                .report-container {
                    max-width: 210mm;
                    min-height: 297mm;
                    margin: 0 auto;
                    background: white;
                    border-radius: 10px;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                    overflow: hidden;
                    page-break-inside: avoid;
                }
                .header {
                    background: linear-gradient(135deg, #1B4332 0%, #2d5a3d 100%);
                    color: white;
                    padding: 20px;
                    text-align: center;
                }
                .header h1 {
                    margin: 0;
                    font-size: 24px;
                    font-weight: bold;
                }
                .header .subtitle {
                    margin: 8px 0 0 0;
                    font-size: 14px;
                    opacity: 0.9;
                }
                .content {
                    padding: 25px;
                }
                .details-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 20px;
                    margin-bottom: 20px;
                }
                .detail-section {
                    background: #f8f9fa;
                    border-radius: 8px;
                    padding: 15px;
                    border-right: 3px solid #1B4332;
                }
                .detail-item {
                    display: flex;
                    align-items: center;
                    margin-bottom: 10px;
                    padding: 8px;
                    background: white;
                    border-radius: 6px;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
                }
                .detail-item:last-child {
                    margin-bottom: 0;
                }
                .detail-icon {
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-left: 12px;
                    font-size: 14px;
                    color: white;
                    flex-shrink: 0;
                }
                .detail-content {
                    flex: 1;
                }
                .detail-label {
                    font-weight: bold;
                    color: #1B4332;
                    font-size: 12px;
                    margin-bottom: 2px;
                }
                .detail-value {
                    color: #333;
                    font-size: 14px;
                }
                .icon-blue { background: linear-gradient(135deg, #007bff, #0056b3); }
                .icon-green { background: linear-gradient(135deg, #28a745, #1e7e34); }
                .icon-orange { background: linear-gradient(135deg, #fd7e14, #e55a00); }
                .icon-purple { background: linear-gradient(135deg, #6f42c1, #5a32a3); }
                .icon-red { background: linear-gradient(135deg, #dc3545, #c82333); }
                .icon-teal { background: linear-gradient(135deg, #20c997, #17a2b8); }
                .footer {
                    background: #f8f9fa;
                    padding: 15px 25px;
                    text-align: center;
                    border-top: 1px solid #dee2e6;
                    color: #666;
                    font-size: 12px;
                }
                @media print {
                    body {
                        background: white;
                        padding: 0;
                        margin: 0;
                        font-size: 12px;
                    }
                    .report-container {
                        box-shadow: none;
                        max-width: 100%;
                        margin: 0;
                        border-radius: 0;
                        page-break-inside: avoid;
                    }
                }
            </style>
        </head>
        <body>
            <div class="report-container">
                <div class="header">
                    <h1>تفاصيل الاجتماع</h1>
                </div>

                <div class="content">
                    <div class="details-grid">
                        <div class="detail-section">
                            <h3 style="color: #1B4332; margin-bottom: 15px; text-align: center; border-bottom: 3px solid #1B4332; padding-bottom: 8px; font-size: 18px; font-weight: bold;">تفاصيل الاجتماع</h3>

                            <div class="detail-item">
                                <div class="detail-icon icon-blue">📋</div>
                                <div class="detail-content">
                                    <div class="detail-label">الموضوع</div>
                                    <div class="detail-value">${meeting.subject || 'غير محدد'}</div>
                                </div>
                            </div>

                            <div class="detail-item">
                                <div class="detail-icon icon-green">🏢</div>
                                <div class="detail-content">
                                    <div class="detail-label">نوع الفعالية</div>
                                    <div class="detail-value">${meeting.meeting_type || 'اجتماع'}</div>
                                </div>
                            </div>

                            <div class="detail-item">
                                <div class="detail-icon icon-orange">📍</div>
                                <div class="detail-content">
                                    <div class="detail-label">المكان</div>
                                    <div class="detail-value">${meeting.location || 'قاعة الاجتماعات'}</div>
                                </div>
                            </div>

                            <div class="detail-item">
                                <div class="detail-icon icon-red">📅</div>
                                <div class="detail-content">
                                    <div class="detail-label">التاريخ</div>
                                    <div class="detail-value">${formatDate(meeting.meeting_date)}</div>
                                </div>
                            </div>

                            <div class="detail-item">
                                <div class="detail-icon icon-teal">🕐</div>
                                <div class="detail-content">
                                    <div class="detail-label">الوقت</div>
                                    <div class="detail-value">${formatTimeWithPeriod(meeting.meeting_time)}</div>
                                </div>
                            </div>
                        </div>

                        <div class="detail-section">
                            <h3 style="color: #1B4332; margin-bottom: 15px; text-align: center; border-bottom: 3px solid #1B4332; padding-bottom: 8px; font-size: 18px; font-weight: bold;">جهة الدعوة</h3>

                            <div class="detail-item">
                                <div class="detail-icon icon-purple">🏛️</div>
                                <div class="detail-content">
                                    <div class="detail-label">الجهة</div>
                                    <div class="detail-value">${meeting.inviting_party || 'مديرية الدائرة المالية'}</div>
                                </div>
                            </div>

                            <div class="detail-item">
                                <div class="detail-icon icon-green">👔</div>
                                <div class="detail-content">
                                    <div class="detail-label">نوع اللباس</div>
                                    <div class="detail-value">${meeting.dress_code || 'رسمي'}</div>
                                </div>
                            </div>

                            <div class="detail-item">
                                <div class="detail-icon icon-orange">⏰</div>
                                <div class="detail-content">
                                    <div class="detail-label">الحضور قبل الموعد</div>
                                    <div class="detail-value">${meeting.arrival_time_before || 15} دقيقة</div>
                                </div>
                            </div>

                            <div class="detail-item">
                                <div class="detail-icon icon-blue">📋</div>
                                <div class="detail-content">
                                    <div class="detail-label">الحالة</div>
                                    <div class="detail-value">${getMeetingStatus(meeting)}</div>
                                </div>
                            </div>

                            <div class="detail-item">
                                <div class="detail-icon icon-red">📍</div>
                                <div class="detail-content">
                                    <div class="detail-label">المكان</div>
                                    <div class="detail-value">${meeting.location || 'قاعة الاجتماعات'}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    ${meeting.book_number || meeting.notes ? `
                    <div class="detail-section" style="grid-column: 1 / -1; margin-top: 15px;">
                        <h3 style="color: #1B4332; margin-bottom: 15px; text-align: center; border-bottom: 3px solid #1B4332; padding-bottom: 8px; font-size: 18px; font-weight: bold;">معلومات إضافية</h3>

                        ${meeting.book_number ? `
                        <div class="detail-item">
                            <div class="detail-icon icon-purple">📄</div>
                            <div class="detail-content">
                                <div class="detail-label">رقم الكتاب</div>
                                <div class="detail-value">${meeting.book_number}</div>
                            </div>
                        </div>
                        ` : ''}

                        ${meeting.book_date ? `
                        <div class="detail-item">
                            <div class="detail-icon icon-green">📅</div>
                            <div class="detail-content">
                                <div class="detail-label">تاريخ الكتاب</div>
                                <div class="detail-value">${formatDate(meeting.book_date)}</div>
                            </div>
                        </div>
                        ` : ''}

                        ${meeting.notes ? `
                        <div class="detail-item">
                            <div class="detail-icon icon-orange">📝</div>
                            <div class="detail-content">
                                <div class="detail-label">ملاحظات</div>
                                <div class="detail-value">${meeting.notes}</div>
                            </div>
                        </div>
                        ` : ''}
                    </div>
                    ` : ''}
                </div>

                <div class="footer">
                    تاريخ الطباعة: ${new Date().toISOString().split('T')[0].replace(/-/g, '/')}
                </div>
            </div>
        </body>
        </html>
    `;

    // فتح نافذة طباعة جديدة
    const printWindow = window.open('', '_blank', 'width=900,height=700');
    if (!printWindow) {
        console.error('❌ تعذر فتح نافذة الطباعة');
        alert('تعذر فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.');
        return;
    }

    console.log('✅ تم فتح نافذة الطباعة بنجاح');
    printWindow.document.write(reportHTML);
    printWindow.document.close();

    // انتظار تحميل المحتوى ثم الطباعة
    printWindow.onload = function() {
        setTimeout(() => {
            printWindow.focus();
            printWindow.print();
            console.log('✅ تم تشغيل طباعة تقرير تفاصيل الاجتماع من صفحة التفاصيل');
        }, 500);
    };

    console.log('✅ تم إنشاء التقرير المحسن بنجاح من صفحة التفاصيل');
}

function exportMeetingPDF(meetingId) {
    window.open(`/api/meeting/${meetingId}/export_pdf`, '_blank');
}

// وظائف المرفقات الجديدة
function showAddAttachmentModal(meetingId) {
    document.getElementById('addAttachmentForm').setAttribute('data-meeting-id', meetingId);

    // إعادة تعيين النموذج
    document.getElementById('newAttachments').value = '';
    document.getElementById('selectedFilesInfo').innerHTML = '';

    const modal = new bootstrap.Modal(document.getElementById('addAttachmentModal'));
    modal.show();

    // تفعيل رفع الملفات
    setupFileUpload();
}

function setupFileUpload() {
    const fileInput = document.getElementById('newAttachments');
    const fileInfo = document.getElementById('selectedFilesInfo');

    fileInput.addEventListener('change', function() {
        if (this.files.length > 0) {
            let html = '<div class="alert alert-info"><h6>الملفات المختارة:</h6><ul class="mb-0">';

            for (let file of this.files) {
                const fileSize = (file.size / 1024).toFixed(1);
                html += `<li><strong>${file.name}</strong> (${fileSize} KB)</li>`;
            }

            html += '</ul></div>';
            fileInfo.innerHTML = html;
        } else {
            fileInfo.innerHTML = '';
        }
    });
}

function uploadNewAttachments() {
    const form = document.getElementById('addAttachmentForm');
    const meetingId = form.getAttribute('data-meeting-id');
    const fileInput = document.getElementById('newAttachments');

    if (fileInput.files.length === 0) {
        alert('يرجى اختيار ملف واحد على الأقل');
        return;
    }

    const formData = new FormData();
    for (let file of fileInput.files) {
        formData.append('attachments', file);
    }

    // إظهار رسالة تحميل
    const uploadBtn = event.target;
    const originalText = uploadBtn.innerHTML;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الرفع...';
    uploadBtn.disabled = true;

    fetch(`/api/meeting/${meetingId}/add_attachments`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم رفع المرفقات بنجاح!');
            location.reload();
        } else {
            alert('خطأ في رفع المرفقات: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('خطأ في الاتصال بالخادم');
    })
    .finally(() => {
        uploadBtn.innerHTML = originalText;
        uploadBtn.disabled = false;
    });
}
</script>

<!-- مودال إضافة مرفق جديد -->
<div class="modal fade" id="addAttachmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background: var(--jaf-gradient); color: white;">
                <h5 class="modal-title">
                    <i class="fas fa-paperclip me-2"></i>
                    إضافة مرفق جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addAttachmentForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label class="form-label">اختر الملفات</label>
                        <div class="file-upload-area-modal" id="fileUploadAreaModal" onclick="document.getElementById('newAttachments').click()">
                            <i class="fas fa-cloud-upload-alt" style="font-size: 2rem; color: #6c757d; margin-bottom: 1rem;"></i>
                            <h6>اسحب الملفات هنا أو انقر للاختيار</h6>
                            <p class="text-muted mb-0 small">يُقبل ملفات PDF, Word, والصور</p>
                            <input type="file" id="newAttachments" name="attachments" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" multiple style="display: none;">
                        </div>
                        <div id="selectedFilesInfo" class="mt-3"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="uploadNewAttachments()">
                    <i class="fas fa-upload me-1"></i>
                    رفع المرفقات
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.file-upload-area-modal {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafafa;
}

.file-upload-area-modal:hover {
    border-color: #28a745;
    background-color: rgba(40, 167, 69, 0.05);
}
</style>

{% endblock %}

{% block extra_js %}
<!-- قالب الطباعة الموحد -->
<script src="{{ url_for('static', filename='js/unified-print-template.js') }}"></script>
{% endblock %}
