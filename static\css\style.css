/* ألوان القوات المسلحة الأردنية */
:root {
    --jaf-primary: #1B4332;      /* أخضر داكن */
    --jaf-secondary: #2D6A4F;    /* أخضر متوسط */
    --jaf-success: #FFD700;      /* ذهبي للنجاح */
    --jaf-info: #FFA500;         /* برتقالي ذهبي للمعلومات */
    --jaf-warning: #F77F00;      /* برتقالي */
    --jaf-danger: #D62828;       /* أحمر */
    --jaf-light: #F8F9FA;        /* رمادي فاتح */
    --jaf-dark: #212529;         /* رمادي داكن */
    --jaf-gold: #FFD60A;         /* ذهبي */
    --jaf-gradient: linear-gradient(135deg, var(--jaf-primary) 0%, var(--jaf-secondary) 100%);
}

/* الخط الأساسي */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* شريط التنقل */
.jaf-navbar {
    background: var(--jaf-gradient);
    box-shadow: 0 2px 10px rgba(27, 67, 50, 0.3);
    padding: 0.8rem 0;
    min-height: 75px;
}

.jaf-navbar .navbar-brand {
    font-weight: 700;
    color: #DAA520 !important;
    font-size: 1.4rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.jaf-navbar .navbar-brand img {
    border-radius: 8px;
    transition: transform 0.3s ease;
    width: 60px !important;
    height: 60px !important;
}

.jaf-navbar .navbar-brand:hover img {
    transform: scale(1.05);
}

.jaf-navbar .nav-link {
    color: #DAA520 !important;
    font-weight: 600;
    font-size: 1.1rem;
    padding: 0.7rem 1.2rem !important;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

.jaf-navbar .nav-link:hover,
.jaf-navbar .nav-link.active {
    background-color: rgba(218, 165, 32, 0.2);
    color: #FFD700 !important;
    transform: translateY(-1px);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.4);
    box-shadow: 0 2px 8px rgba(218, 165, 32, 0.3);
}

/* تضليل التبويبة النشطة */
.jaf-navbar .nav-link.current-page {
    background: linear-gradient(135deg, rgba(218, 165, 32, 0.4), rgba(255, 215, 0, 0.3));
    color: #FFD700 !important;
    border: 2px solid rgba(218, 165, 32, 0.6);
    box-shadow: 0 6px 20px rgba(218, 165, 32, 0.5);
    transform: translateY(-3px);
    font-weight: 700;
    position: relative;
    z-index: 10;
}

.jaf-navbar .nav-link.current-page::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 3px;
    background: linear-gradient(90deg, #DAA520, #FFD700, #DAA520);
    border-radius: 2px;
    animation: activeTabGlow 2s ease-in-out infinite;
}

@keyframes activeTabGlow {
    0%, 100% {
        opacity: 0.8;
        box-shadow: 0 0 10px rgba(218, 165, 32, 0.5);
    }
    50% {
        opacity: 1;
        box-shadow: 0 0 20px rgba(218, 165, 32, 0.8);
    }
}

/* تأثير hover للتبويبة النشطة */
.jaf-navbar .nav-link.current-page:hover {
    background: linear-gradient(135deg, rgba(218, 165, 32, 0.5), rgba(255, 215, 0, 0.4));
    box-shadow: 0 8px 25px rgba(218, 165, 32, 0.6);
    transform: translateY(-4px) scale(1.02);
}

/* منع التأثيرات العادية على التبويبة النشطة */
.jaf-navbar .nav-link.current-page:not(:hover) {
    transition: all 0.3s ease;
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    padding-top: 105px;
    padding-bottom: 2rem;
}

/* البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: var(--jaf-gradient);
    color: white;
    border: none;
    padding: 1.25rem;
    font-weight: 600;
}

.card-body {
    padding: 2rem;
}

/* الأزرار */
.btn-jaf-primary {
    background: var(--jaf-gradient);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-jaf-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(27, 67, 50, 0.4);
    color: white;
}

.btn-jaf-secondary {
    background-color: var(--jaf-secondary);
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-jaf-secondary:hover {
    background-color: var(--jaf-primary);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(45, 106, 79, 0.4);
    color: white;
}

.btn-jaf-gold {
    background-color: var(--jaf-gold);
    border: none;
    color: var(--jaf-dark);
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.btn-jaf-gold:hover {
    background-color: #e6c200;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 214, 10, 0.4);
    color: var(--jaf-dark);
}

/* النماذج */
.form-control {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--jaf-primary);
    box-shadow: 0 0 0 0.2rem rgba(27, 67, 50, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--jaf-dark);
    margin-bottom: 0.5rem;
}

/* الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background: var(--jaf-gradient);
    color: white;
    border: none;
    font-weight: 600;
    padding: 1rem;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(27, 67, 50, 0.05);
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: 10px;
    padding: 1rem 1.5rem;
    font-weight: 500;
}

.alert-success {
    background-color: rgba(64, 145, 108, 0.1);
    color: var(--jaf-success);
    border-left: 4px solid var(--jaf-success);
}

.alert-danger {
    background-color: rgba(214, 40, 40, 0.1);
    color: var(--jaf-danger);
    border-left: 4px solid var(--jaf-danger);
}

.alert-info {
    background-color: rgba(82, 183, 136, 0.1);
    color: var(--jaf-info);
    border-left: 4px solid var(--jaf-info);
}

.alert-warning {
    background-color: rgba(247, 127, 0, 0.1);
    color: var(--jaf-warning);
    border-left: 4px solid var(--jaf-warning);
}

/* إشعارات منبثقة مخصصة */
.alert.position-fixed {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideInLeft 0.5s ease-out;
}

.alert.position-fixed.alert-success {
    background: rgba(64, 145, 108, 0.9);
    color: white;
    border-left: 4px solid #40916C;
}

.alert.position-fixed.alert-danger {
    background: rgba(214, 40, 40, 0.9);
    color: white;
    border-left: 4px solid #D62828;
}

.alert.position-fixed.alert-info {
    background: rgba(82, 183, 136, 0.9);
    color: white;
    border-left: 4px solid #52B788;
}

.alert.position-fixed.alert-warning {
    background: rgba(247, 127, 0, 0.9);
    color: white;
    border-left: 4px solid #F77F00;
}

/* تأثير الانزلاق من اليسار */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* التذييل */
.jaf-footer {
    background: var(--jaf-gradient);
    color: white;
    padding: 1.5rem 0;
    margin-top: auto;
}

/* الأيقونات التفاعلية */
.icon-interactive {
    transition: all 0.3s ease;
    cursor: pointer;
}

.icon-interactive:hover {
    transform: scale(1.2);
    color: var(--jaf-gold);
}

/* بطاقات الإحصائيات */
.stats-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--jaf-gradient);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--jaf-primary);
    margin-bottom: 0.5rem;
}

.stats-label {
    color: var(--jaf-dark);
    font-weight: 600;
    font-size: 1.1rem;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .main-content {
        padding-top: 80px;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .btn-jaf-primary,
    .btn-jaf-secondary,
    .btn-jaf-gold {
        padding: 0.5rem 1.5rem;
        font-size: 0.9rem;
    }
    
    .stats-number {
        font-size: 2rem;
    }
}

/* تأثيرات الحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* تخصيص شريط التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--jaf-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--jaf-secondary);
}

/* ===== تصميم التقارير الاحترافي ===== */

/* شبكة الإحصائيات الاحترافية */
.professional-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
    padding: 1rem;
}

/* البطاقة الإحصائية الاحترافية */
.professional-stat-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 24px;
    padding: 0;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 8px 16px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(20px);
    min-height: 280px;
}

.professional-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 50%,
        rgba(0, 0, 0, 0.02) 100%);
    pointer-events: none;
    transition: opacity 0.3s ease;
    opacity: 0;
}

.professional-stat-card:hover::before {
    opacity: 1;
}

.professional-stat-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow:
        0 30px 60px rgba(0, 0, 0, 0.15),
        0 12px 24px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* نمط الخلفية */
.card-background-pattern {
    position: absolute;
    top: 0;
    right: 0;
    width: 120px;
    height: 120px;
    background: linear-gradient(45deg,
        rgba(27, 67, 50, 0.03) 0%,
        rgba(45, 106, 79, 0.05) 100%);
    border-radius: 0 24px 0 100%;
    transition: all 0.3s ease;
}

.champion-pattern {
    background: linear-gradient(45deg,
        rgba(255, 214, 10, 0.1) 0%,
        rgba(218, 165, 32, 0.15) 100%);
    animation: championShimmer 3s ease-in-out infinite;
}

@keyframes championShimmer {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

/* قسم رأس البطاقة */
.card-header-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 1.5rem 1.5rem 0;
    position: relative;
    z-index: 2;
}

.stat-icon-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-icon {
    width: 64px;
    height: 64px;
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    color: white;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.icon-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 1;
}

.professional-stat-card:hover .icon-glow {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1.2);
}

/* ألوان الأيقونات */
.primary-icon {
    background: linear-gradient(135deg, var(--jaf-primary) 0%, var(--jaf-secondary) 100%);
}

.primary-icon + .icon-glow {
    background: radial-gradient(circle, var(--jaf-primary) 0%, transparent 70%);
}

.success-icon {
    background: linear-gradient(135deg, #27AE60 0%, #2ECC71 100%);
}

.success-icon + .icon-glow {
    background: radial-gradient(circle, #27AE60 0%, transparent 70%);
}

.info-icon {
    background: linear-gradient(135deg, #3498DB 0%, #5DADE2 100%);
}

.info-icon + .icon-glow {
    background: radial-gradient(circle, #3498DB 0%, transparent 70%);
}

.warning-icon {
    background: linear-gradient(135deg, #E74C3C 0%, #EC7063 100%);
}

.warning-icon + .icon-glow {
    background: radial-gradient(circle, #E74C3C 0%, transparent 70%);
}

.postponed-icon {
    background: linear-gradient(135deg, #F39C12 0%, #E67E22 100%);
}

.postponed-icon + .icon-glow {
    background: radial-gradient(circle, #F39C12 0%, transparent 70%);
}

.secondary-icon {
    background: linear-gradient(135deg, #9B59B6 0%, #BB8FCE 100%);
}

.secondary-icon + .icon-glow {
    background: radial-gradient(circle, #9B59B6 0%, transparent 70%);
}

.champion-icon {
    background: linear-gradient(135deg, var(--jaf-gold) 0%, #F39C12 100%);
    animation: championIconGlow 2s ease-in-out infinite;
}

.champion-icon + .icon-glow {
    background: radial-gradient(circle, var(--jaf-gold) 0%, transparent 70%);
}

@keyframes championIconGlow {
    0%, 100% {
        box-shadow: 0 8px 16px rgba(255, 214, 10, 0.3);
    }
    50% {
        box-shadow: 0 12px 24px rgba(255, 214, 10, 0.5);
    }
}

/* الشارات */
.stat-badge {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--jaf-primary);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.upcoming-badge {
    color: #27AE60;
    background: rgba(39, 174, 96, 0.1);
    border-color: rgba(39, 174, 96, 0.2);
}

.completed-badge {
    color: #3498DB;
    background: rgba(52, 152, 219, 0.1);
    border-color: rgba(52, 152, 219, 0.2);
}

.cancelled-badge {
    color: #E74C3C;
    background: rgba(231, 76, 60, 0.1);
    border-color: rgba(231, 76, 60, 0.2);
}

.average-badge {
    color: #9B59B6;
    background: rgba(155, 89, 182, 0.1);
    border-color: rgba(155, 89, 182, 0.2);
}

.postponed-badge {
    color: #F39C12;
    background: rgba(243, 156, 18, 0.1);
    border-color: rgba(243, 156, 18, 0.2);
}

.champion-badge {
    color: var(--jaf-gold);
    background: linear-gradient(135deg, rgba(255, 214, 10, 0.15), rgba(218, 165, 32, 0.1));
    border-color: rgba(255, 214, 10, 0.3);
    animation: championBadgeGlow 2s ease-in-out infinite;
}

@keyframes championBadgeGlow {
    0%, 100% {
        box-shadow: 0 4px 8px rgba(255, 214, 10, 0.2);
    }
    50% {
        box-shadow: 0 6px 12px rgba(255, 214, 10, 0.4);
    }
}

/* قسم المحتوى */
.card-content-section {
    padding: 1rem 1.5rem;
    position: relative;
    z-index: 2;
}

.stat-number-container {
    position: relative;
    margin-bottom: 1rem;
}

.stat-number {
    font-size: 3rem;
    font-weight: 900;
    color: var(--jaf-dark);
    line-height: 1;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.champion-number {
    background: linear-gradient(135deg, var(--jaf-gold), #F39C12);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: championNumberShine 3s ease-in-out infinite;
}

@keyframes championNumberShine {
    0%, 100% { opacity: 0.9; }
    50% { opacity: 1; }
}

.number-animation-line {
    position: absolute;
    bottom: -4px;
    left: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--jaf-primary), var(--jaf-secondary));
    border-radius: 2px;
    width: 0;
    transition: width 0.8s ease;
}

.champion-line {
    background: linear-gradient(90deg, var(--jaf-gold), #F39C12);
}

.professional-stat-card:hover .number-animation-line {
    width: 60%;
}

.stat-info {
    margin-bottom: 1rem;
}

.stat-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--jaf-primary);
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.stat-subtitle {
    font-size: 0.9rem;
    color: #6c757d;
    line-height: 1.4;
    margin: 0;
}

/* قسم التذييل */
.card-footer-section {
    padding: 0 1.5rem 1.5rem;
    position: relative;
    z-index: 2;
}

/* أزرار العمل في البطاقات */
.card-action-buttons {
    display: flex;
    gap: 8px;
    margin-left: 12px;
    opacity: 1;
    transform: translateX(0);
    transition: all 0.3s ease;
}

.action-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.action-btn:hover::before {
    opacity: 1;
}

.print-btn {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.print-btn:hover {
    background: linear-gradient(135deg, #495057, #343a40);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(108, 117, 125, 0.4);
}

.pdf-btn {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.pdf-btn:hover {
    background: linear-gradient(135deg, #c82333, #bd2130);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(220, 53, 69, 0.4);
}

.action-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* تحسين تخطيط stat-meta */
.stat-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
}

.progress-indicator {
    background: rgba(0, 0, 0, 0.05);
    height: 6px;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-bar {
    height: 100%;
    border-radius: 3px;
    transition: width 1s ease;
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.3) 50%,
        transparent 100%);
    animation: progressShine 2s ease-in-out infinite;
}

@keyframes progressShine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.upcoming-progress {
    background: linear-gradient(90deg, #27AE60, #2ECC71);
}

.completed-progress {
    background: linear-gradient(90deg, #3498DB, #5DADE2);
}

.cancelled-progress {
    background: linear-gradient(90deg, #E74C3C, #EC7063);
}

.average-progress {
    background: linear-gradient(90deg, #9B59B6, #BB8FCE);
}

.postponed-progress {
    background: linear-gradient(90deg, #F39C12, #E67E22);
}

.champion-progress {
    background: linear-gradient(90deg, var(--jaf-gold), #F39C12);
    animation: championProgressGlow 2s ease-in-out infinite;
}

@keyframes championProgressGlow {
    0%, 100% {
        box-shadow: 0 0 10px rgba(255, 214, 10, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 214, 10, 0.6);
    }
}

.stat-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.trend-text {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    font-weight: 600;
    color: #6c757d;
    transition: all 0.3s ease;
}

.champion-trend {
    color: var(--jaf-gold);
    animation: championTrendGlow 2s ease-in-out infinite;
}

@keyframes championTrendGlow {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

/* بطاقة الإحصائية المحسنة */
.stat-card {
    background: white;
    border-radius: 20px;
    padding: 0;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, var(--jaf-primary), var(--jaf-secondary), var(--jaf-gold));
    transition: height 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.stat-card:hover::before {
    height: 8px;
}

/* أنواع البطاقات المختلفة */
.primary-card {
    background: linear-gradient(135deg, rgba(27, 67, 50, 0.05), rgba(45, 106, 79, 0.02));
}

.success-card {
    background: linear-gradient(135deg, rgba(64, 145, 108, 0.05), rgba(82, 183, 136, 0.02));
}

.info-card {
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.05), rgba(41, 128, 185, 0.02));
}

.warning-card {
    background: linear-gradient(135deg, rgba(247, 127, 0, 0.05), rgba(230, 126, 34, 0.02));
}

.secondary-card {
    background: linear-gradient(135deg, rgba(155, 89, 182, 0.05), rgba(142, 68, 173, 0.02));
}

.special-card {
    background: linear-gradient(135deg, rgba(255, 214, 10, 0.1), rgba(218, 165, 32, 0.05));
}

/* محتوى البطاقة */
.stat-card {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    gap: 1rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.stat-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), transparent);
    transition: opacity 0.3s ease;
    opacity: 0;
}

.stat-card:hover .stat-icon::before {
    opacity: 1;
}

/* ألوان الأيقونات */
.primary-card .stat-icon {
    background: linear-gradient(135deg, var(--jaf-primary), var(--jaf-secondary));
}

.success-card .stat-icon {
    background: linear-gradient(135deg, #40916C, #52B788);
}

.info-card .stat-icon {
    background: linear-gradient(135deg, #3498DB, #2980B9);
}

.warning-card .stat-icon {
    background: linear-gradient(135deg, var(--jaf-warning), #E67E22);
}

.secondary-card .stat-icon {
    background: linear-gradient(135deg, #9B59B6, #8E44AD);
}

.special-card .stat-icon {
    background: linear-gradient(135deg, var(--jaf-gold), #DAA520);
}

/* محتوى النص */
.stat-content {
    flex: 1;
    min-width: 0;
}

.stat-number {
    font-size: 2.2rem;
    font-weight: 800;
    color: var(--jaf-dark);
    margin-bottom: 0.25rem;
    line-height: 1;
    transition: all 0.3s ease;
}

.stat-label {
    font-size: 1rem;
    font-weight: 600;
    color: var(--jaf-primary);
    margin-bottom: 0.25rem;
    line-height: 1.2;
}

.stat-description {
    font-size: 0.85rem;
    color: #6c757d;
    line-height: 1.3;
    opacity: 0.8;
}

/* مؤشر الاتجاه */
.stat-trend {
    width: 40px;
    height: 40px;
    flex-shrink: 0;
}

.trend-indicator {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    color: white;
    transition: all 0.3s ease;
}

.trend-indicator.positive {
    background: linear-gradient(135deg, #27AE60, #2ECC71);
}

.trend-indicator.upcoming {
    background: linear-gradient(135deg, #3498DB, #5DADE2);
}

.trend-indicator.completed {
    background: linear-gradient(135deg, #16A085, #48C9B0);
}

.trend-indicator.cancelled {
    background: linear-gradient(135deg, #E74C3C, #EC7063);
}

.trend-indicator.average {
    background: linear-gradient(135deg, #9B59B6, #BB8FCE);
}

.trend-indicator.champion {
    background: linear-gradient(135deg, var(--jaf-gold), #F39C12);
    animation: championGlow 2s ease-in-out infinite;
}

@keyframes championGlow {
    0%, 100% {
        box-shadow: 0 0 10px rgba(255, 214, 10, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(255, 214, 10, 0.8);
    }
}

/* تأثيرات الحركة للأرقام */
.stat-card:hover .stat-number {
    transform: scale(1.05);
    color: var(--jaf-primary);
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .enhanced-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.25rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .stat-label {
        font-size: 0.9rem;
    }

    .stat-description {
        font-size: 0.8rem;
    }

    .trend-indicator {
        width: 35px;
        height: 35px;
        font-size: 0.8rem;
    }
}



/* تحسينات للأجهزة المحمولة - البطاقات الاحترافية */
@media (max-width: 768px) {
    .professional-stats-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0.5rem;
    }

    .professional-stat-card {
        min-height: 240px;
    }

    .card-header-section {
        padding: 1.25rem 1.25rem 0;
    }

    .stat-icon {
        width: 56px;
        height: 56px;
        font-size: 1.5rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .stat-title {
        font-size: 1rem;
    }

    .stat-subtitle {
        font-size: 0.85rem;
    }
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    align-items: center;
}

.control-group label {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--jaf-primary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.control-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.control-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 10px;
    font-weight: 500;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    color: white;
    min-width: 100px;
    justify-content: center;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    color: white;
}

.control-btn i {
    font-size: 0.9rem;
}

/* ألوان أزرار التحكم */
.control-btn.primary {
    background: linear-gradient(135deg, var(--jaf-primary), var(--jaf-secondary));
}

.control-btn.primary:hover {
    box-shadow: 0 4px 12px rgba(27, 67, 50, 0.3);
}

.control-btn.info {
    background: linear-gradient(135deg, #3498DB, #2980B9);
}

.control-btn.info:hover {
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.control-btn.success {
    background: linear-gradient(135deg, #27AE60, #2ECC71);
}

.control-btn.success:hover {
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.control-btn.secondary {
    background: linear-gradient(135deg, #6C757D, #495057);
}

.control-btn.secondary:hover {
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.control-btn.warning {
    background: linear-gradient(135deg, var(--jaf-warning), #E67E22);
}

.control-btn.warning:hover {
    box-shadow: 0 4px 12px rgba(247, 127, 0, 0.3);
}

.control-btn.dark {
    background: linear-gradient(135deg, #2C3E50, #34495E);
}

.control-btn.dark:hover {
    box-shadow: 0 4px 12px rgba(44, 62, 80, 0.3);
}

/* تحسينات للأجهزة المحمولة - الرسوم البيانية */
@media (max-width: 768px) {
    .enhanced-section-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .enhanced-chart-tabs {
        grid-template-columns: 1fr;
    }

    .enhanced-chart-tab {
        padding: 0.75rem;
    }

    .tab-icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .tab-title {
        font-size: 0.9rem;
    }

    .tab-description {
        font-size: 0.8rem;
    }

    .chart-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .chart-content {
        padding: 1rem;
    }

    .chart-controls {
        flex-direction: column;
        gap: 1rem;
    }

    .control-group {
        width: 100%;
    }

    .control-buttons {
        justify-content: center;
    }

    .control-btn {
        min-width: 80px;
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }
}
