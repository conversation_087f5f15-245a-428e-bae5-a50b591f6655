#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
إضافة بيانات تجريبية لاختبار التقارير
"""

import sqlite3
import os
from datetime import date

def create_test_data():
    # إنشاء قاعدة البيانات
    db_path = 'instance/jaf_meetings.db'
    os.makedirs('instance', exist_ok=True)

    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # إنشاء جدول المستخدمين
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS user (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username VARCHAR(80) UNIQUE NOT NULL,
        email VARCHAR(120) UNIQUE NOT NULL,
        password_hash VARCHAR(128),
        full_name VARCHAR(100),
        is_admin BOOLEAN DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # إنشاء جدول الاجتماعات
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS meeting (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title VARCHAR(200) NOT NULL,
        subject VARCHAR(200),
        meeting_type VARCHAR(100),
        meeting_date DATE NOT NULL,
        meeting_time TIME,
        location VARCHAR(200),
        inviting_party VARCHAR(200),
        book_number VARCHAR(100),
        book_date DATE,
        dress_code VARCHAR(100),
        notes TEXT,
        is_cancelled BOOLEAN DEFAULT 0,
        is_postponed BOOLEAN DEFAULT 0,
        creator_id INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # حذف البيانات الموجودة
    cursor.execute('DELETE FROM meeting')
    cursor.execute('DELETE FROM user')

    # إضافة مستخدم admin
    cursor.execute('''
    INSERT INTO user (username, email, password_hash, full_name, is_admin)
    VALUES ('admin', '<EMAIL>', 'pbkdf2:sha256:600000$salt$hash', 'مدير النظام', 1)
    ''')

    # إضافة اجتماع 2024
    cursor.execute('''
    INSERT INTO meeting (title, subject, meeting_type, meeting_date, meeting_time, location, inviting_party, book_number, book_date, dress_code, notes, is_cancelled, is_postponed, creator_id)
    VALUES ('اجتماع مجلس الإدارة - مارس 2024', 'اجتماع مجلس الإدارة الشهري', 'اجتماع إداري', '2024-03-15', '10:30', 'قاعة الاجتماعات الرئيسية', 'مجلس الإدارة', '001/2024/م.د', '2024-03-10', 'الزي الرسمي', 'اجتماع دوري لمناقشة الأمور الإدارية', 0, 0, 1)
    ''')

    # إضافة اجتماعات 2025
    meetings_2025 = [
        ('اجتماع يناير 2025', '2025-01-10', 0),
        ('اجتماع فبراير 2025', '2025-02-15', 0),
        ('اجتماع مارس 2025', '2025-03-20', 1),  # ملغي
        ('اجتماع أبريل 2025', '2025-04-12', 0),
        ('اجتماع مايو 2025', '2025-05-18', 0),
        ('اجتماع يونيو 2025', '2025-06-25', 0),
        ('اجتماع يوليو 2025', '2025-07-08', 1),   # ملغي
        ('اجتماع أغسطس 2025', '2025-08-14', 0),
        ('اجتماع سبتمبر 2025', '2025-09-22', 0),
        ('اجتماع أكتوبر 2025', '2025-10-30', 0)
    ]

    for i, (title, meeting_date, is_cancelled) in enumerate(meetings_2025, 1):
        cursor.execute('''
        INSERT INTO meeting (title, subject, meeting_type, meeting_date, meeting_time, location, inviting_party, book_number, book_date, dress_code, notes, is_cancelled, is_postponed, creator_id)
        VALUES (?, ?, 'اجتماع إداري', ?, '14:00', 'قاعة المؤتمرات', 'الإدارة العامة', ?, ?, 'الزي الرسمي', ?, ?, 0, 1)
        ''', (title, f'اجتماع شهري رقم {i}', meeting_date, f'{i:03d}/2025/م.د', meeting_date, f'اجتماع شهري لسنة 2025', is_cancelled))

    conn.commit()
    
    # التحقق من البيانات
    cursor.execute('SELECT COUNT(*) FROM meeting WHERE meeting_date LIKE "2024%"')
    count_2024 = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM meeting WHERE meeting_date LIKE "2025%"')
    count_2025 = cursor.fetchone()[0]
    
    conn.close()

    print('✅ تم إنشاء قاعدة البيانات وإضافة البيانات التجريبية بنجاح!')
    print(f'📊 تم إضافة {count_2024} اجتماع في 2024 و {count_2025} اجتماعات في 2025')
    
    return True

if __name__ == '__main__':
    create_test_data()
