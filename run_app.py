#!/usr/bin/env python
# -*- coding: utf-8 -*-

try:
    print("🚀 بدء تشغيل نظام إدارة الاجتماعات...")
    print("📦 تحميل المكتبات...")

    import sys
    import os
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

    from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session, send_file, make_response
    from flask_sqlalchemy import SQLAlchemy
    from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user
    from werkzeug.security import generate_password_hash, check_password_hash
    from datetime import datetime, date, time, timedelta
    import urllib.parse
    import webbrowser

    print("✅ تم تحميل المكتبات الأساسية")

    # إنشاء التطبيق
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'jaf-meetings-secret-key-2024'
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///meetings.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

    db = SQLAlchemy(app)
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'login'

    print("✅ تم إنشاء التطبيق")

    # نماذج قاعدة البيانات
    class User(UserMixin, db.Model):
        id = db.Column(db.Integer, primary_key=True)
        username = db.Column(db.String(80), unique=True, nullable=False)
        email = db.Column(db.String(120), unique=True, nullable=False)
        password_hash = db.Column(db.String(120), nullable=False)
        full_name = db.Column(db.String(100), nullable=False)
        is_admin = db.Column(db.Boolean, default=False)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)

        def set_password(self, password):
            self.password_hash = generate_password_hash(password)

        def check_password(self, password):
            return check_password_hash(self.password_hash, password)

    class Meeting(db.Model):
        id = db.Column(db.Integer, primary_key=True)
        subject = db.Column(db.String(200), nullable=False)
        meeting_type = db.Column(db.String(100))
        meeting_date = db.Column(db.Date, nullable=False)
        meeting_time = db.Column(db.Time, nullable=False)
        location = db.Column(db.String(200), nullable=False)
        inviting_party = db.Column(db.String(200), nullable=False)
        dress_code = db.Column(db.String(100))
        book_number = db.Column(db.String(100))
        book_date = db.Column(db.Date)
        notes = db.Column(db.Text)
        is_cancelled = db.Column(db.Boolean, default=False)
        is_postponed = db.Column(db.Boolean, default=False)
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        updated_at = db.Column(db.DateTime, default=datetime.utcnow)
        creator_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

        creator = db.relationship('User', backref=db.backref('meetings', lazy=True))

    class NotificationSettings(db.Model):
        __tablename__ = 'notification_settings'

        id = db.Column(db.Integer, primary_key=True)
        enabled = db.Column(db.Boolean, default=False, nullable=False)
        phone_number = db.Column(db.String(20))
        api_key = db.Column(db.String(500))
        api_url = db.Column(db.String(500))
        created_at = db.Column(db.DateTime, default=datetime.utcnow)
        updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))

    print("✅ تم إنشاء النماذج")

    # دالة إرسال WhatsApp
    def send_whatsapp_instant(phone_number, message):
        try:
            print(f"⚡ إرسال فوري لـ WhatsApp...")
            print(f"📞 الرقم: {phone_number}")
            print(f"💬 الرسالة: {message[:100]}...")

            # تنظيف رقم الهاتف
            clean_phone = phone_number.replace('+', '').replace('-', '').replace(' ', '')

            # إضافة رمز الدولة إذا لم يكن موجوداً
            if not clean_phone.startswith('962'):
                if clean_phone.startswith('0'):
                    clean_phone = '962' + clean_phone[1:]
                else:
                    clean_phone = '962' + clean_phone

            # إنشاء رابط WhatsApp
            encoded_message = urllib.parse.quote(message)
            whatsapp_url = f"https://wa.me/{clean_phone}?text={encoded_message}"

            print(f"🌐 رابط WhatsApp: {whatsapp_url}")

            # فتح الرابط في المتصفح
            webbrowser.open(whatsapp_url)
            print("✅ تم فتح WhatsApp Web في المتصفح")
            return True

        except Exception as e:
            print(f"❌ خطأ في الإرسال الفوري: {str(e)}")
            return False

    def send_whatsapp_notification(meeting, action_type):
        try:
            print(f"🔍 فحص إعدادات الإشعارات...")

            # التحقق من إعدادات الإشعارات
            settings = NotificationSettings.query.first()
            if not settings or not settings.enabled:
                print("📱 نظام الإشعارات معطل")
                return False

            if not settings.phone_number:
                print("📱 رقم الواتساب غير محدد")
                return False

            print(f"✅ النظام مفعل للرقم: {settings.phone_number}")

            # تنسيق التاريخ والوقت
            meeting_date = meeting.meeting_date.strftime('%Y/%m/%d') if meeting.meeting_date else 'غير محدد'
            meeting_time = meeting.meeting_time.strftime('%H:%M') if meeting.meeting_time else 'غير محدد'

            # صيغ الرسائل
            messages = {
                'new': f"""🆕 *اجتماع جديد*

📋 *الموضوع:* {meeting.subject}
📍 *المكان:* {meeting.location}
📅 *التاريخ:* {meeting_date}
⏰ *الوقت:* {meeting_time}
👤 *المنظم:* {meeting.inviting_party}

---
نظام إدارة الاجتماعات
القوات المسلحة الأردنية""",

                'updated': f"""✏️ *تم تعديل الاجتماع*

📋 *الموضوع:* {meeting.subject}
📍 *المكان:* {meeting.location}
📅 *التاريخ:* {meeting_date}
⏰ *الوقت:* {meeting_time}
👤 *المنظم:* {meeting.inviting_party}

🔄 *تم تحديث تفاصيل هذا الاجتماع*

---
نظام إدارة الاجتماعات
القوات المسلحة الأردنية""",

                'cancelled': f"""❌ *تم إلغاء الاجتماع*

📋 *الموضوع:* {meeting.subject}
📍 *المكان:* {meeting.location}
📅 *التاريخ:* {meeting_date}
⏰ *الوقت:* {meeting_time}

⚠️ *تم إلغاء هذا الاجتماع*

---
نظام إدارة الاجتماعات
القوات المسلحة الأردنية""",

                'postponed': f"""⏰ *تم تأجيل الاجتماع*

📋 *الموضوع:* {meeting.subject}
📍 *المكان:* {meeting.location}
📅 *التاريخ الجديد:* {meeting_date}
⏰ *الوقت الجديد:* {meeting_time}

🔄 *تم تأجيل هذا الاجتماع إلى موعد جديد*

---
نظام إدارة الاجتماعات
القوات المسلحة الأردنية"""
            }

            message = messages.get(action_type, f"📋 تحديث على الاجتماع: {meeting.subject}")

            print(f"📤 محاولة إرسال إشعار WhatsApp نوع: {action_type}")
            print(f"📱 الرقم: {settings.phone_number}")

            # إرسال الرسالة
            success = send_whatsapp_instant(settings.phone_number, message)

            if success:
                print(f"✅ تم إرسال إشعار WhatsApp بنجاح: {action_type}")
            else:
                print(f"❌ فشل في إرسال إشعار WhatsApp: {action_type}")

            return success

        except Exception as e:
            print(f"خطأ في إرسال إشعار WhatsApp: {str(e)}")
            return False

    print("✅ تم إنشاء دوال WhatsApp")

    if __name__ == '__main__':
        with app.app_context():
            db.create_all()

            # إنشاء مستخدم افتراضي
            if not User.query.filter_by(username='admin').first():
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    full_name='مدير النظام',
                    is_admin=True
                )
                admin.set_password('admin')
                db.session.add(admin)
                db.session.commit()
                print("✅ تم إنشاء المستخدم الافتراضي: admin/admin")

            # إنشاء إعدادات الإشعارات
            settings = NotificationSettings.query.first()
            if not settings:
                settings = NotificationSettings(
                    enabled=True,
                    phone_number='+9620782146467',
                    api_key='',
                    api_url=''
                )
                db.session.add(settings)
                db.session.commit()
                print("✅ تم إنشاء إعدادات الإشعارات - مفعل: True, رقم: +9620782146467")
            else:
                settings.enabled = True
                settings.phone_number = '+9620782146467'
                db.session.commit()
                print("✅ تم تحديث إعدادات الإشعارات - مفعل: True, رقم: +9620782146467")

        print("🚀 تشغيل نظام إدارة الاجتماعات مع إشعارات WhatsApp...")
        print("📍 الرابط: http://127.0.0.1:5000")
        print("🔑 المستخدم: admin | كلمة المرور: admin")
        print("📱 نظام WhatsApp مفعل للرقم: +9620782146467")
        print("=" * 60)

        # تشغيل التطبيق
        app.run(host='127.0.0.1', port=5000, debug=True, use_reloader=False)

except Exception as e:
    print(f"❌ خطأ في تشغيل التطبيق: {str(e)}")
    import traceback
    traceback.print_exc()
