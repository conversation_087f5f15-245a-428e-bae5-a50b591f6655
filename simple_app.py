#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
تطبيق مبسط لاختبار إشعارات WhatsApp
"""

from flask import Flask, request, jsonify, render_template_string
import urllib.parse
import webbrowser
from datetime import datetime

app = Flask(__name__)

# دالة إرسال WhatsApp
def send_whatsapp_instant(phone_number, message):
    try:
        print(f"⚡ إرسال فوري لـ WhatsApp...")
        print(f"📞 الرقم: {phone_number}")
        print(f"💬 الرسالة: {message[:100]}...")
        
        # تنظيف رقم الهاتف
        clean_phone = phone_number.replace('+', '').replace('-', '').replace(' ', '')
        
        # إضافة رمز الدولة إذا لم يكن موجوداً
        if not clean_phone.startswith('962'):
            if clean_phone.startswith('0'):
                clean_phone = '962' + clean_phone[1:]
            else:
                clean_phone = '962' + clean_phone
        
        # إنشاء رابط WhatsApp
        encoded_message = urllib.parse.quote(message)
        whatsapp_url = f"https://wa.me/{clean_phone}?text={encoded_message}"
        
        print(f"🌐 رابط WhatsApp: {whatsapp_url}")
        
        # فتح الرابط في المتصفح
        webbrowser.open(whatsapp_url)
        print("✅ تم فتح WhatsApp Web في المتصفح")
        return True
            
    except Exception as e:
        print(f"❌ خطأ في الإرسال الفوري: {str(e)}")
        return False

# الصفحة الرئيسية
@app.route('/')
def home():
    html = """
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>اختبار إشعارات WhatsApp</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
            .whatsapp-btn { background: #25D366; border: none; color: white; }
            .whatsapp-btn:hover { background: #128C7E; color: white; }
        </style>
    </head>
    <body class="bg-light">
        <div class="container mt-5">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card shadow">
                        <div class="card-header bg-success text-white text-center">
                            <h2><i class="fab fa-whatsapp me-2"></i>اختبار إشعارات WhatsApp</h2>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>الرقم المستهدف:</strong> 0782146467
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <button class="btn whatsapp-btn w-100 btn-lg" onclick="testNewMeeting()">
                                        <i class="fas fa-plus me-2"></i>
                                        اختبار اجتماع جديد
                                    </button>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <button class="btn btn-warning w-100 btn-lg" onclick="testPostpone()">
                                        <i class="fas fa-clock me-2"></i>
                                        اختبار تأجيل اجتماع
                                    </button>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <button class="btn btn-danger w-100 btn-lg" onclick="testCancel()">
                                        <i class="fas fa-times me-2"></i>
                                        اختبار إلغاء اجتماع
                                    </button>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <button class="btn btn-primary w-100 btn-lg" onclick="testUpdate()">
                                        <i class="fas fa-edit me-2"></i>
                                        اختبار تعديل اجتماع
                                    </button>
                                </div>
                            </div>
                            
                            <div id="result" class="mt-4"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
        async function sendTest(type) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...</div>';
            
            try {
                const response = await fetch('/test-notification', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ type: type })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `<div class="alert alert-success"><i class="fas fa-check me-2"></i>${result.message}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-times me-2"></i>${result.error}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-times me-2"></i>خطأ في الإرسال: ${error}</div>`;
            }
        }
        
        function testNewMeeting() { sendTest('new'); }
        function testPostpone() { sendTest('postponed'); }
        function testCancel() { sendTest('cancelled'); }
        function testUpdate() { sendTest('updated'); }
        </script>
    </body>
    </html>
    """
    return html

# API لاختبار الإشعارات
@app.route('/test-notification', methods=['POST'])
def test_notification():
    try:
        data = request.get_json()
        notification_type = data.get('type', 'new')
        
        print(f"🧪 اختبار إشعار نوع: {notification_type}")
        
        # بيانات اجتماع وهمي
        meeting_data = {
            'subject': 'اجتماع اختبار النظام',
            'location': 'قاعة الاجتماعات الرئيسية',
            'meeting_date': datetime.now().strftime('%Y/%m/%d'),
            'meeting_time': datetime.now().strftime('%H:%M'),
            'inviting_party': 'مديرية الدائرة المالية'
        }
        
        # صيغ الرسائل
        messages = {
            'new': f"""🆕 *اجتماع جديد*
            
📋 *الموضوع:* {meeting_data['subject']}
📍 *المكان:* {meeting_data['location']}
📅 *التاريخ:* {meeting_data['meeting_date']}
⏰ *الوقت:* {meeting_data['meeting_time']}
👤 *المنظم:* {meeting_data['inviting_party']}

---
نظام إدارة الاجتماعات
القوات المسلحة الأردنية""",

            'updated': f"""✏️ *تم تعديل الاجتماع*
            
📋 *الموضوع:* {meeting_data['subject']}
📍 *المكان:* {meeting_data['location']}
📅 *التاريخ:* {meeting_data['meeting_date']}
⏰ *الوقت:* {meeting_data['meeting_time']}
👤 *المنظم:* {meeting_data['inviting_party']}

🔄 *تم تحديث تفاصيل هذا الاجتماع*

---
نظام إدارة الاجتماعات
القوات المسلحة الأردنية""",

            'cancelled': f"""❌ *تم إلغاء الاجتماع*
            
📋 *الموضوع:* {meeting_data['subject']}
📍 *المكان:* {meeting_data['location']}
📅 *التاريخ:* {meeting_data['meeting_date']}
⏰ *الوقت:* {meeting_data['meeting_time']}

⚠️ *تم إلغاء هذا الاجتماع*

---
نظام إدارة الاجتماعات
القوات المسلحة الأردنية""",

            'postponed': f"""⏰ *تم تأجيل الاجتماع*
            
📋 *الموضوع:* {meeting_data['subject']}
📍 *المكان:* {meeting_data['location']}
📅 *التاريخ الجديد:* {meeting_data['meeting_date']}
⏰ *الوقت الجديد:* {meeting_data['meeting_time']}

🔄 *تم تأجيل هذا الاجتماع إلى موعد جديد*

---
نظام إدارة الاجتماعات
القوات المسلحة الأردنية"""
        }
        
        message = messages.get(notification_type, messages['new'])
        
        # إرسال الرسالة
        success = send_whatsapp_instant('+9620782146467', message)
        
        if success:
            return jsonify({
                'success': True,
                'message': f'تم إرسال إشعار {notification_type} بنجاح! تحقق من WhatsApp'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'فشل في إرسال الإشعار'
            })
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الإشعار: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    print("🚀 تشغيل تطبيق اختبار إشعارات WhatsApp...")
    print("📍 الرابط: http://127.0.0.1:5000")
    print("📱 الرقم المستهدف: 0782146467")
    print("=" * 60)
    
    app.run(host='127.0.0.1', port=5000, debug=True)
