// -*- coding: utf-8 -*-
/**
 * الملف الرئيسي للـ JavaScript
 * نظام إدارة المواعيد - القوات المسلحة الأردنية
 */

// إعداد Moment.js للغة العربية
moment.locale('ar');

// متغيرات عامة
const JAF_COLORS = {
    primary: '#1B4332',
    secondary: '#2D6A4F',
    success: '#40916C',
    info: '#52B788',
    warning: '#F77F00',
    danger: '#D62828',
    gold: '#FFD60A'
};

// وظائف مساعدة للتاريخ والوقت
const DateTimeUtils = {
    // تحويل التاريخ إلى تنسيق عربي
    formatArabicDate: function(date) {
        return moment(date).format('dddd، DD MMMM YYYY');
    },
    
    // تحويل الوقت إلى تنسيق عسكري (بدون نقطتين)
    formatMilitaryTime: function(time) {
        return moment(time, 'HH:mm').format('HHmm');
    },
    
    // حساب الوقت المتبقي
    getTimeRemaining: function(targetDate) {
        const now = moment();
        const target = moment(targetDate);
        const duration = moment.duration(target.diff(now));
        
        if (duration.asMilliseconds() < 0) {
            return 'انتهى الوقت';
        }
        
        const days = Math.floor(duration.asDays());
        const hours = duration.hours();
        const minutes = duration.minutes();
        
        if (days > 0) {
            return `<span dir="rtl">${days} يوم و ${hours} ساعة</span>`;
        } else if (hours > 0) {
            return `<span dir="rtl">${hours} ساعة و ${minutes} دقيقة</span>`;
        } else {
            return `<span dir="rtl">${minutes} دقيقة</span>`;
        }
    },
    
    // التحقق من تضارب المواعيد
    checkTimeConflict: function(newDateTime, existingMeetings) {
        const newTime = moment(newDateTime);
        
        for (let meeting of existingMeetings) {
            const meetingTime = moment(meeting.datetime);
            const timeDiff = Math.abs(newTime.diff(meetingTime, 'minutes'));
            
            if (timeDiff < 30) { // أقل من 30 دقيقة
                return {
                    hasConflict: true,
                    conflictingMeeting: meeting
                };
            }
        }
        
        return { hasConflict: false };
    }
};

// وظائف التحقق من صحة البيانات
const ValidationUtils = {
    // التحقق من رقم الكتاب
    validateBookNumber: function(bookNumber) {
        const pattern = /^[0-9]+$/;
        return pattern.test(bookNumber) && parseInt(bookNumber) > 0 && parseInt(bookNumber) <= 999999;
    },
    
    // التحقق من رقم الهاتف
    validatePhoneNumber: function(phone) {
        const pattern = /^(\+962|0)?[0-9]{9}$/;
        return pattern.test(phone.replace(/\s/g, ''));
    },
    
    // التحقق من البريد الإلكتروني
    validateEmail: function(email) {
        const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return pattern.test(email);
    },
    
    // التحقق من صيغة الملف
    validateFileType: function(fileName, allowedTypes) {
        const extension = fileName.split('.').pop().toLowerCase();
        return allowedTypes.includes(extension);
    }
};

// وظائف الإشعارات - استخدام النظام الموحد
const NotificationUtils = {
    // عرض إشعار نجاح
    showSuccess: function(message, duration = 3000) {
        if (window.UnifiedNotifications) {
            window.UnifiedNotifications.showSuccess(message, duration);
        } else {
            console.log('نجاح:', message);
            alert(message);
        }
    },

    // عرض إشعار خطأ
    showError: function(message, duration = 4000) {
        if (window.UnifiedNotifications) {
            window.UnifiedNotifications.showError(message, duration);
        } else {
            console.log('خطأ:', message);
            alert(message);
        }
    },

    // عرض إشعار معلومات
    showInfo: function(message, duration = 3000) {
        if (window.UnifiedNotifications) {
            window.UnifiedNotifications.showInfo(message, duration);
        } else {
            console.log('معلومات:', message);
            alert(message);
        }
    },

    // عرض إشعار تحذير
    showWarning: function(message, duration = 3500) {
        if (window.UnifiedNotifications) {
            window.UnifiedNotifications.showWarning(message, duration);
        } else {
            console.log('تحذير:', message);
            alert(message);
        }
    },
    
    // وظيفة عامة لعرض الإشعارات - استخدام النظام الموحد
    showNotification: function(message, type, duration) {
        if (window.UnifiedNotifications) {
            // تحويل أنواع الإشعارات
            const unifiedType = type === 'danger' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            window.UnifiedNotifications.showNotification(message, unifiedType, duration);
        } else {
            console.log('إشعار:', message, type);
            alert(message);
        }

        // إزالة الإشعار عند النقر على زر الإغلاق
        const closeBtn = alertDiv.querySelector('.btn-close');
        closeBtn.addEventListener('click', () => {
            alertDiv.style.opacity = '0';
            alertDiv.style.transform = 'translateX(-100%)';
            alertDiv.style.transition = 'all 0.3s ease-out';
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 300);
        });
    },
    
    // الحصول على الأيقونة المناسبة
    getIconForType: function(type) {
        const icons = {
            success: 'check-circle',
            danger: 'exclamation-triangle',
            warning: 'exclamation-circle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
};

// وظائف AJAX
const AjaxUtils = {
    // إرسال طلب GET
    get: function(url, successCallback, errorCallback) {
        fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (successCallback) successCallback(data);
        })
        .catch(error => {
            console.error('Error:', error);
            if (errorCallback) errorCallback(error);
        });
    },
    
    // إرسال طلب POST
    post: function(url, data, successCallback, errorCallback) {
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (successCallback) successCallback(data);
        })
        .catch(error => {
            console.error('Error:', error);
            if (errorCallback) errorCallback(error);
        });
    }
};

// وظائف التصدير
const ExportUtils = {
    // تصدير إلى Excel
    exportToExcel: function(data, filename) {
        // سيتم تنفيذها لاحقاً
        console.log('Exporting to Excel:', filename);
    },
    
    // تصدير إلى PDF
    exportToPDF: function(data, filename) {
        // سيتم تنفيذها لاحقاً
        console.log('Exporting to PDF:', filename);
    },
    
    // طباعة
    print: function(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head>
                    <title>طباعة</title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
                    <style>
                        body { font-family: 'Cairo', sans-serif; direction: rtl; }
                        @media print { .no-print { display: none !important; } }
                    </style>
                </head>
                <body>
                    ${element.innerHTML}
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }
    }
};

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // تفعيل popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // تهيئة التبويبات النشطة
    initializeActiveNavigation();

    // إضافة تأثيرات الحركة للعناصر
    const animatedElements = document.querySelectorAll('.fade-in-up');
    animatedElements.forEach((element, index) => {
        element.style.animationDelay = `${index * 0.1}s`;
    });
    
    // تحديث الوقت الحالي في الشريط العلوي
    updateCurrentTime();
    setInterval(updateCurrentTime, 60000); // كل دقيقة

    // تحويل جميع الأرقام العربية إلى إنجليزية
    setTimeout(() => {
        convertAllArabicNumbersOnPage();
    }, 100);

    // تفعيل مراقبة التغييرات
    observePageChanges();

    // تحويل دوري كل ثانية للتأكد
    setInterval(() => {
        convertAllArabicNumbersOnPage();
    }, 1000);

    // معالج خاص للحقول
    setupInputHandlers();
});

// دالة تحويل الأرقام العربية للإنجليزية
function convertAllArabicNumbersOnPage() {
    const arabicDigits = '٠١٢٣٤٥٦٧٨٩';
    const englishDigits = '0123456789';

    // تحويل جميع النصوص في الصفحة
    function convertText(text) {
        let result = text;
        for (let i = 0; i < arabicDigits.length; i++) {
            result = result.replace(new RegExp(arabicDigits[i], 'g'), englishDigits[i]);
        }
        return result;
    }

    // البحث عن جميع العناصر التي تحتوي على أرقام عربية
    const walker = document.createTreeWalker(
        document.body,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );

    const textNodes = [];
    let node;
    while (node = walker.nextNode()) {
        if (/[٠-٩]/.test(node.textContent)) {
            textNodes.push(node);
        }
    }

    // تحويل الأرقام في كل عقدة نص
    textNodes.forEach(textNode => {
        textNode.textContent = convertText(textNode.textContent);
    });

    // تحويل قيم الحقول أيضاً
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        if (input.value && /[٠-٩]/.test(input.value)) {
            input.value = convertText(input.value);
        }
        if (input.placeholder && /[٠-٩]/.test(input.placeholder)) {
            input.placeholder = convertText(input.placeholder);
        }
    });

    // تحويل محتوى العناصر المحددة
    const elements = document.querySelectorAll('.arabic-date, .arabic-time, .date-display, .time-display');
    elements.forEach(element => {
        if (element.textContent && /[٠-٩]/.test(element.textContent)) {
            element.textContent = convertText(element.textContent);
        }
        if (element.innerHTML && /[٠-٩]/.test(element.innerHTML)) {
            element.innerHTML = convertText(element.innerHTML);
        }
    });

    console.log('✅ تم تحويل جميع الأرقام العربية إلى إنجليزية في الصفحة');
}

// دالة مراقبة التغييرات في الصفحة
function observePageChanges() {
    const observer = new MutationObserver(function(mutations) {
        let hasArabicNumbers = false;

        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === Node.TEXT_NODE && /[٠-٩]/.test(node.textContent)) {
                        hasArabicNumbers = true;
                    } else if (node.nodeType === Node.ELEMENT_NODE) {
                        if (node.textContent && /[٠-٩]/.test(node.textContent)) {
                            hasArabicNumbers = true;
                        }
                    }
                });
            } else if (mutation.type === 'characterData' && /[٠-٩]/.test(mutation.target.textContent)) {
                hasArabicNumbers = true;
            }
        });

        if (hasArabicNumbers) {
            setTimeout(convertAllArabicNumbersOnPage, 100);
        }
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true,
        characterData: true
    });

    console.log('✅ تم تفعيل مراقبة التغييرات للأرقام العربية');
}

// دالة إعداد معالجات الحقول
function setupInputHandlers() {
    const arabicDigits = '٠١٢٣٤٥٦٧٨٩';
    const englishDigits = '0123456789';

    function convertNumbers(text) {
        let result = text;
        for (let i = 0; i < arabicDigits.length; i++) {
            result = result.replace(new RegExp(arabicDigits[i], 'g'), englishDigits[i]);
        }
        return result;
    }

    // معالج للكتابة
    document.addEventListener('input', function(e) {
        if (e.target.matches('input, textarea, select')) {
            if (e.target.value && /[٠-٩]/.test(e.target.value)) {
                const cursorPos = e.target.selectionStart;
                e.target.value = convertNumbers(e.target.value);
                e.target.setSelectionRange(cursorPos, cursorPos);
            }
        }
    });

    // معالج للصق
    document.addEventListener('paste', function(e) {
        if (e.target.matches('input, textarea, select')) {
            setTimeout(() => {
                if (e.target.value && /[٠-٩]/.test(e.target.value)) {
                    e.target.value = convertNumbers(e.target.value);
                }
            }, 10);
        }
    });

    // معالج للتركيز
    document.addEventListener('focus', function(e) {
        if (e.target.matches('input, textarea, select')) {
            if (e.target.value && /[٠-٩]/.test(e.target.value)) {
                e.target.value = convertNumbers(e.target.value);
            }
        }
    }, true);

    console.log('✅ تم إعداد معالجات الحقول للأرقام العربية');
}

// تحديث الوقت الحالي
function updateCurrentTime() {
    const timeElements = document.querySelectorAll('.current-time');
    const now = moment();
    const timeString = now.format('dddd، DD MMMM YYYY - HH:mm');

    timeElements.forEach(element => {
        element.textContent = timeString;
    });
}

// تصدير الوظائف للاستخدام العام
window.DateTimeUtils = DateTimeUtils;
window.ValidationUtils = ValidationUtils;
window.NotificationUtils = NotificationUtils;
window.AjaxUtils = AjaxUtils;
window.ExportUtils = ExportUtils;

// ===== نظام التبويبات النشطة =====

// تهيئة التبويبات النشطة
function initializeActiveNavigation() {
    console.log('🔗 تهيئة نظام التبويبات النشطة...');

    // الحصول على المسار الحالي
    const currentPath = window.location.pathname;
    console.log('📍 المسار الحالي:', currentPath);

    // خريطة المسارات والتبويبات
    const pathToNavMap = {
        '/': 'meetings_simple',
        '/meetings_simple': 'meetings_simple',
        '/all_meetings': 'meetings_simple',
        '/add_meeting': 'add_meeting',
        '/edit_meetings': 'edit_meetings',
        '/search': 'search',
        '/search_new': 'search',
        '/reports': 'reports',
        '/notifications_settings': 'notifications_settings'
    };

    // تحديد التبويبة النشطة
    const activeTab = pathToNavMap[currentPath] || 'meetings_simple';
    console.log('🎯 التبويبة النشطة:', activeTab);

    // إزالة الكلاس النشط من جميع التبويبات
    const allNavLinks = document.querySelectorAll('.navbar-nav .nav-link');
    allNavLinks.forEach(link => {
        link.classList.remove('current-page', 'active');
    });

    // إضافة الكلاس النشط للتبويبة الحالية
    const activeNavLink = document.querySelector(`a[href*="${activeTab}"]`);
    if (activeNavLink) {
        activeNavLink.classList.add('current-page');
        console.log('✅ تم تفعيل التبويبة:', activeNavLink.textContent.trim());
    }

    // إضافة مستمعي الأحداث للتبويبات
    allNavLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // إزالة التفعيل من جميع التبويبات
            allNavLinks.forEach(l => l.classList.remove('current-page', 'active'));

            // تفعيل التبويبة المنقورة
            this.classList.add('current-page');

            // حفظ التبويبة النشطة في localStorage
            const href = this.getAttribute('href');
            if (href) {
                localStorage.setItem('activeTab', href);
                console.log('💾 تم حفظ التبويبة النشطة:', href);
            }

            // إظهار تأثير النقر
            showTabClickEffect(this);
        });
    });

    console.log('✅ تم تهيئة نظام التبويبات النشطة بنجاح');
}

// تأثير النقر على التبويبة
function showTabClickEffect(tabElement) {
    // إنشاء تأثير موجة
    const ripple = document.createElement('div');
    ripple.style.cssText = `
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 215, 0, 0.6);
        transform: scale(0);
        animation: ripple 0.6s linear;
        pointer-events: none;
        width: 100px;
        height: 100px;
        left: 50%;
        top: 50%;
        margin-left: -50px;
        margin-top: -50px;
    `;

    // إضافة CSS للتأثير إذا لم يكن موجوداً
    if (!document.querySelector('#ripple-style')) {
        const style = document.createElement('style');
        style.id = 'ripple-style';
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // إضافة التأثير
    tabElement.style.position = 'relative';
    tabElement.style.overflow = 'hidden';
    tabElement.appendChild(ripple);

    // إزالة التأثير بعد انتهائه
    setTimeout(() => {
        if (ripple.parentNode) {
            ripple.parentNode.removeChild(ripple);
        }
    }, 600);
}
